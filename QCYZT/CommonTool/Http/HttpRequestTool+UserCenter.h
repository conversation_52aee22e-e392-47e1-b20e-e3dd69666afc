//
//  HttpRequestTool+UserCenter.h
//  QCYZT
//
//  Created by shumi on 2022/6/26.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "HttpRequestTool.h"


#define kAPI_Letter_FeedBack                   KDakaBaseUrl(@"/api/v2/letter/feedback.do")

@interface HttpRequestTool (UserCenter)

// 个人信息
+ (void)getPersonalInfoStart:(void (^)())startBlock
                     failure:(void (^)())failBlock
                     success:(requestSuccessBlock)success;

// 个人数据同步
+ (void)userSyncDataWithStart:(void (^)())startBlock
                     failure:(void (^)())failBlock
                      success:(requestSuccessBlock)success;

// 查询金币
+ (void)queryDakaCoinsStart:(void (^)())startBlock
                    failure:(void (^)())failBlock
                    success:(requestSuccessBlock)success;

// 查询金币和积分
+ (void)queryDakaCoinsAndPointsStart:(void (^)())startBlock
                             failure:(void (^)())failBlock
                             success:(requestSuccessBlock)success;

// 查询消费记录
+ (void)queryConsumePage:(NSUInteger)page
                pageSize:(NSUInteger)pageSize
                   start:(void (^)())startBlock
                 failure:(void (^)())failBlock
                 success:(requestSuccessBlock)success;

// 查询收入记录
+ (void)queryIncomePage:(NSUInteger)page
               pageSize:(NSUInteger)pageSize
                  start:(void (^)())startBlock
                failure:(void (^)())failBlock
                success:(requestSuccessBlock)success;

// 获取我的经常访问
+ (void)getUserCenterOffenVisitRequestStart:(void (^)())startBlock
                                    failure:(void (^)())failBlock
                                    success:(requestSuccessBlock)success;


#pragma mark - 私信
// 私信投顾主页
+ (void)requestPrivateLetterHomeWithBigcastId:(NSString *)bigcastId
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                      success:(requestSuccessBlock)success;

// 发送私信
+ (void)requestPrivateLetterSendWithBigcastId:(NSString *)bigcastId
                                      orderId:(NSString *)orderId
                                      content:(NSString *)content
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                      success:(requestSuccessBlock)success;

// 私信消息列表
+ (void)requestPrivateLetterListWithBigcastId:(NSString *)bigcastId
                                     pageSize:(NSInteger)pageSize
                                lastMessageId:(NSString *)lastMessageId
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                      success:(requestSuccessBlock)success;

// 私信更新已读
+ (void)requestPrivateLetterRefreshReadWithBigcastId:(NSString *)bigcastId
                                           messageId:(NSString *)messageId
                                               start:(void (^)())startBlock
                                             failure:(void (^)())failBlock
                                             success:(requestSuccessBlock)success;

#pragma mark - 投顾私信
// 订单列表
+ (void)requestDakaPrivateLetterOrderListWithStart:(void (^)())startBlock
                                           failure:(void (^)())failBlock
                                           success:(requestSuccessBlock)success;

// 消息记录
+ (void)requestDakaPrivateLetterRecordListWithOrderId:(NSString *)orderId
                                                start:(void (^)())startBlock
                                              failure:(void (^)())failBlock
                                              success:(requestSuccessBlock)success;

// 关闭私信
+ (void)requestDakaPrivateLetterStopWithOrderId:(NSString *)orderId
                                          start:(void (^)())startBlock
                                        failure:(void (^)())failBlock
                                        success:(requestSuccessBlock)success;

// 发送私信
+ (void)requestDakaPrivateLetterSendWithOrderId:(NSString *)orderId
                                        content:(NSString *)content
                                    contentType:(NSString *)contentType
                                          start:(void (^)())startBlock
                                        failure:(void (^)())failBlock
                                        success:(requestSuccessBlock)success;

#pragma mark -- 卡券列表
+ (void)requestCouponListWithStatus:(NSInteger )status
                               Page:(NSInteger)page
                           pageSize:(NSInteger)pageSize
                              start:(void (^)())startBlock
                            failure:(void (^)())failBlock
                            success:(requestSuccessBlock)success;

/// 支付时选择卡券列表
/// - Parameters:
///   - bignameId: 投顾id
///   - consumeType: 1 笔记 2 问股 3 私信(Integer)
///   - contentId: 内容id
///   - startBlock: ---
///   - failBlock: ---
///   - success: ---
+ (void)requestPaymentCouponListWithBignameId:(NSString *)bignameId
                                  consumeType:(NSInteger)consumeType
                                    contentId:(NSInteger)contentId
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                      success:(requestSuccessBlock)success;

// 获取赠送给新手的卡券
+ (void)getBeginnerUserCouponStart:(void (^)())startBlock
                           failure:(void (^)())failBlock
                           success:(requestSuccessBlock)success;

// 问股评分 意见反馈
+ (void)letterFeedBackWithQuestionId:(NSInteger)letterId
                           StarLevel:(NSInteger)starLevel
                            feedback:(NSString *)feedback
                               start:(void (^)())startBlock
                             failure:(void (^)())failBlock
                             success:(requestSuccessBlock)success;

@end

