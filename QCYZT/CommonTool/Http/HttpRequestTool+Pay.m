//
//  HttpRequestTool+Pay.m
//  QCYZT
//
//  Created by shumi on 2022/8/31.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "HttpRequestTool+Pay.h"
/// 支付笔记
#define kAPI_Note_Pay           KDakaBaseUrl(@"/api/v2/business/pay/note")
/// 支付 系列课程
#define kAPI_CourseAlbum_Pay    KDakaBaseUrl(@"/api/v2/business/pay/courseSeries")
/// 支付 单课程
#define kAPI_CourseSingle_Pay    KDakaBaseUrl(@"/api/v2/business/pay/course")
/// 笔记打赏支付
#define kAPI_Note_ReWard_Pay    KDakaBaseUrl(@"/api/v2/business/pay/reward")
/// 支付问股
#define kAPI_Qusetion_Pay       KDakaBaseUrl(@"/api/v2/business/pay/listenQuestion")
/// 合规确认书
#define kAPI_Pay_ConfirmOrder   KDakaBaseUrl(@"/api/v2/business/pay/confirmingOrder.do")
#define kAPI_Pay_ConfirmOrder2   KDakaBaseUrl(@"/api/business/pay/confirmingOrderV2.do")
/// 随机立减配置
#define kAPI_Pay_RandDiscount   KDakaBaseUrl(@"/api/v2/business/pay/randomDiscount")
/// 私信支付
#define kAPI_PrivateLetter_Pay  KDakaBaseUrl(@"/api/v2/business/pay/payPrivateLetter")
/// 训练营支付
#define kAPI_TrainingCamp_Pay   KDakaBaseUrl(@"/api/v2/business/pay/bookSeries")

// 直播室门票支付接口
#define kAPI_Video2_payTickets              KDakaBaseUrl(@"/api/v2/business/pay/liveroomTicket")
// 直播室礼物打赏
#define kAPI_Gift_Pay              KDakaBaseUrl(@"/api/v2/business/pay/gift")
// 直播私密消息付费
#define kAPI_Daka_Live_MessagePay      KDakaBaseUrl(@"/api/v2/business/pay/liveroomMessage")

// 指标支付
#define kAPI_Index_Pay    KDakaBaseUrl(@"/api/v2/business/pay/index")

// 策略支付
#define kAPI_Strategy_Pay    KDakaBaseUrl(@"/api/v2/business/pay/strategy")

// 解套宝支付
#define kAPI_UnlockPosition_Pay    KDakaBaseUrl(@"/api/v2/business/pay/payValueAdd")


@implementation HttpRequestTool (Pay)

///  支付笔记
/// - Parameters:
///   - noteId: 笔记id
///   - type:  1 金币 其余传卡券类型 3 通用券 4 专用券
///   - goodsId: 券id
///   - usePoints: 使用的积分数
///   - startBlock: 开始回调
///   - failBlock: 失败回调
///   - success: 成功回调
+(void)payNoteWithNoteId:(NSString *)noteId
                    type:(NSString *)type
                 goodsId:(NSString *)goodsId
               usePoints:(NSInteger)usePoints
                   start:(void (^)())startBlock
                 failure:(void (^)())failBlock
                 success:(requestSuccessBlock)success{
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (noteId) {
        [params setObject:noteId forKey:@"noteid"];
    }
    if (type.length > 0) {
        [params setObject:type forKey:@"type"];
    }
    if (goodsId.length > 0) {
        [params setObject:goodsId forKey:@"goodsId"];
    }
    if (usePoints > 0) {
        [params setObject:@(1) forKey:@"usePoints"];
    }
    [self getDataInfoWithUrl:kAPI_Note_Pay
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

/// 支付课程系列
/// - Parameters:
///   - albumId: 课程id
///   - type: 1 金币 其余传卡券类型 3 通用券 4 专用券
///   - goodsId: 券id
///   - usePoints: 使用的积分数
///   - startBlock: 开始回调
///   - failBlock: 失败回调
///   - success: 成功回调
+(void)payCourseAlbumWithAlbumId:(NSString *)albumId
                            type:(NSString *)type
                         goodsId:(NSString *)goodsId
                       usePoints:(NSInteger)usePoints
                           start:(void (^)())startBlock
                         failure:(void (^)())failBlock
                         success:(requestSuccessBlock)success{
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (albumId) {
        [params setObject:albumId forKey:@"id"];
    }
    if (type.length > 0) {
        [params setObject:type forKey:@"type"];
    }
    if (goodsId.length > 0) {
        [params setObject:goodsId forKey:@"goodsId"];
    }
    if (usePoints > 0) {
        [params setObject:@(1) forKey:@"usePoints"];
    }
    [self getDataInfoWithUrl:kAPI_CourseAlbum_Pay
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

/// 单课程支付
/// - Parameters:
///   - courseId: 课程id
///   - type: 1 金币 其余传卡券类型 3 通用券 4 专用券
///   - goodsId: 券id
///   - usePoints: 使用的积分数
///   - startBlock: 开始回调
///   - failBlock: 失败回调
///   - success: 成功回调
+(void)payCourseSingleWithCourseId:(NSString *)courseId
                              type:(NSString *)type
                           goodsId:(NSString *)goodsId
                         usePoints:(NSInteger)usePoints
                             start:(void (^)())startBlock
                           failure:(void (^)())failBlock
                           success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (courseId) {
        [params setObject:courseId forKey:@"id"];
    }
    if (type.length > 0) {
        [params setObject:type forKey:@"type"];
    }
    if (goodsId.length > 0) {
        [params setObject:goodsId forKey:@"goodsId"];
    }
    if (usePoints > 0) {
        [params setObject:@(1) forKey:@"usePoints"];
    }
    [self getDataInfoWithUrl:kAPI_CourseSingle_Pay
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];}


/// 支付问股
/// - Parameters:
///   - questionid: 问股id
///   - type: 1 金币 其余传卡券类型 3 通用券 4 专用券
///   - goodsId: 券id
///   - usePoints: 使用的积分数
///   - startBlock: 开始回调
///   - failBlock: 失败回调
///   - success: 成功回调
+(void)payQuestionWithQuestionid:(NSString *)questionid
                            type:(NSString *)type
                        couponId:(NSString *)couponId
                       usePoints:(NSInteger)usePoints
                           start:(void (^)())startBlock
                         failure:(void (^)())failBlock
                         success:(requestSuccessBlock)success{
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (questionid) {
        [params setObject:questionid forKey:@"questionid"];
    }
    if (type.length > 0) {
        [params setObject:type forKey:@"type"];
    }
    if (couponId.length > 0) {
        [params setObject:couponId forKey:@"goodsId"];
    }
    if (usePoints > 0) {
        [params setObject:@(1) forKey:@"usePoints"];
    }
    [self getDataInfoWithUrl:kAPI_Qusetion_Pay
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 支付确认书
+(void)payConfirmOrderWithBigNameId:(NSString *)bigNameId
                              start:(void (^)())startBlock
                            failure:(void (^)())failBlock
                            success:(requestSuccessBlock)success{
    if (bigNameId.length) {
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        if (bigNameId) {
            [params setObject:bigNameId forKey:@"bigNameId"];
        }
        
        [self getDataInfoWithUrl:kAPI_Pay_ConfirmOrder
                          params:params
                           start:startBlock
                         failure:failBlock
                         success:success];
    } else {
        [self getDataInfoWithUrl:kAPI_Pay_ConfirmOrder2
                          params:nil
                           start:startBlock
                         failure:failBlock
                         success:success];
    }
}


// 获取随机立减配置
+(void)getRandomDiscountStart:(void (^)())startBlock
                      failure:(void (^)())failBlock
                      success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_Pay_RandDiscount params:nil start:startBlock failure:failBlock success:success];
}

// 私信支付
+ (void)requestPrivateLetterPayWithBigcastId:(NSString *)bigcastId
                                        type:(NSString *)type
                                     goodsId:(NSString *)goodsId
                                   usePoints:(NSInteger)usePoints
                                       start:(void (^)())startBlock
                                     failure:(void (^)())failBlock
                                     success:(requestSuccessBlock)success {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    if (bigcastId) {
        [param setObject:bigcastId forKey:@"bignameId"];
    }
    if (type.length > 0) {
        [param setObject:type forKey:@"type"];
    }
    if (goodsId.length > 0) {
        [param setObject:goodsId forKey:@"goodsId"];
    }
    if (usePoints > 0) {
        [param setObject:@(1) forKey:@"usePoints"];
    }
    [self getDataInfoWithUrl:kAPI_PrivateLetter_Pay params:param start:startBlock failure:failBlock success:success];
}

// 训练营支付
+ (void)trainingCampPayWithBookId:(NSString *)bookId
                             type:(NSString *)type
                        usePoints:(NSInteger)usePoints
                            start:(void (^)())startBlock
                          failure:(void (^)())failBlock
                          success:(requestSuccessBlock)success{
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (bookId) {
        [params setObject:bookId forKey:@"bookid"];
    }
    if (type) {
        [params setObject:type forKey:@"type"];
    }
    if (usePoints > 0) {
        [params setObject:@(1) forKey:@"usePoints"];
    }

    [self getDataInfoWithUrl:kAPI_TrainingCamp_Pay
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}


/// 打赏支付
+ (void)payNoteRewardWithDakaId:(NSString *)bignameId
                         noteId:(NSString *)noteId
                           coin:(NSInteger)coin
                      usePoints:(NSInteger)usePoints
                          Start:(void (^)())startBlock
                        failure:(void (^)())failBlock
                        success:(requestSuccessBlock)success {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param setObject:noteId forKey:@"noteId"];
    [param setObject:bignameId forKey:@"bignameId"];
    [param setObject:[NSNumber numberWithInteger:coin] forKey:@"coin"];
    if (usePoints > 0) {
        [param setObject:@(1) forKey:@"usePoints"];
    }
    [self postDataJsonInfoWithUrl:kAPI_Note_ReWard_Pay
                           params:param
                            start:startBlock
                          failure:failBlock
                          success:success];
}

/// 直播聊天室付费消息
+ (void)liveChatRoomPayMessageWithMessageId:(NSInteger)messageId
                                     roomId:(NSInteger)roomId
                                  usePoints:(NSInteger)usePoints
                                      start:(void (^)())startBlock
                                    failure:(void (^)())failBlock
                                    success:(requestSuccessBlock)success {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    if (messageId > 0) {
        [param setObject:[NSNumber numberWithInteger:messageId] forKey:@"goodsId"];
    }
    if (roomId > 0) {
        [param setObject:[NSNumber numberWithInteger:roomId] forKey:@"roomId"];
    }
    if (usePoints > 0) {
        [param setObject:@(1) forKey:@"usePoints"];
    }
    [self getDataInfoWithUrl:kAPI_Daka_Live_MessagePay params:param start:startBlock failure:failBlock success:success];
}


/// 赠送礼物
+ (void)liveSendGiftWithRoomId:(NSInteger)roomId
                        giftId:(NSInteger)giftId
                     usePoints:(NSInteger)usePoints
                         start:(void (^)())startBlock
                       failure:(void (^)())failBlock
                       success:(requestSuccessBlock)success {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param setObject:[NSNumber numberWithInteger:roomId] forKey:@"roomId"];
    [param setObject:[NSNumber numberWithInteger:giftId] forKey:@"giftId"];
    if (usePoints > 0) {
        [param setObject:@(1) forKey:@"usePoints"];
    }
    [self getDataInfoWithUrl:kAPI_Gift_Pay params:param start:startBlock failure:failBlock success:success];

}


// 直播室门票支付接口
+(void)payTicket2WithRoomId:(NSString *)roomid
                       type:(NSString *)type
                    goodsId:(NSString *)goodsId
                  usePoints:(NSInteger)usePoints
                      start:(void (^)())startBlock
                    failure:(void (^)())failBlock
                    success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (type) {
        [params setObject:type forKey:@"type"];
    }
    if (goodsId.length > 0) {
        [params setObject:goodsId forKey:@"goodsId"];
    }
    if (roomid) {
        [params setObject:roomid forKey:@"roomId"];
    } else {
        [params setObject:@"0" forKey:@"roomId"];
    }
    if (usePoints > 0) {
        [params setObject:@(1) forKey:@"usePoints"];
    }
    [self getDataInfoWithUrl:kAPI_Video2_payTickets
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}


/// 指标支付
+ (void)payIndexWithGoodsId:(NSString *)goodsId
                  usePoints:(NSInteger)usePoints
                      start:(void (^)())startBlock
                    failure:(void (^)())failBlock
                    success:(requestSuccessBlock)success {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param setObject:goodsId forKey:@"id"];
    if (usePoints > 0) {
        [param setObject:@(1) forKey:@"usePoints"];
    }
    [self getDataInfoWithUrl:kAPI_Index_Pay
                           params:param
                            start:startBlock
                          failure:failBlock
                          success:success];
}

/// 策略支付
+ (void)payStrategyWithGoodsId:(NSString *)goodsId
                     usePoints:(NSInteger)usePoints
                         start:(void (^)())startBlock
                       failure:(void (^)())failBlock
                       success:(requestSuccessBlock)success {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param setObject:goodsId forKey:@"id"];
    if (usePoints > 0) {
        [param setObject:@(1) forKey:@"usePoints"];
    }
    [self getDataInfoWithUrl:kAPI_Strategy_Pay
                           params:param
                            start:startBlock
                          failure:failBlock
                          success:success];
}

/// 解套宝支付
+ (void)payUnlockPositionWithGoodsId:(NSString *)goodsId
                           usePoints:(NSInteger)usePoints
                               start:(void (^)())startBlock
                             failure:(void (^)())failBlock
                             success:(requestSuccessBlock)success {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param setObject:goodsId forKey:@"id"];
    if (usePoints > 0) {
        [param setObject:@(1) forKey:@"usePoints"];
    }
    [self getDataInfoWithUrl:kAPI_UnlockPosition_Pay
                           params:param
                            start:startBlock
                          failure:failBlock
                          success:success];
}

@end
