//
//  HttpRequestTool.m
//  DakaNote
//
//  Created by <PERSON><PERSON> on 2017/5/22.
//  Copyright © 2017年 LZ. All rights reserved.
//

#import "HttpRequestTool.h"
#import "FMPayTool.h"
#import "LFPhoneInfo.h"

@implementation HttpRequestTool


/**
 *  加密参数 (每个接口都要包含四个基础参数  version platform timestamp sign)
 *
 *  @param params 传过来一个接口参数字典,通过方法,返回一个带有四个基础参数的字典
 */
+ (NSDictionary*)hashWithParams:(NSDictionary *)params {
    NSMutableDictionary *newDic = [[NSMutableDictionary alloc]init];
    // 字典里添加三个对象  (版本号  iOS  时间戳)
    NSString *platform = @"iOS";
    NSString *timestamp = [NSString stringWithFormat:@"%.f",[FMHelper getTimestamp]];
    [newDic setObject:platform forKey:@"platform"];
    [newDic setObject:timestamp forKey:@"timestamp"];
    [newDic setObject:[MyUUIDManager getUUID] forKey:@"deviceId"];
    [newDic setObject:[MyUUIDManager getUUID] forKey:@"deviceToken"];
    
    [newDic setValuesForKeysWithDictionary:params];
//    // 把字典的key按照英文首字母升序排列
//    NSArray *keys = newDic.allKeys;
//    keys = [keys sortedArrayUsingComparator:^NSComparisonResult(id obj1, id obj2) {
//        NSComparisonResult result = [obj1 compare:obj2];
//        return result==NSOrderedDescending;
//    }];
//    // 把排列后的参数拼接
//    NSString *hashStr = @"";
//    for (NSString *key in keys) {
//        id value = [newDic objectForKey:key];
//        NSString *value1 = [NSString stringWithFormat:@"%@",value];
//        if (value && ![value isEqual:[NSNull null]] && value != nil && value1.length != 0) {
//            hashStr = [hashStr stringByAppendingString:[NSString stringWithFormat:@"%@%@",key,value]];
//        }
//    }
//    // 拼接密钥,并且md5加密,得到sign的值
//    NSString *newHashStr = [NSString stringWithFormat:@"%@%@",kAPI_Key,hashStr];
//    
//    // md5加密
//    NSString *auth = [FMHelper md532BitUpper:newHashStr];
//    [newDic setObject:auth forKey:@"sign"];
    return newDic;
}

/**
 *  创建通用分页参数字典
 */
+ (NSMutableDictionary *)createPaginationParamsWithPage:(NSInteger)page 
                                               pageSize:(NSInteger)pageSize 
                                       additionalParams:(NSDictionary *)additionalParams {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    
    // 添加分页参数
    if (page > 0) {
        [params setObject:@(page) forKey:@"pageNo"];
    }
    if (pageSize > 0) {
        [params setObject:@(pageSize) forKey:@"pageSize"];
    }
    
    // 添加额外参数
    if (additionalParams && additionalParams.count > 0) {
        [params setValuesForKeysWithDictionary:additionalParams];
    }
    
    return params;
}

#pragma mark -- 通用Get / Post
+(void)getDataInfoWithUrl:(NSString *)url
                   params:(NSDictionary *)params
                    start:(void (^)())startBlock
                  failure:(void (^)())failBlock
                  success:(requestSuccessBlock)success {
    [self requestWithType:HttpRequestTypeGet url:url header:nil params:params start:startBlock failure:failBlock success:success];
}

+(void)getDataInfoWithUrl:(NSString *)url
                   header:(NSDictionary *)header
                   params:(NSDictionary *)params
                    start:(void (^)())startBlock
                  failure:(void (^)())failBlock
                  success:(requestSuccessBlock)success {
    [self requestWithType:HttpRequestTypeGet url:url header:header params:params start:startBlock failure:failBlock success:success];
}

+ (void)postDataInfoWithUrl:(NSString *)url
                     params:(NSDictionary *)params
                      start:(void (^)())startBlock
                    failure:(void (^)())failBlock
                    success:(requestSuccessBlock)success {
    [self requestWithType:HttpRequestTypePost url:url header:nil params:params start:startBlock failure:failBlock success:success];
}

+ (void)postDataInfoWithUrl:(NSString *)url
                     header:(NSDictionary *)header
                     params:(NSDictionary *)params
                      start:(void (^)())startBlock
                    failure:(void (^)())failBlock
                    success:(requestSuccessBlock)success {
    [self requestWithType:HttpRequestTypePost url:url header:header params:params start:startBlock failure:failBlock success:success];
}

+ (void)putDataInfoWithUrl:(NSString *)url
                    params:(NSDictionary *)params
                     start:(void (^)())startBlock
                   failure:(void (^)())failBlock
                   success:(requestSuccessBlock)success {
    [self requestWithType:HttpRequestTypePut url:url header:nil params:params start:startBlock failure:failBlock success:success];
}

+ (void)postDataJsonInfoWithUrl:(NSString *)url
                         params:(NSDictionary *)params
                          start:(void (^)())startBlock
                        failure:(void (^)())failBlock
                        success:(requestSuccessBlock)success {
    [self requestWithType:HttpRequestTypeBodyPost url:url header:nil params:params start:startBlock failure:failBlock success:success];
}

+ (void)postDataJsonInfoWithUrl:(NSString *)url
                         header:(NSDictionary *)header
                         params:(NSDictionary *)params
                          start:(void (^)())startBlock
                        failure:(void (^)())failBlock
                        success:(requestSuccessBlock)success {
    [self requestWithType:HttpRequestTypeBodyPost url:url header:header params:params start:startBlock failure:failBlock success:success];
}


+ (void)requestWithType:(HttpRequestType)type
                    url:(NSString *)url
                 header:(NSDictionary *)header
                 params:(NSDictionary *)params
                  start:(void (^)())startBlock
                failure:(void (^)())failBlock
                success:(requestSuccessBlock)success {
    if(startBlock){
        startBlock();        
    }
    [UIApplication sharedApplication].networkActivityIndicatorVisible = YES;
    WEAKSELF
    [NetworkManager requestWithType:type withUrlString:url header:header withParaments:[self hashWithParams:params] withSuccessBlock:^(NSDictionary *object) {
        [UIApplication sharedApplication].networkActivityIndicatorVisible = NO;
        if ([[object class] isSubclassOfClass:[NSDictionary class]] && object) {
            if (success) {
                success(object);
            }
            [CountDownShareInstance shareInstance].serverTime =  [object[@"systemTime"] longValue];
            
            if ([object[@"errcode"] isEqualToString:@"100"]) {
                if ([FMUserDefault getUserId].length > 0)  {
                    [__weakSelf userLoginAuthByToken:^{
                        if (![object[@"status"] isEqualToString:@"1"]) {
                            [__weakSelf normalHttpDataInfoWithType:type url:url params:params start:startBlock failure:failBlock success:success];
                        }
                    } authTokenFailureBlock:^{
                    }];
                } else {
                    [ProtocolJump jumpWithUrl:Login_URL];
                }
            }
            
            if ([object[@"status"] isEqualToString:@"0"]) {
                if([object[@"errcode"] isEqualToString:@"501"] || [object[@"errcode"] isEqualToString:@"502"] || [object[@"errcode"] isEqualToString:@"503"]) {
                    [SVProgressHUD dismiss];
                    // 没有可用的支付方式
                    if ([FMUserDefault getUserId].length) {
                        [[FMPayTool payTool] noEnablePayModelWithErrorCode:[object[@"errcode"] integerValue] errorText:object[@"errmessage"]];
                    }
                }
            }
        } else {
            if (failBlock) {
                failBlock();
            }
        }
    } withFailureBlock:^(NSError *error) {
        [UIApplication sharedApplication].networkActivityIndicatorVisible = NO;
        if (error.code != -999) { // -999表示取消了请求
            if (failBlock) {
                failBlock();
            }
        }
    } progress:^(float progress) {
        // 进度
    }];
}


#pragma mark -- 普通的Get / Post，不做任何异常处理
+(void)normalHttpDataInfoWithType:(HttpRequestType)type
                              url:(NSString *)url
                           params:(NSDictionary *)params
                            start:(void (^)())startBlock
                          failure:(void (^)())failBlock
                          success:(requestSuccessBlock)success{
    if (startBlock) {
        startBlock();
    }
    [NetworkManager requestWithType:type withUrlString:url withParaments:[self hashWithParams:params] withSuccessBlock:^(NSDictionary *object) {
        if ([[object class] isSubclassOfClass:[NSDictionary class]] && object) {
            if (success) {
                success(object);
            }
        } else {
            if (failBlock) {
                failBlock();
            }
        }
    } withFailureBlock:^(NSError *error) {
        if (failBlock) {
            failBlock();
        }
    } progress:^(float progress) {
        // 进度
    }];
}

#pragma mark - 下载PDF文件请求

+(void)downLoadFileRequestWithOperations:(NSDictionary *)operations
                                SavePath:(NSString *)savePath
                               UrlString:(NSString *)urlString
                                 success:(requestSuccessBlock)success
                                 failure:(void(^)())failBlock
                                progress:(void(^)(float progress))progressBlock{
    
    [NetworkManager downLoadFileWithOperations:operations withSavaPath:savePath withUrlString:urlString withSuccessBlock:^(NSDictionary *object) {
        if ([[object class] isSubclassOfClass:[NSDictionary class]] && object) {
            success(object);
        } else {
            failBlock();
        }
    } withFailureBlock:^(NSError *error) {
        failBlock();
    } withDownLoadProgress:^(float progress) {
        progressBlock(progress);
    }];
}

#pragma mark - 取消请求
/**
 *  取消所有的网络请求
 */
+(void)cancelAllRequest {
    [NetworkManager cancelAllRequest];
}

/**
 *  取消指定的url请求
 *
 *  @param requestType 该请求的请求类型
 *  @param string      该请求的url
 */
+(void)cancelHttpRequestWithUrlString:(NSString *)string{
    [NetworkManager cancelHttpRequestWithUrlString:string];
}


@end
