//
//  FMPayHandle.h
//  QCYZT
//
//  Created by <PERSON> on 2022/8/22.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "FMLiveDetailModel.h"
#import "EnablePayModel.h"
NS_ASSUME_NONNULL_BEGIN


@interface FMPayHandle : NSObject
/// 支付金币
/// @param detailModel 直播室详情模型
/// @param success 支付成功模型
+(void)payCoinWithDetailModel:(FMLiveDetailModel *)detailModel success:(void(^)(EnablePayModel *selectedModel))success;

/// 导航到vip购买页面
/// @param detailModel
+(void)pushToVipBuyPageWithDetailModel:(FMLiveDetailModel *)detailModel;

/// 联系顾问
+(void)contactAdviser;
@end

NS_ASSUME_NONNULL_END
