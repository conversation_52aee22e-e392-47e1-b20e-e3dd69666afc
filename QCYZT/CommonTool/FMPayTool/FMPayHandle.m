//
//  FMPayHandle.m
//  QCYZT
//
//  Created by <PERSON> on 2022/8/22.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMPayHandle.h"
#import "FMPayTool.h"
#import "PaymentView.h"
#import "HttpRequestTool+Pay.h"
#import "FMMemberCenterProductViewController.h"
#import "FMPaySuccessPopView.h"

@implementation FMPayHandle
+(void)payCoinWithDetailModel:(FMLiveDetailModel *)detailModel success:(void(^)(EnablePayModel *selectedModel))success{
    // 检查登录
    if ([FMHelper checkLoginStatus]) {
        
        NSString *dakaId = detailModel.bignameDto.userId;
        // 判断针对此投顾是否需要弹出确认书
        [[FMPayTool payTool] judgeConfirmOrderStatusWithDakaId:dakaId certCode:detailModel.bignameDto.certCode clickView:nil confirmOperation:^{
            // 点击确认书下面“确认支付”的回调
            [self payClickBlockWithDetailModel:detailModel success:success];
        }];
    }
};

/// 点击确认书下面“确认支付”的回调
/// @param detailModel detailModel
+(void)payClickBlockWithDetailModel:(FMLiveDetailModel *)detailModel success:(void(^)(EnablePayModel *))success{
    
    // 先置空
    detailModel.enablePayModel = @[];
    EnablePayModel *enablePayModel = [[EnablePayModel alloc] init];
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    enablePayModel.paydesc = [NSString stringWithFormat:@"您当前拥有%zd金币，确认支付", userModel.coin];
    enablePayModel.num = [NSString stringWithFormat:@"%zd", userModel.coin];
    enablePayModel.name = @"金币";
    enablePayModel.type = PaymentTypeCoin;
    enablePayModel.freeDesc = detailModel.freeDesc;
    enablePayModel.consumeType = 5;
    enablePayModel.bignameId = detailModel.bignameDto.userId;
    enablePayModel.contentId = detailModel.liveId;
    detailModel.enablePayModel = @[enablePayModel];
    
    NSString *price = [NSString stringWithFormat:@"%ld",detailModel.ticketInfo.price];
    NSString *title = detailModel.name;
    
    [PaymentView showWithEnablePayModel:enablePayModel payPrice:price productName:title bottomReminder:@"注：已付费的产品和内容，不会重复扣费" payAction:^(EnablePayModel *selectedModel) {
        [self sendPayCoinHttpReqWithDetailModel:detailModel selectedModel:selectedModel success:success];
    } dismissBlock:^{
        
    }];
}


/// 直播室门票支付接口
/// @param detailModel 直播室详情模型
/// @param selectedModel 选中模型
/// @param success 成功回调
+(void)sendPayCoinHttpReqWithDetailModel:(FMLiveDetailModel *)detailModel selectedModel:(EnablePayModel *)selectedModel success:(void(^)(EnablePayModel *))success{
    // 直播室门票支付接口
    
    [HttpRequestTool payTicket2WithRoomId:[NSString stringWithFormat:@"%ld",detailModel.liveId] type:[NSString stringWithFormat:@"%zd", selectedModel.type] goodsId:selectedModel.couponId usePoints:selectedModel.usePoints start:^{
        [SVProgressHUD show];
//                            confirmButton.userInteractionEnabled = NO;
    } failure:^{
//                            confirmButton.userInteractionEnabled = YES;
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        [SVProgressHUD dismiss];
        if ([dic[@"status"] isEqualToString:@"1"] || [dic[@"status"] isEqualToString:@"2"]) {
            if (success) {
                success(selectedModel);
            }
            FMPaySuccessPopView *popView = [[FMPaySuccessPopView alloc] init];
            popView.jumpIndex = 1;
            [popView show];
        } else if ([dic[@"errcode"] isEqualToString:@"501"] || [dic[@"errcode"] isEqualToString:@"502"] || [dic[@"errcode"] isEqualToString:@"503"]) {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

+(void)pushToVipBuyPageWithDetailModel:(FMLiveDetailModel *)detailModel{
    FMMemberCenterProductViewController *vc = [[FMMemberCenterProductViewController alloc] init];
    vc.bigcastId = detailModel.bignameDto.userId;
    [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
}

+(void)contactAdviser{
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    if (userModel.vip) {
        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:@"weixin://"] options:@{} completionHandler:nil];
    } else {
        NSString *str = [NSString stringWithFormat:@"tel://%@", [FMUserDefault getSeting:AppInit_ContactUs_Phone]];
        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:str] options:@{} completionHandler:nil];
    }
}
@end
