//
//  FMPrivateLetterNoRightViewController.m
//  QCYZT
//
//  Created by zeng on 2022/6/28.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMPrivateLetterNoRightViewController.h"
#import "HttpRequestTool+Pay.h"
#import "FMPayTool.h"
#import "FMMemberCenterProductViewController.h"
#import "FMPrivateLetterViewController.h"
#import "PaymentView.h"

@interface FMPrivateLetterNoRightViewController ()

@property (nonatomic, strong) UIView *containView;
@property (nonatomic, strong) UIImageView *iconImgV;
@property (nonatomic, strong) UIButton *coinBtn;
//@property (nonatomic, strong) UIButton *memberBtn;
@property (nonatomic, strong) UIView *ruleView;
@property (nonatomic, strong) YYLabel *phoneLabel;

@end

@implementation FMPrivateLetterNoRightViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.title = self.model.bignameName;
    self.view.backgroundColor = UIColor.up_contentBgColor;
    
    UIView *containerView = [[UIView alloc] init];
    [self.view addSubview:containerView];
    [containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsZero);
    }];
    self.containView = containerView;
    
    [containerView addSubview:self.iconImgV];
    [self.iconImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(@0);
        make.top.equalTo(@70);
    }];
    
    [containerView addSubview:self.coinBtn];
    [self.coinBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(@0);
        make.top.equalTo(self.iconImgV.mas_bottom).offset(25);
        make.width.equalTo(@(UI_SCREEN_WIDTH - 75));
        make.height.equalTo(@45);
    }];
    
//    [containerView addSubview:self.memberBtn];
//    [self.memberBtn mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.centerX.equalTo(@0);
//        make.top.equalTo(self.coinBtn.mas_bottom).offset(15);
//        make.width.equalTo(@(UI_SCREEN_WIDTH - 75));
//        make.height.equalTo(@45);
//    }];
    
    [containerView addSubview:self.ruleView];
    [self.ruleView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(@0);
        make.left.equalTo(@15);
        make.top.equalTo(self.coinBtn.mas_bottom).offset(UI_Relative_HeightValue(75));
    }];
    
    [containerView addSubview:self.phoneLabel];
    [self.phoneLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.left.equalTo(@0);
        make.top.equalTo(self.ruleView.mas_bottom).offset(15);
    }];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    [self configNavWhiteColorWithCloseSEL:@selector(backArrowClicked)];
}

- (void)backArrowClicked {
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)coinBtnClicked {
    if (self.model.enablePayModel.count == 0) {
        // 没有可用的支付方式
        [[FMPayTool payTool] noEnablePayModelWithErrorCode:self.model.noPayModelCode errorText:self.model.noPayModelText];
        return;
    }
    
    [[FMPayTool payTool] judgeConfirmOrderStatusWithDakaId:self.model.bignameId certCode:self.model.cert_code clickView:self.coinBtn confirmOperation:^{
        EnablePayModel *payModel = [NSArray modelArrayWithClass:[EnablePayModel class] json:self.model.enablePayModel].firstObject;
        payModel.bignameId = self.model.bignameId;
        payModel.consumeType = 3;
        payModel.contentId = self.model.currentOrderId.integerValue;
        payModel.name = @"金币";
        [PaymentView showWithEnablePayModel:payModel payPrice:[NSString stringWithFormat:@"%.0f", self.model.price] productName:[NSString stringWithFormat:@"与%@的私聊", self.model.bignameName] bottomReminder:@"注：已付费的产品和内容，不会重复扣费" payAction:^(EnablePayModel *selectedModel) {
            [self requestPay:selectedModel];
        } dismissBlock:^{
        }];
    }];
}

- (void)requestPay:(EnablePayModel *)payModel {
    [HttpRequestTool requestPrivateLetterPayWithBigcastId:self.model.bignameId
                                                     type:[NSString stringWithFormat:@"%zd", payModel.type]
                                                  goodsId:payModel.couponId
                                                usePoints:payModel.usePoints
                                                    start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD showSuccessWithStatus:@"支付成功"];
            
            self.model.currentOrderId = [NSString stringWithFormat:@"%@", dic[@"data"][@"id"]];
            self.model.currentSentence = 2;
            FMPrivateLetterViewController *vc = [[FMPrivateLetterViewController alloc] init];
            vc.model = self.model;
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self removeFromParentViewController];
            });
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

- (void)memberBtnClicked {
    FMMemberCenterProductViewController *vc = [[FMMemberCenterProductViewController alloc] init];
    vc.bigcastId = self.model.bignameId;
    [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
}

- (void)setModel:(FMPrivateLetterBigcastHomeModel *)model {
    _model = model;
    
    [self.coinBtn setTitle:[NSString stringWithFormat:@"%.0f金币 付费私聊", model.price] forState:UIControlStateNormal];
//    self.memberBtn.hidden = !model.allowOpenVip;
}

- (UIImageView *)iconImgV {
    if (!_iconImgV) {
        _iconImgV = [[UIImageView alloc] initWithImage:ImageWithName(@"PrivateLetter_NoRight")];
    }
    
    return _iconImgV;
}

- (UIButton *)coinBtn {
    if (!_coinBtn) {
        _coinBtn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(16) normalTextColor:FMNavColor backgroundColor:FMClearColor title:@"付费私聊" image:nil target:self action:@selector(coinBtnClicked)];
        UI_View_BorderRadius(_coinBtn, 22.5, 1.0, FMNavColor);
    }
    
    return _coinBtn;
}

- (UIView *)ruleView {
    if (!_ruleView) {
        _ruleView = [UIView new];
        _ruleView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
        UI_View_Radius(_ruleView, 5.0f);
        
        UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(14) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        titleLabel.text = @"私信规则";
        [_ruleView addSubview:titleLabel];
        [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(@0);
            make.top.equalTo(@12);
        }];
        
        UILabel *descLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(13) textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:0 textAlignment:NSTextAlignmentLeft];
        [_ruleView addSubview:descLabel];
        [descLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(@15);
            make.right.equalTo(@-15);
            make.top.equalTo(@40);
            make.bottom.equalTo(@-15);
        }];
        descLabel.text = @"1、支付完成后，24小时内可向老师发送2条消息，老师可回复多条消息；\n2、若24小时后，老师未回复消息，花费的金币/卡券/积分将原路退回，若积分已过期将不再退回。";
    }
    
    return _ruleView;
}

- (YYLabel *)phoneLabel {
    if (!_phoneLabel) {
        _phoneLabel = [[YYLabel alloc] init];
        _phoneLabel.font = FontWithSize(13);
        _phoneLabel.textColor = UIColor.up_textSecondary1Color;
        _phoneLabel.numberOfLines = 1;
        
        NSString *descStr = [NSString stringWithFormat:@"联系客服：%@", [FMUserDefault getSeting:AppInit_ContactUs_Phone]];
        NSRange range = [descStr rangeOfString:[FMUserDefault getSeting:AppInit_ContactUs_Phone]];
        NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:descStr];
        attrStr.yy_color = UIColor.up_textSecondary1Color;
        attrStr.yy_alignment = NSTextAlignmentCenter;
        [attrStr yy_setTextHighlightRange:range color:ColorWithHex(0x0077ff) backgroundColor:FMClearColor tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
            NSString *str = [NSString stringWithFormat:@"tel://%@", [FMUserDefault getSeting:AppInit_ContactUs_Phone]];
            [[UIApplication sharedApplication] openURL:[NSURL URLWithString:str] options:@{} completionHandler:nil];
        }];
        _phoneLabel.attributedText = attrStr;
    }
    
    return _phoneLabel;
}

@end
