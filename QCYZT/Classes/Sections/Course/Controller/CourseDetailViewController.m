//
//  CourseDetailViewController.m
//  QCYZT
//
//  Created by <PERSON><PERSON> on 2016/12/18.
//  Copyright © 2016年 sdcf. All rights reserved.
//

#import "CourseDetailViewController.h"
#import "CourseDetailProfileViewController.h"
#import "CourseDetailCatalogViewController.h"
#import "HttpRequestTool+Course.h"
#import "CourseDetailTopView.h"
#import "FMCommentView.h"
#import "CourseDetailModel.h"
#import "FMShareHelper.h"
#import "FMDetailBottomView.h"
#import "FMPayTool.h"
#import "EnablePayModel.h"
#import "PaymentView.h"
#import "CourseAlbumDetailModel.h"
#import "FMPaySuccessPopView.h"
//视频播放
#import "ZFPlayerControlView.h"
#import "FMProgressHUD.h"
#import "FMTaskConfigModel.h"
#import "HttpRequestTool+Pay.h"
#import "HttpRequestTool+Course.h"
#import "FMPlayerManager.h"
#import "StatisticsManager.h"

@interface CourseDetailViewController ()<FMDetailBottomViewDelegate,CourseDetailTopViewDelegate,SGPageTitleViewDelegate,SGPageContentCollectionViewDelegate>

@property (nonatomic, strong) UIView *blackView;
@property (nonatomic, strong) CourseDetailTopView *topView;
@property (nonatomic, strong) SGPageTitleView *pageTitleView;
@property (nonatomic, strong) SGPageContentCollectionView *pageContentCollectionView;
@property (nonatomic, strong) FMDetailBottomView *bottomView;
@property (nonatomic, strong) FMCommentView *commentView;
@property (nonatomic, strong) UIButton *backBtn;

@property (nonatomic, strong) CourseDetailModel *detailModel;

@property (nonatomic, assign) BOOL needsRefresh;   // 登录之后是否要刷新
//@property (nonatomic, strong) ZFPlayerController *player;
@property (nonatomic, strong) ZFPlayerControlView *controlView;
//@property (nonatomic, strong) ZFAVPlayerManager *playerManager;
@property (nonatomic,assign) NSInteger currentPalyStatus;///登录状态改变时,试看结束或者重新试看时 当前试看播放状态记录字段。  0初始状态1重置播放2试看结束

@property (nonatomic, assign) BOOL isFirstRequest;
@property (nonatomic, assign) CGFloat kTopViewTop;

@end

@implementation CourseDetailViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.view.backgroundColor = UIColor.fm_F7F7F7_232426;
    self.title = @"课程详情";

    self.isFirstRequest = YES;
    if (DEVICE_IS_iPhoneXAll) {
        self.kTopViewTop = UI_STATUS_HEIGHT;
        UIView *blackView = [[UIView alloc] init];
        [self.view addSubview:blackView];
        [blackView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.left.right.equalTo(@0);
            make.height.equalTo(@(self.kTopViewTop));
        }];
        self.blackView = blackView;
    } else {
        self.kTopViewTop = 0;
    }
        
    [self.view addSubview:self.bottomView];
    [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(@0);
        make.height.equalTo(@(DetailBottomViewHeight));
    }];
    self.bottomView.hidden = YES;
    self.bottomView.delegate = self;
    
    [self.view addSubview:self.topView];
    [self.topView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(@0);
        make.top.equalTo(@(self.kTopViewTop));
        make.height.equalTo(@0);
    }];
    
    [self.topView addSubview:self.backBtn];
    [self.backBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.top.equalTo(@10);
        make.width.equalTo(@20);
    }];
    
    // 请求课程详情
    [self requestCourseDetailWithSuccess:nil];

    [FMHelper addLoginAndLogoutNotificationWithObserver:self selector:@selector(handleLoginStatusNotification) monitorAuthLogin:NO];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleLoginStatusNotification) name:kRechargeSuccess object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(notificationViewClick:) name:kNotificationViewClickNotification object:nil];
    //  注册系列课程分享成功通知
    if(self.isChapterDetail) {
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(albumShared) name:kShareCourseAlbum object:nil];
    }
    
    // 添加功能使用统计 - 详情控制器统计
    [[StatisticsManager sharedManager] trackControllerCreated:self functionId:FunctionItemTypeCourse];
}

- (void)viewWillAppear:(BOOL)animated {
    self.selfNavigationBarHidden = YES;
    
    [super viewWillAppear:animated];
    if (![[FMPlayerManager shareManager].courseModel.courseId isEqualToString:self.courseId]) {
        [FMPlayerManager dismissGlobalVideoPlayViewWithDestroyPlayer];
    }
    if (self.needsRefresh) {
        self.needsRefresh = NO;
        [self requestCourseDetailWithSuccess:nil];
    }
    
    if (!DEVICE_IS_iPhoneXAll) {
        [[UIApplication sharedApplication] setStatusBarHidden:YES];
    }
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    if (![FMPlayerManager shareManager].isSmallViewPlay) {
        [FMPlayerManager shareManager].player.viewControllerDisappear = YES;
    }
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];

    if (!DEVICE_IS_iPhoneXAll) {
        [[UIApplication sharedApplication] setStatusBarHidden:NO];
    }
}

- (void)back {
    [self.navigationController popViewControllerAnimated:YES];
}


- (void)dealloc {
    FMLog(@"%s",__func__);
    if (![FMPlayerManager shareManager].isSmallViewPlay) {
        [FMPlayerManager dismissGlobalVideoPlayViewWithDestroyPlayer];
    }
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark- 控制横竖屏幕
//  是否支持自动转屏
- (BOOL)shouldAutorotate
{
    return NO;
}

// 支持哪些转屏方向
- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    if ([FMPlayerManager shareManager].player.isFullScreen) {
        return UIInterfaceOrientationMaskLandscape;
    }
    return UIInterfaceOrientationMaskPortrait;
}

#pragma mark -- NSNotification
- (void)handleLoginStatusNotification {
    self.needsRefresh = YES;
}

/// 章节详情分享成功通知
- (void)albumShared  {
    if ([FMHelper isLogined]) {
        [HttpRequestTool shareAlbumWithAlbumId:self.detailModel.courseId start:^{
        } failure:^{
        } success:^(NSDictionary *dic) {
            if ([dic[@"status"] isEqualToString:@"1"]) {
                self.detailModel.hasShared = 1;
                self.detailModel.authority = @"1";
                self.detailModel.series_info.hasShared = 1;

                if (self.childViewControllers.count == 2) {
                    CourseDetailCatalogViewController *vc = self.childViewControllers[1];
                    vc.detailModel = self.detailModel;
                }
            }
        }];
    }
}

//收到通知 点击通知栏协议跳转  如果是横屏 需要回到竖屏 并做相应跳转
- (void)notificationViewClick:(NSNotification *)noti {
    [FMPlayerManager shareManager].player.viewControllerDisappear = YES;
    if ([FMPlayerManager shareManager].player.isFullScreen) {
        [[FMPlayerManager shareManager].player enterFullScreen:NO animated:YES];
    }
    [self layoutTopView];
}

- (void)networkChanged {
    if ([FMNetworkStatusMonitor sharedMonitor].currentStatus == NetworkStatusCellular) {
        if ([[FMHelper getCurrentVC] isEqual:self]) {
            [FMProgressHUD showTextOnlyInView:self.controlView withText:@"当前处于非wifi环境,请注意流量消耗"];
        }
    }
}

#pragma mark - CourseDetailTopViewDelegate
//重新试看
- (void)courseVideoReWatchBtnClick:(CourseDetailTopView *)topView {
    self.currentPalyStatus = 1;
    self.controlView.portraitControlView.slider.isdragging = NO;
    self.controlView.portraitControlView.slider.isdragging = NO;
    [FMPlayerManager shareManager].player.currentPlayerManager.view.hidden = NO;
    [[FMPlayerManager shareManager].player.currentPlayerManager.view.superview bringSubviewToFront:[FMPlayerManager shareManager].player.currentPlayerManager.view];
    [[FMPlayerManager shareManager].player.currentPlayerManager replay];

    self.detailModel.playStatus = self.currentPalyStatus;
    topView.model = self.detailModel;
    [topView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@(topView.viewHeight));
    }];
    [self changePageViewFrame];
}

- (void)courseDetailTopViewVideoImgVClick:(CourseDetailTopView *)topView {
    self.detailModel.playStatus = self.currentPalyStatus;
    topView.model = self.detailModel;
    
    [self showVideoPlayer];
}

- (void)courseSeriesPayBtnClick:(CourseDetailTopView *)topView {
    if ([FMHelper checkLoginStatus]) {
        if (self.detailModel.ex_needVIP) {
            FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
            if (userModel.vip) {
                [[UIApplication sharedApplication] openURL:[NSURL URLWithString:@"weixin://"] options:@{} completionHandler:nil];
            } else {
                NSString *str = [NSString stringWithFormat:@"tel://%@", [FMUserDefault getSeting:AppInit_ContactUs_Phone]];
                [[UIApplication sharedApplication] openURL:[NSURL URLWithString:str] options:@{} completionHandler:nil];
            }
        } else {
            [self gotoPay];
        }
    }
}

#pragma mark - FMDetailBottomView Delegate
- (void)detailBottomViewCommentBtnDidClicked:(UIView *)bottomView btn:(UIButton *)commentBtn {
    bottomView.userInteractionEnabled = NO;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        bottomView.userInteractionEnabled = YES;
    });
    WEAKSELF;
    if ([FMHelper checkLoginStatus]) {
        UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
        self.commentView.frame = keyWindow.bounds;
        [keyWindow addSubview:self.commentView];
        self.commentView.publisLb.text = @"发表评论";
        self.commentView.publishBlock = ^(NSString *content){
            CourseType courseType = __weakSelf.isChapterDetail ? SeriesCourseType : ExcellentCourseType;
          
            [HttpRequestTool courseCommentPublishWithCourseId:__weakSelf.courseId CourseType:courseType commentLev:@"1" commentid:nil commentContent:content start:^{
                [SVProgressHUD show];
            } failure:^{
                [SVProgressHUD showErrorWithStatus:@"网络不给力"];
            } success:^(NSDictionary *dic) {
                if ([dic[@"status"] isEqualToString:@"1"]) {
                    [SVProgressHUD showSuccessWithStatus:@"发布成功！审核通过后展示"];
                    __weakSelf.commentView.textView.text = @"";
                    
//                    CourseDetailProfileViewController *vc = __weakSelf.childViewControllers.firstObject;
//                    vc.courseType = __weakSelf.isChapterDetail ? SeriesCourseType : ExcellentCourseType;
//                    [vc addCommentWithContent:content commentId:[NSString stringWithFormat:@"%@", dic[@"data"][@"id"]]];
                } else {
                    [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
                }
            }];
        };
    };
}

- (void)detailBottomViewCollectBtnDidClicked:(UIView *)bottomView btn:(UIButton *)collectBtn {
    if ([FMHelper checkLoginStatus]) {
        WEAKSELF;
        CourseType courseType = self.isChapterDetail ? SeriesCourseType : ExcellentCourseType;
        if (self.detailModel.myCollected.boolValue) {
            [FMCommonHttp unCollectWithButton:collectBtn contentId:self.courseId courseType:courseType unCollectSuccess:^{
                __weakSelf.detailModel.myCollected = @"0";
                __weakSelf.bottomView.collected = NO;
            }];
        } else {
            [FMCommonHttp collectBtnWithButton:collectBtn contentId:self.detailModel.courseId courseType:courseType collectSuccess:^{
                __weakSelf.detailModel.myCollected = @"1";
                __weakSelf.bottomView.collected = YES;
            }];
        }
    }
}

- (void)detailBottomViewShareBtnDidClicked:(UIView *)bottomView btn:(UIButton *)shareBtn {
    if(self.isChapterDetail) {
        CourseDetailModel * model = [self.detailModel mutableCopy];
        model.courseId = self.courseId;
        [FMShareHelper shareAlbumCourseChapersDetailWithModel:model];
    } else {
        [FMShareHelper shareCourseDetailWithModel:self.detailModel];
    }
}

- (void)detailBottomViewPraiseBtnDidClicked:(UIView *)bottomView btn:(FMDetailBottomViewRightTopNumView *)praiseBtn {
    if ([FMHelper checkLoginStatus]) {
        CourseType courseType = self.isChapterDetail ? SeriesCourseType : ExcellentCourseType;
        [HttpRequestTool coursePraiseWithCourseId:self.courseId CourseType:courseType start:^{
            praiseBtn.userInteractionEnabled = NO;
        } failure:^{
            praiseBtn.userInteractionEnabled = YES;
            [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        } success:^(NSDictionary *dic) {
            praiseBtn.userInteractionEnabled = YES;
    
            if ([dic[@"status"] isEqualToString:@"1"]) {
                [SVProgressHUD dismiss];
    
                self.detailModel.mySatisfied = @"1";
                self.detailModel.satisfiedNums =  [NSString stringWithFormat:@"%ld",self.detailModel.satisfiedNums.integerValue + 1];
                
                self.bottomView.praised = YES;
                self.bottomView.praiseNum = self.detailModel.satisfiedNums.integerValue;
            } else {
                [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            }
        }];
    }
}

#pragma mark - SGPageTitleViewDelegate
- (void)pageTitleView:(SGPageTitleView *)pageTitleView selectedIndex:(NSInteger)selectedIndex{
    [self.pageContentCollectionView setPageContentCollectionViewCurrentIndex:selectedIndex];
}

#pragma mark - SGPageContentCollectionViewDelegate
- (void)pageContentCollectionView:(SGPageContentCollectionView *)pageContentCollectionView progress:(CGFloat)progress originalIndex:(NSInteger)originalIndex targetIndex:(NSInteger)targetIndex {
    [self.pageTitleView setPageTitleViewWithProgress:progress originalIndex:originalIndex targetIndex:targetIndex];
}

#pragma mark - Http
/// 优质课程 系列课程章节 详情
- (void)requestCourseDetailWithSuccess:(void(^)(void))successBlock {
    WEAKSELF;
    NSInteger courseType = self.isChapterDetail ? ChaptersCourseType : ExcellentCourseType;
    [HttpRequestTool getCourseDetailWithcourseId:[self.courseId integerValue] CourseType:courseType  start:^{
        if (__weakSelf.isFirstRequest) {
            __weakSelf.topView.hidden = YES;
        }
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        [SVProgressHUD dismiss];
        if ([dic[@"status"] isEqualToString:@"1"]) {
            self.detailModel = [CourseDetailModel modelWithDictionary:dic[@"data"]];
            self.detailModel.isChapterDetail = self.isChapterDetail;
            // 课程被删除 显示站位图
            if (__weakSelf.detailModel.deleteFlag.integerValue < 0) {
                self.navigationItem.title = @"课程详情";
                dispatch_async(dispatch_get_main_queue(), ^{
                    [self.navigationController setNavigationBarHidden:NO animated:NO];
                    self.selfNavigationBarHidden = NO;
                    
                    NSMutableAttributedString *attr = [[NSMutableAttributedString alloc] initWithString:@"内容不存在" attributes:@{NSFontAttributeName : FontWithSize(15), NSForegroundColorAttributeName : UIColor.up_textSecondaryColor}];
                    [self.view showNoDataViewWithImage:ImageWithName(@"note_delete_placeholder") attributedString:attr offsetY:80];
                });
                return;
            }
    
            __weakSelf.detailModel.courseId = [__weakSelf.courseId copy];

            __weakSelf.blackView.backgroundColor = FMZeroColor;
            
            __weakSelf.topView.hidden = NO;
            __weakSelf.topView.model = __weakSelf.detailModel;
            [__weakSelf.topView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.equalTo(@(__weakSelf.topView.viewHeight));
            }];
            
            __weakSelf.bottomView.hidden = NO;
            __weakSelf.bottomView.collected = __weakSelf.detailModel.myCollected.boolValue;
            __weakSelf.bottomView.praised = __weakSelf.detailModel.mySatisfied.boolValue;
            __weakSelf.bottomView.praiseNum = __weakSelf.detailModel.satisfiedNums.integerValue;
            [__weakSelf.bottomView setNeedsLayout];
            [__weakSelf.bottomView layoutIfNeeded];
            
            if (self.isChapterDetail) {  // 章节详情时 修改目录列表播放状态
                [__weakSelf changeCatalogPlayingStatus];
            }
            
            [__weakSelf changeProfileShow];
            
            if (__weakSelf.isFirstRequest) {
                [__weakSelf configPlayer];
                [__weakSelf setupPageView];
            } else {
                [__weakSelf changePageViewFrame];
            }
            if (successBlock) {
                successBlock();
            }
            __weakSelf.isFirstRequest = NO;
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            if ([dic[@"errcode"] isEqualToString:@"-1"]) {
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [self.navigationController popViewControllerAnimated:YES];
                });
            }
        }
    }];
}

#pragma mark - Private
- (void)gotoPay {
    if ([FMHelper checkLoginStatus]) {
        FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
        
        EnablePayModel *enablePayModel = [[EnablePayModel alloc] init];
        enablePayModel.paydesc = [NSString stringWithFormat:@"您当前拥有%ld金币，确认支付", userModel.coin];
        enablePayModel.name = @"金币";
        enablePayModel.num = [NSString stringWithFormat:@"%zd", userModel.coin];
        enablePayModel.type = PaymentTypeCoin;
        enablePayModel.freeDesc = self.isChapterDetail ? self.detailModel.series_info.freeDesc : self.detailModel.freeDesc;
        self.detailModel.enablePayModel = @[enablePayModel];
        NSString *dakaId = self.detailModel.series_info ? self.detailModel.series_info.bignameDto.userId : self.detailModel.bignameDto.userId;
        NSString *price = self.detailModel.series_info ? self.detailModel.series_info.coursePrice : self.detailModel.coursePrice;
        NSString *title = self.detailModel.series_info ? self.detailModel.series_info.coursePrice : self.detailModel.courseTitle;
        WEAKSELF;
        enablePayModel.consumeType = self.detailModel.series_info ? 7 : 6;
        enablePayModel.contentId = self.isChapterDetail ? self.detailModel.seriesId.integerValue : self.courseId.integerValue;
        enablePayModel.bignameId = self.detailModel.bignameDto.userId;
        [[FMPayTool payTool] judgeConfirmOrderStatusWithDakaId:dakaId certCode:self.detailModel.series_info.bignameDto.certCode clickView:nil confirmOperation:^{
            [PaymentView showWithEnablePayModel:enablePayModel payPrice:price productName:title bottomReminder:@"注：已付费的产品和内容，不会重复扣费" payAction:^(EnablePayModel *selectedModel) {
                [__weakSelf httpForPayAlbum:[NSString stringWithFormat:@"%zd", selectedModel.type] goodsId:selectedModel.couponId usePoints:selectedModel.usePoints];
            } dismissBlock:^{
            }];
        }];
    }
}

// 支付
- (void)httpForPayAlbum:(NSString *)payType goodsId:(NSString *)goodsId usePoints:(NSInteger)usePoints {
    if (self.detailModel.series_info) {
        // 系列课程支付
        [self albumCoursePayWithType:payType goodsId:goodsId usePoints:usePoints];
    } else {
        // 单个课程支付
        [self singleCoursePayWithType:payType goodsId:goodsId usePoints:usePoints];
    }
}

/// 系列课程支付
- (void)albumCoursePayWithType:(NSString *)payType goodsId:(NSString *)goodsId usePoints:(NSInteger)usePoints {
    //系列课程支付
    WEAKSELF;
    [HttpRequestTool payCourseAlbumWithAlbumId:self.detailModel.series_info.courseId type:payType goodsId:goodsId usePoints:usePoints start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD dismiss];
            FMPaySuccessPopView *popView = [[FMPaySuccessPopView alloc] init];
            popView.jumpIndex = 3;
            [popView show];
            
            __weakSelf.detailModel.authority = @"1";
            __weakSelf.detailModel.series_info.authority = @"1";
            for (ChaptersListItem *model in self.detailModel.series_info.chaptersList) {
                model.authority = @"1";
            }
            
            __weakSelf.topView.model = __weakSelf.detailModel;
            [__weakSelf.topView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.equalTo(@(__weakSelf.topView.viewHeight));
            }];
            [__weakSelf changePageViewFrame];
            
            [[NSNotificationCenter defaultCenter] postNotificationName:kAlbumPaySuccess object:nil userInfo:@{@"albumId":self.detailModel.series_info.courseId}];
            
            [HttpRequestTool queryDakaCoinsAndPointsStart:nil failure:nil success:nil];
        } else {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            });
        }
    }];
}

/// 单课程支付
- (void)singleCoursePayWithType:(NSString *)payType goodsId:(NSString *)goodsId usePoints:(NSInteger)usePoints {
    WEAKSELF
    [HttpRequestTool payCourseSingleWithCourseId:self.detailModel.courseId type:payType goodsId:goodsId usePoints:usePoints start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD dismiss];
            FMPaySuccessPopView *popView = [[FMPaySuccessPopView alloc] init];
            popView.jumpIndex = 3;
            [popView show];
            
            // 支付成功，刷新播放器显示
            __weakSelf.detailModel.authority = @"1";
            __weakSelf.detailModel.tryPlaySecond = @"0";
            __weakSelf.currentPalyStatus = 0;
            [FMPlayerManager shareManager].player.currentPlayerManager.view.hidden = NO;
            [[FMPlayerManager shareManager].player.currentPlayerManager.view.superview bringSubviewToFront:[FMPlayerManager shareManager].player.currentPlayerManager.view];
            
            __weakSelf.detailModel.playStatus = self.currentPalyStatus;
            __weakSelf.topView.model = self.detailModel;
            [__weakSelf.topView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.equalTo(@(self.topView.viewHeight));
            }];
            [__weakSelf changePageViewFrame];
            
            [HttpRequestTool queryDakaCoinsAndPointsStart:nil failure:nil success:nil];
        } else {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            });
        }
    }];
}

- (void)gotoShare {
    CourseAlbumDetailModel *model = [[CourseAlbumDetailModel alloc] init];
    model.title = self.detailModel.series_info.courseTitle;
    model.profile = self.detailModel.series_info.bignameDto.userProfiles;
    model.courseId = self.detailModel.series_info.courseId;
    [FMShareHelper shareCourseAlbumDetailWithModel:model];
}

-(void)setupPageView {
    if (!self.isChapterDetail) {
        [self addChildVC];
        return;
    }
    
    [self.pageTitleView removeFromSuperview];
    [self.pageContentCollectionView removeFromSuperview];
    self.pageTitleView = nil;
    self.pageContentCollectionView = nil;

    SGPageTitleViewConfigure *configure = [SGPageTitleViewConfigure pageTitleViewConfigure];
    configure.titleColor = UIColor.up_textSecondaryColor;
    configure.titleFont = FontWithSize(17.0);
    configure.titleSelectedColor = UIColor.up_textPrimaryColor;
    configure.titleSelectedFont = BoldFontWithSize(17.0);
    configure.indicatorStyle = SGIndicatorStyleFixed;
    configure.indicatorColor = FMNavColor;
    configure.indicatorFixedWidth = 18;
    configure.indicatorHeight = 3;
    configure.indicatorCornerRadius = 1.5;
    configure.titleAdditionalWidth = 30;
    configure.equivalence = YES;
    configure.showBottomSeparator = YES;
    configure.bottomSeparatorColor = UIColor.fm_sepline_color;
    
    self.pageTitleView = [SGPageTitleView pageTitleViewWithFrame:CGRectMake(0, self.kTopViewTop + self.topView.viewHeight, UI_SCREEN_WIDTH, 45) delegate:self titleNames:@[@"简介", @"目录"] configure:configure];
    self.pageTitleView.backgroundColor = UIColor.up_contentBgColor;
    [self.view addSubview:self.pageTitleView];
    self.pageContentCollectionView = [[SGPageContentCollectionView alloc] initWithFrame:CGRectMake(0, self.kTopViewTop + self.topView.viewHeight + 45, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT - (self.kTopViewTop + self.topView.viewHeight + 45 +  DetailBottomViewHeight)) parentVC:self childVCs:[self addChildVCs]];
    self.pageContentCollectionView.delegatePageContentCollectionView = self;
    [self.view addSubview:self.pageContentCollectionView];
    // 处理侧滑返回失效
    [self.pageContentCollectionView.collectionView.panGestureRecognizer requireGestureRecognizerToFail:self.navigationController.interactivePopGestureRecognizer];    
}

- (void)changePageViewFrame {
    if (!self.isChapterDetail) {
        CourseDetailProfileViewController *vc = self.childViewControllers.firstObject;
        vc.courseType = self.isChapterDetail ? SeriesCourseType : ExcellentCourseType;
        vc.detailModel = self.detailModel;
        vc.view.frame = CGRectMake(0, self.kTopViewTop + self.topView.viewHeight, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT-(self.kTopViewTop + self.topView.viewHeight + DetailBottomViewHeight));
        [vc.tableView reloadData];
        return;
    }
    
    self.pageTitleView.frame = CGRectMake(0, self.kTopViewTop + self.topView.viewHeight, UI_SCREEN_WIDTH, 45);
    self.pageContentCollectionView.frame = CGRectMake(0, self.kTopViewTop + self.topView.viewHeight + 45, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT - (self.kTopViewTop + self.topView.viewHeight + 45 +  DetailBottomViewHeight));
}

- (void)addChildVC {
    for (UIViewController *subVc in self.childViewControllers) {
        [subVc.view removeFromSuperview];
        [subVc removeFromParentViewController];
    }

    WEAKSELF
    CourseDetailProfileViewController *vc1 = [[CourseDetailProfileViewController alloc] init];
    vc1.detailModel = self.detailModel;
    vc1.courseType = self.isChapterDetail ? SeriesCourseType : ExcellentCourseType;
    vc1.commentCellReplyBlock = ^(FMCommentModel *commentModel) {
        [__weakSelf courseCommentReply:commentModel];
    };
    [self.view addSubview:vc1.view];
    vc1.view.frame = CGRectMake(0, self.kTopViewTop + self.topView.viewHeight, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT - (self.kTopViewTop + self.topView.viewHeight + DetailBottomViewHeight));
    [self addChildViewController:vc1];
}

- (NSArray *)addChildVCs {
    for (UIViewController *subVc in self.childViewControllers) {
        [subVc.view removeFromSuperview];
        [subVc removeFromParentViewController];
    }
    
    WEAKSELF
    CourseDetailProfileViewController *vc1 = [[CourseDetailProfileViewController alloc] init];
    vc1.detailModel = self.detailModel;
    vc1.courseType = self.isChapterDetail ? SeriesCourseType : ExcellentCourseType;
    vc1.commentCellReplyBlock = ^(FMCommentModel *commentModel) {
        [__weakSelf courseCommentReply:commentModel];
    };
    [self addChildViewController:vc1];
    
    CourseDetailCatalogViewController *vc2 = [[CourseDetailCatalogViewController alloc] init];
    vc2.courseId = self.courseId;
    vc2.detailModel = self.detailModel;
    vc2.share = ^{
        [__weakSelf gotoShare];
    };
    vc2.pay = ^{
        [__weakSelf gotoPay];
    };
    vc2.changePlayCourse = ^(NSString *courseId) {
        if ([__weakSelf.courseId isEqualToString:courseId]) {
            return;
        }        
        __weakSelf.courseId = courseId;

        // 切换系列课程章节时 更新简介信息章节评论
        [vc1 updateCommentData:courseId];

        [__weakSelf requestCourseDetailWithSuccess:^{
            [[FMPlayerManager shareManager].player playTheIndexPath:nil assetURL:[NSURL URLWithString:__weakSelf.detailModel.courseVideo]];
            [__weakSelf.controlView showTitle:@""
                         coverURLString:__weakSelf.detailModel.courseImg
                         fullScreenMode:ZFFullScreenModeLandscape];
            // 配置画中画
            [[FMAppDelegate shareApp] configPiP:[FMPlayerManager shareManager].playerManager.avPlayerLayer];
        }];
    };
    [self addChildViewController:vc2];

    return self.childViewControllers;
}

/// 修改目录列表播放状态
- (void)changeCatalogPlayingStatus {
    
    self.controlView.player.currentPlayerManager.rate = 1.0f;
    
    CourseDetailCatalogViewController *vc = self.childViewControllers.lastObject;
    vc.detailModel = self.detailModel;
    if (!vc.chaptersArray.count) {
        return;
    }
    for (ChaptersListItem *model in vc.chaptersArray) {
        if ([model.courseId isEqualToString:self.courseId]) {
            model.playing = YES;
        } else {
            model.playing = NO;
        }
    }
    [vc.tableView reloadData];
}

/// 修改简介显示
- (void)changeProfileShow {
    if (!self.isChapterDetail) {
        return;
    }
    CourseDetailProfileViewController *vc = self.childViewControllers.firstObject;
    vc.detailModel = self.detailModel;
    vc.courseType = self.isChapterDetail ? SeriesCourseType : ExcellentCourseType;
    [vc.tableView reloadData];
}

- (void)courseCommentReply:(FMCommentModel *)commentModel {
    UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
    self.commentView.frame = keyWindow.bounds;
    [keyWindow addSubview:self.commentView];
    self.commentView.publisLb.text = [NSString stringWithFormat:@"回复:%@", commentModel.commenterName];

    WEAKSELF;
    self.commentView.publishBlock = ^(NSString *content){
        NSInteger courseType = __weakSelf.isChapterDetail ? SeriesCourseType : ExcellentCourseType;
        [HttpRequestTool courseCommentPublishWithCourseId:__weakSelf.detailModel.courseId CourseType:courseType commentLev:@"2" commentid:commentModel.commentId commentContent:content start:^{
            [SVProgressHUD show];
        } failure:^{
            [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        } success:^(NSDictionary *dic) {
            if ([dic[@"status"] isEqualToString:@"1"]) {
                [SVProgressHUD showSuccessWithStatus:@"发布成功！审核通过后展示"];
                __weakSelf.commentView.textView.text = @"";
                
//                CourseDetailProfileViewController *vc = __weakSelf.childViewControllers.firstObject;
//                vc.courseType = courseType;
//                [vc addReplyWithContent:content commentId:[NSString stringWithFormat:@"%@", dic[@"data"][@"id"]] commentCell:cell];
            } else {
                [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            }
        }];
    };
}

#pragma mark -- 播放器相关
// 点击出现视频播放器
- (void)showVideoPlayer {
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(networkChanged) name:kNetworkStatusChangedNotification object:nil];

    if ([FMPlayerManager shareManager].player) {
        [FMPlayerManager shareManager].player.currentPlayerManager.view.hidden = NO;
        self.controlView.portraitControlView.slider.isdragging = NO;
        self.controlView.portraitControlView.slider.isdragging = NO;
    }
    [self showVideoPlayerInView:self.topView.videoImgV];
}

- (void)showVideoPlayerInView:(UIView *)view {
    if (self.detailModel.courseVideo.length > 0) {
        self.controlView.prepareShowControlView = YES;
        
        [FMPlayerManager shareManager].courseModel = self.detailModel;
        [FMPlayerManager play];
        [self.controlView showTitle:@""
                     coverURLString:self.detailModel.courseImg
                     fullScreenMode:ZFFullScreenModeLandscape];
        WEAKSELF
        if (self.detailModel.tryPlaySecond.integerValue > 0 && self.detailModel.authority.integerValue != 1) {
            [FMPlayerManager shareManager].player.playerPlayTimeChanged = ^(id<ZFPlayerMediaPlayback>  _Nonnull asset, NSTimeInterval currentTime, NSTimeInterval duration) {
                if (__weakSelf.detailModel.authority.integerValue != 1) {
                    if (__weakSelf.detailModel.tryPlaySecond.integerValue > 0) { //可以试看
                        if (floorf(currentTime) >= __weakSelf.detailModel.tryPlaySecond.integerValue  || __weakSelf.currentPalyStatus == 2) { //试看结束
                            __weakSelf.currentPalyStatus = 2;
                            [[FMPlayerManager shareManager].player seekToTime:__weakSelf.detailModel.tryPlaySecond.integerValue completionHandler:nil];
                            [FMPlayerManager shareManager].player.currentPlayerManager.view.hidden = YES;
                            [[FMPlayerManager shareManager].player.currentPlayerManager pause];
                            if ([FMPlayerManager shareManager].player.isFullScreen) {
                                [[FMPlayerManager shareManager].player enterFullScreen:NO animated:YES];
                            }
                            __weakSelf.detailModel.playStatus = __weakSelf.currentPalyStatus;
                            
                            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                                [__weakSelf layoutTopView];
                                [__weakSelf changePageViewFrame];
                            });
                        }
                    }
                }
            };
            
            [FMPlayerManager shareManager].player.orientationDidChanged = ^(ZFPlayerController * _Nonnull player, BOOL isFullScreen) {
                if (__weakSelf.detailModel.tryPlaySecond.integerValue > 0 &&  __weakSelf.detailModel.authority.integerValue != 1) { //可以试看
                    if (__weakSelf.currentPalyStatus == 2) { //试看结束
                        if (isFullScreen) {
                            [[FMPlayerManager shareManager].player enterFullScreen:NO animated:YES];
                        }
                    }
                }
            };
        }
    }
}

- (void)layoutTopView {
    self.topView.model = self.detailModel;
    [self.topView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@(self.topView.viewHeight));
    }];
}

//配置播放器
- (void)configPlayer {
    
    if (![[FMPlayerManager shareManager].courseModel.courseId isEqualToString:self.courseId]) {
        [FMPlayerManager dismissGlobalVideoPlayViewWithDestroyPlayer];
    }
    [FMPlayerManager shareManager];
    [FMPlayerManager configZFPlayerContainerView:self.topView.videoImgV controlView:self.controlView videoUrl:self.detailModel.courseVideo];
    [FMPlayerManager shareManager].player.currentPlayerManager.view.hidden = NO;
    self.controlView.portraitControlView.slider.isdragging = NO;
    self.controlView.portraitControlView.slider.isdragging = NO;
    
    [FMPlayerManager shareManager].courseModel = self.detailModel;
    
    if(self.isChapterDetail) {
        if (self.detailModel.authority.integerValue != 1) {
            self.controlView.landScapeControlView.isShowSpeed = self.detailModel.isTryPlay.integerValue > 0;
        } else {
            self.controlView.landScapeControlView.isShowSpeed = self.detailModel.authority.integerValue == 1;
        }
        
    } else {
        // 有权限观看时 显示倍速按钮
        self.controlView.landScapeControlView.isShowSpeed = self.detailModel.authority.integerValue == 1;
    }
    // 配置小窗播放按钮
    self.controlView.portraitControlView.isShowSmallViewPlayBtn = self.detailModel.authority.integerValue == 1;
    [self.controlView.portraitControlView layoutSubviews];
    
}

#pragma mark -- lazy
- (CourseDetailTopView *)topView {
    if (!_topView) {
        _topView = [[CourseDetailTopView alloc] init];
        _topView.delegate = self;
    }
    
    return _topView;
}

- (UIButton *)backBtn {
    if (!_backBtn) {
        _backBtn = [[UIButton alloc] initWithFrame:CGRectZero font:nil normalTextColor:nil backgroundColor:FMClearColor title:nil image:ImageWithName(@"return") target:self action:@selector(back)];
        UI_View_Radius(_backBtn, 2.0);
        _backBtn.lz_touchAreaInsets = UIEdgeInsetsMake(-10, -10, -10, -10);
    }
    
    return _backBtn;
}

- (FMDetailBottomView *)bottomView {
    if (!_bottomView) {
        _bottomView = [[FMDetailBottomView alloc] init];
        _bottomView.showType = FMDetailBottomShowTypeShare | FMDetailBottomShowTypeCollect | FMDetailBottomShowTypePraise;
        FMTaskConfigModel *taskConfig = [FMUserDefault getUnArchiverDataForKey:AppInit_Task];
        if (taskConfig.taskDic[@(FMTaskTypeComment)].isEnable) {
            [_bottomView.commentBtn setTitle:[NSString stringWithFormat:@"发表精彩评论免费获取%zd金币", (taskConfig.taskDic[@(FMTaskTypeComment)]).taskAward] forState:UIControlStateNormal];
        } else {
            [_bottomView.commentBtn setTitle:@"发表精彩评论" forState:UIControlStateNormal];
        }
    }
    return _bottomView;
}

- (FMCommentView *)commentView {
    if (!_commentView) {
        _commentView = [[FMCommentView alloc] init];
        _commentView.placeholderText = @"在这里输入内容...";
    }
    return _commentView;
}

- (ZFPlayerControlView *)controlView {
    if (!_controlView) {
        _controlView = [[ZFPlayerControlView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_WIDTH*9/16)];
        _controlView.activity.center = _controlView.center;
        _controlView.prepareShowLoading = YES;
        _controlView.prepareShowControlView = YES;
        WEAKSELF
        _controlView.portraitControlView.smallBtnClickBlock = ^{
            [FMPlayerManager showGlobalVideoPlayView];
            /// 点击小窗口播放后 定位到首页
            [ProtocolJump jumpWithUrl:@"qcyzt://home"];
            [__weakSelf.navigationController popViewControllerAnimated:YES];
        };
    }
    return _controlView;
}

@end
