//
//  CourseAlbumDetailViewController.m
//  QCYZT
//
//  Created by 涂威 on 2018/2/5.
//  Copyright © 2018年 sdcf. All rights reserved.
//

#import "CourseAlbumDetailViewController.h"
#import "CourseDetailViewController.h"
#import "CourseAlbumDetailModel.h"
#import "CourseAlbumInfoCell.h"
#import "FMDetailHtmlCell.h"
#import "CourseCatalogCell.h"
#import "FMPayTool.h"
#import "EnablePayModel.h"
#import "PaymentView.h"
#import "FMShareHelper.h"
#import "CourseAlbumBottomView.h"
#import "HTMLParseTool.h"
#import "HttpRequestTool+Pay.h"
#import "HMSegmentedControl.h"
#import "HttpRequestTool+Course.h"
#import "FMPaySuccessPopView.h"

#define SectionHeaderHeight 40


@interface CourseAlbumDetailViewController () <UITableViewDelegate, UITableViewDataSource, CourseAlbumBottomViewDelegate>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) CourseAlbumBottomView *bottomView;
@property (nonatomic, strong) UIView *headerView;
@property (nonatomic, strong) UIImageView *headImageView;
@property (nonatomic, strong) HMSegmentedControl *segmentedCtr;  // SGPageTitleView在iOS16上这里有问题

@property (nonatomic, strong) CourseAlbumDetailModel *model;
@property (nonatomic, strong) FMDetailHtmlModel *htmlModel;

@property (nonatomic, assign) BOOL needsRefresh;
@property (nonatomic, assign) BOOL isFirstLoad;

@end

@implementation CourseAlbumDetailViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    
    [self.view addSubview:self.bottomView];
    [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(@(0));
        make.height.equalTo(@(CourseAlbumBottomHeight));
    }];
    self.bottomView.hidden = YES;
    
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(@(0));
        make.bottom.equalTo(self.bottomView.mas_top);
    }];
    self.tableView.hidden = YES;
    
    self.isFirstLoad = YES;
    
    [self requestDataWithFinishBlock:nil];
    [FMHelper addLoginAndLogoutNotificationWithObserver:self selector:@selector(handleLoginStatusNotification) monitorAuthLogin:NO];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleLoginStatusNotification) name:kRechargeSuccess object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(albumPaySuccess:) name:kAlbumPaySuccess object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(albumShared) name:kShareCourseAlbum object:nil];
}

- (void)configShareNav {
    UIImageView *shareV = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 24, 23)];
    shareV.image = FMImgInBundle(@"导航/亮黑暗灰分享");
    shareV.userInteractionEnabled = YES;
    [shareV addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(navShareBtnClick)]];
    UIBarButtonItem *shareItem = [[UIBarButtonItem alloc] initWithCustomView:shareV];
    self.navigationItem.rightBarButtonItem = shareItem;
}

- (void)navShareBtnClick{
    if ([self.model.payType isEqualToString:@"1"]) {
        if ([FMHelper checkLoginStatus]) {
            [FMShareHelper shareCourseAlbumDetailWithModel:self.model];
        }
    } else {
        [FMShareHelper shareCourseAlbumDetailWithModel:self.model];
    }
}

- (void)backArrowClicked {
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    if (self.needsRefresh) {
        self.needsRefresh = NO;
        [self requestDataWithFinishBlock:^{
            [self scrollViewDidScroll:self.tableView];
        }];
    }
    
    [self configNavWhiteColorWithCloseSEL:@selector(backArrowClicked)];
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    CGFloat offsetY = scrollView.contentOffset.y;
    CGFloat headerImageHeight = self.model.adsImage.length ? ceil(165.f * UI_RELATIVE_WIDTH) : 0;
    if (offsetY - headerImageHeight >= -(1e-3)) {
        self.segmentedCtr.y = 0;
        [self.view addSubview:self.segmentedCtr];
    } else {
        self.segmentedCtr.y = self.headImageView.bottom;
        [self.headerView addSubview:self.segmentedCtr];
    }
    
    CGRect rect = [self.tableView rectForHeaderInSection:2];
    if (offsetY - (CGRectGetMaxY(rect)-self.segmentedCtr.height) >= -(1e-3)) {
        [self.segmentedCtr setSelectedSegmentIndex:1 animated:YES];
    } else {
        [self.segmentedCtr setSelectedSegmentIndex:0 animated:NO];
    }
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 3;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (section == 2) {
        return self.model.chaptersList.count;
    } else {
        return 1;
    }
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell;
    if (indexPath.section == 0) {
        cell = [self configTableView:tableView albumInfoAtIndexPath:indexPath];
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
    } else if (indexPath.section == 1) {
        cell = [self configTableView:tableView webCellAtIndexPath:indexPath];
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
    } else if (indexPath.section == 2) {
        cell = [self configTableView:tableView catalogAtIndexPath:indexPath];
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
    }
    return cell;
}

- (CourseAlbumInfoCell *)configTableView:(UITableView *)tableView albumInfoAtIndexPath:(NSIndexPath *)indexPath {
    CourseAlbumInfoCell *cell = [tableView reuseCellClass:[CourseAlbumInfoCell class] indexPath:indexPath];
    cell.model = self.model;
    return cell;
}

- (FMDetailHtmlCell *)configTableView:(UITableView *)tableView webCellAtIndexPath:(NSIndexPath *)indexPath {
    FMDetailHtmlCell *cell = [tableView reuseCellClass:[FMDetailHtmlCell class] indexPath:indexPath];
    cell.model = self.htmlModel;
    return cell;
}

- (CourseCatalogCell *)configTableView:(UITableView *)tableView catalogAtIndexPath:(NSIndexPath *)indexPath {
    CourseCatalogCell *cell = [tableView reuseCellClass:[CourseCatalogCell class] indexPath:indexPath];
    cell.model = self.model.chaptersList[indexPath.row];
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
    
    if (indexPath.section == 2) {
        ChaptersListItem *model = self.model.chaptersList[indexPath.row];
        // 单课程 可以试看，无需判断登录, 直接跳转详情
        if (model.isTryPlay.integerValue > 0) {
            CourseDetailViewController *vc = [[CourseDetailViewController alloc] init];
            vc.courseId = model.courseId;
            vc.isChapterDetail = YES;
            [self.navigationController pushViewController:vc animated:YES];
        } else {
            if ([self.model.payType isEqualToString:@"0"]) { // 系列免费
                if (self.model.ex_needVIP) { 
                    if ([FMHelper checkLoginStatus]) { // 判断登录
                        // 显示弹窗
                        [FMHelper showVIPAlertWithType:VIPReadTypeCourseSeries needVip:-1 authority:self.model.authority price:[self.model.price floatValue] showAlert:YES clickSure:nil clickCancel:nil clickSureDismiss:YES];
                    }
                } else { // 系列不需要升级VIP，跳转详情
                    CourseDetailViewController *vc = [[CourseDetailViewController alloc] init];
                    vc.courseId = model.courseId;
                    vc.isChapterDetail = YES;
                    [self.navigationController pushViewController:vc animated:YES];
                }
            } else if ([self.model.payType isEqualToString:@"1"]) { // 系列分享可看
                if ([FMHelper checkLoginStatus]) { // 判断登录
                    if (self.model.hasShared == 1) { // 已经分享过系列，跳转详情
                        CourseDetailViewController *vc = [[CourseDetailViewController alloc] init];
                        vc.courseId = model.courseId;
                        vc.isChapterDetail = YES;
                        [self.navigationController pushViewController:vc animated:YES];
                    } else { // 没有分享过
                        if (self.model.ex_needVIP) {
                            // 显示弹窗
                            [FMHelper showVIPAlertWithType:VIPReadTypeCourseSeries needVip:-1 authority:self.model.authority price:[self.model.price floatValue] showAlert:YES clickSure:nil clickCancel:nil clickSureDismiss:YES];
                        } else { // 去分享系列
                            ShowConfirm([FMHelper getCurrentVC].navigationController, nil, @"分享给好友后就能免费查看系列课程噢^_^", @"取消", @"确定", ^{
                            }, ^{
                                [FMShareHelper shareCourseAlbumDetailWithModel:self.model];
                            });
                        }
                    }
                }
            } else if ([self.model.payType isEqualToString:@"2"]) { // 系列需要付费
                if ([FMHelper checkLoginStatus]) { // 判断登录
                    if (self.model.authority.integerValue == 1) { // 课程有权限，跳转详情
                        CourseDetailViewController *vc = [[CourseDetailViewController alloc] init];
                        vc.courseId = model.courseId;
                        vc.isChapterDetail = YES;
                        [self.navigationController pushViewController:vc animated:YES];
                    } else {
                        if (self.model.ex_needVIP) {
                            // 显示弹窗
                            [FMHelper showVIPAlertWithType:VIPReadTypeCourseSeries needVip:-1 authority:self.model.authority price:[self.model.price floatValue] showAlert:YES clickSure:nil clickCancel:nil clickSureDismiss:YES];
                        } else { // 去购买系列
                            ShowConfirm([FMHelper getCurrentVC].navigationController, nil, @"购买系列后即可观看全部课程，是否立即购买？", @"取消", @"确定", ^{
                            }, ^{
                                [self gotoPay];
                            });
                        }
                    }
                }
            }
        }
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == 0) {
        return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([CourseAlbumInfoCell class]) configuration:^(CourseAlbumInfoCell *cell) {
            cell.model = self.model;
        }];
    } else if (indexPath.section == 1) {
        return ceil(self.htmlModel.textHeight) + 20;
    } else if (indexPath.section == 2) {
        CGFloat height = [tableView fd_heightForCellWithIdentifier:NSStringFromClass([CourseCatalogCell class]) configuration:^(CourseCatalogCell *cell) {
            cell.model = self.model.chaptersList[indexPath.row];
        }];
        return (height < 120.0f) ? 120.0f : height;
    }
    
    return CGFLOAT_MIN;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    if (section == 2) {
        return SectionHeaderHeight;
    }
    return CGFLOAT_MIN;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    if (section == 2) {
        UIView *view = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, SectionHeaderHeight)];
        view.backgroundColor = UIColor.up_contentBgColor;
        UILabel *textLabel = [[UILabel alloc] init];
        textLabel.font = BoldFontWithSize(18.f);
        textLabel.text = @"课程目录";
        textLabel.textColor = UIColor.up_textPrimaryColor;
        [textLabel sizeToFit];
        textLabel.left = 15.f;
        textLabel.top = 15.f;
        [view addSubview:textLabel];
        
        UILabel *statusLabel = [UILabel new];
        [view addSubview:statusLabel];
        [statusLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(@-15);
            make.centerY.equalTo(view);
        }];
        statusLabel.font = [UIFont systemFontOfSize:14.0f];
        statusLabel.textColor = FMNavColor;
        statusLabel.text = (self.model.chaptersList.count == self.model.lesson.integerValue) ? @"已完结" : @"更新中";
        
        return view;
    } else {
        return nil;
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    if (section == 1) {
        return 8;
    }
    
    return CGFLOAT_MIN;
}

#pragma mark - CourseAlbumBottomViewDelegate
- (void)courseAlbumBottomViewBtnClick:(UIButton *)btn {
    switch (btn.tag) {
        case CourseAlbumBottomViewBtnTagShare:
        {
            if ([FMHelper checkLoginStatus]) {
                [FMShareHelper shareCourseAlbumDetailWithModel:self.model];
            }
        }
            break;
            
        case CourseAlbumBottomViewBtnTagWatch:
        {
            for (NSInteger i = 0; i < self.model.chaptersList.count; i++) {
                ChaptersListItem *model = self.model.chaptersList[i];
                if (model.isTryPlay.integerValue > 0) {
                    CourseDetailViewController *vc = [[CourseDetailViewController alloc] init];
                    vc.isChapterDetail = YES;
                    vc.courseId = model.courseId;
                    [self.navigationController pushViewController:vc animated:YES];
                    break;
                }
            }
        }
            break;
            
        case CourseAlbumBottomViewBtnTagPay:
        {
            if ([FMHelper checkLoginStatus]) {
                if (self.model.ex_needVIP) {
                    [FMHelper showVIPAlertWithType:VIPReadTypeCourseSeries needVip:-1 authority:self.model.authority price:[self.model.price floatValue] showAlert:YES clickSure:nil clickCancel:nil clickSureDismiss:YES];
                } else {
                    [self gotoPay];
                }
            }
        }
            break;
            
        default:
            break;
    }
}

#pragma mark - NSNotification
- (void)handleLoginStatusNotification {
    self.needsRefresh = YES;
}

- (void)albumPaySuccess:(NSNotification *)noti {
    NSDictionary *info = noti.userInfo;
    if ([info[@"albumId"] isEqualToString:self.model.courseId]) {
        self.model.authority = @"1";
        for (ChaptersListItem *model in self.model.chaptersList) {
            model.authority = @"1";
        }
        [self changeBottomView];
        [self.tableView reloadData];
        [self changeTableFooterView];
    }
}

- (void)albumShared {
    if ([FMHelper isLogined]) {
        [HttpRequestTool shareAlbumWithAlbumId:self.model.courseId start:^{
        } failure:^{
        } success:^(NSDictionary *dic) {
            if ([dic[@"status"] isEqualToString:@"1"]) {
                self.model.hasShared = 1;
                self.model.authority = @"1";
                [self changeBottomView];
                
                [self.tableView reloadData];
                [self changeTableFooterView];
            }
        }];
    }
}

#pragma mark - HTTP
- (void)requestDataWithFinishBlock:(void(^)())finishBlock {
    WEAKSELF;
    
    [HttpRequestTool getCourseDetailWithcourseId:[self.albumId integerValue] CourseType:SeriesCourseType start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD dismiss];
        [__weakSelf.view showReloadNetworkViewWithBlock:^{
            [__weakSelf requestDataWithFinishBlock:finishBlock];
        }];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD dismiss];
            __weakSelf.tableView.hidden = NO;
            __weakSelf.bottomView.hidden = NO;
            
            __weakSelf.model = [CourseAlbumDetailModel modelWithJSON:dic[@"data"]];
            __weakSelf.model.albumId = self.albumId;
            [__weakSelf createHtmlModel];
            
            for (NSInteger i = 0; i < __weakSelf.model.chaptersList.count; i++){
                ChaptersListItem *model = __weakSelf.model.chaptersList[i];
                model.index = i;
            }
            CGFloat headerImageHeight = __weakSelf.model.adsImage.length ? ceil(165.f * UI_RELATIVE_WIDTH) : 0;
            [__weakSelf.headImageView sd_setImageWithURL:[NSURL URLWithString:__weakSelf.model.adsImage] placeholderImage:[UIImage imageNamed:@"sphc_placeholder"]];
            __weakSelf.tableView.tableHeaderView.height =  headerImageHeight + UI_SegmentControl_Height;
            __weakSelf.headImageView.height = headerImageHeight;
            __weakSelf.segmentedCtr.y = headerImageHeight;
            
            __weakSelf.title = __weakSelf.model.title;
            [__weakSelf changeBottomView];
            
            [__weakSelf.tableView reloadData];
            dispatch_async(dispatch_get_main_queue(), ^{
                if (finishBlock) {
                    finishBlock();
                }
            });
            [__weakSelf changeTableFooterView];
            // 有数据后 在配置分享按钮
            [__weakSelf configShareNav];
        } else {
            [SVProgressHUD dismiss];
            [__weakSelf.view showReloadNetworkViewWithBlock:^{
                [__weakSelf requestDataWithFinishBlock:finishBlock];
            }];
        }
    }];
}

- (void)createHtmlModel {
    FMDetailHtmlModel *model = [[FMDetailHtmlModel alloc] init];
    self.htmlModel = model;
    
    model.HTMLString = self.model.profileHtml;
    
    [HTMLParseTool shareInstance].textFontSize = 16.0f;
    [HTMLParseTool shareInstance].lineSpacing = 8.0f;
    [HTMLParseTool shareInstance].paragraphSpacing = 15.0f;
    [HTMLParseTool shareInstance].alignment = NSTextAlignmentLeft;
    [HTMLParseTool shareInstance].imgHeadIndent = NO;
    [HTMLParseTool shareInstance].needSupportBigFont = YES;
    [HTMLParseTool shareInstance].imageWidth = UI_SCREEN_WIDTH - 30;

    WEAKSELF
    __weak typeof(model) __weakModel = model;
    
    [[HTMLParseTool shareInstance] parseWithHTMLString:model.HTMLString contentId:self.model.courseId completeWithAttrStr:^(NSMutableAttributedString *attrStr, NSArray *imgs, BOOL loadCompleted) {
        __weakModel.attrStr = attrStr;
        __weakModel.imgArr = imgs;
        CGSize size = [attrStr boundingRectWithSize:CGSizeMake(UI_SCREEN_WIDTH - 30, CGFLOAT_MAX) options:NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading context:nil].size;
        __weakModel.textHeight = size.height;
        
        [__weakSelf.tableView reloadData];
    }];
}

- (void)httpForPayAlbum:(NSString *)payType goodsId:(NSString *)goodsId usePoints:(NSInteger)usePoints {
    WEAKSELF
    [HttpRequestTool payCourseAlbumWithAlbumId:self.model.courseId type:payType goodsId:goodsId usePoints:usePoints start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD dismiss];
            FMPaySuccessPopView *popView = [[FMPaySuccessPopView alloc] init];
            popView.jumpIndex = 3;
            [popView show];
            
            __weakSelf.model.authority = @"1";
            __weakSelf.model.seriseNoticed = @"1";
            for (ChaptersListItem *model in self.model.chaptersList) {
                model.authority = @"1";
            }
            [__weakSelf changeBottomView];
            [__weakSelf.tableView reloadData];
            [__weakSelf changeTableFooterView];
            
            [HttpRequestTool queryDakaCoinsAndPointsStart:nil failure:nil success:nil];
        } else {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            });
        }
    }];
}


#pragma mark - Private
- (void)changeBottomView {
    self.bottomView.hidden = NO;
    self.bottomView.shareBtn.hidden = NO;
    self.bottomView.watchBtn.hidden = NO;
    self.bottomView.payBtn.hidden = NO;
    if (self.model.ex_needVIP) {
        self.bottomView.shareBtn.hidden = YES;
        BOOL isNeedShowTryWatch = NO;
        for (ChaptersListItem *model in self.model.chaptersList) {
            if (model.isTryPlay.integerValue > 0) {
                isNeedShowTryWatch = YES;
                break;
            }
        }
        if (!isNeedShowTryWatch) {
            self.bottomView.watchBtn.hidden = YES;
        }
//        [self.bottomView.payBtn setTitle:self.model.payText forState:UIControlStateNormal];
    } else {
        if ([self.model.payType isEqualToString:@"2"]) { // 付费可看
            if (self.model.authority.integerValue != 1) { // 没有权限
                [self.bottomView.stackView removeArrangedSubview:self.bottomView.shareBtn];
                self.bottomView.shareBtn.hidden = YES;
                BOOL isNeedShowTryWatch = NO;
                for (ChaptersListItem *model in self.model.chaptersList) {
                    if (model.isTryPlay.integerValue > 0) {
                        isNeedShowTryWatch = YES;
                        break;
                    }
                }
                if (!isNeedShowTryWatch) {
                    self.bottomView.watchBtn.hidden = YES;
                }
                [self.bottomView.payBtn setTitle:self.model.payText forState:UIControlStateNormal];
            } else {
                [self.bottomView mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.height.equalTo(@(UI_SAFEAREA_BOTTOM_HEIGHT));
                }];
                self.bottomView.hidden = YES;
            }
        } else if ([self.model.payType isEqualToString:@"1"]) { // 分享可看
            if (self.model.hasShared != 1) { // 未分享
                self.bottomView.watchBtn.hidden = YES;
                self.bottomView.payBtn.hidden = YES;
                self.bottomView.shareBtn.hidden = NO;
            } else {
                [self.bottomView mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.height.equalTo(@(UI_SAFEAREA_BOTTOM_HEIGHT));
                }];
                self.bottomView.hidden = YES;
            }
        } else {
            [self.bottomView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.equalTo(@(UI_SAFEAREA_BOTTOM_HEIGHT));
            }];
            self.bottomView.hidden = YES;
        }
    }
}

- (void)gotoPay {
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    EnablePayModel *enablePayModel = [[EnablePayModel alloc] init];
    enablePayModel.paydesc = [NSString stringWithFormat:@"您当前拥有%zd金币，确认支付", userModel.coin];
    enablePayModel.name = @"金币";
    enablePayModel.num = [NSString stringWithFormat:@"%zd", userModel.coin];
    enablePayModel.type = PaymentTypeCoin;
    enablePayModel.freeDesc = self.model.freeDesc;
    enablePayModel.consumeType = 7;
    enablePayModel.contentId = self.albumId.integerValue;
    enablePayModel.bignameId = self.model.bignameDto.userId;
    self.model.enablePayModel = @[enablePayModel];
    WEAKSELF
    [[FMPayTool payTool] judgeConfirmOrderStatusWithDakaId:self.model.authorId certCode:self.model.bignameDto.certCode clickView:self.bottomView.payBtn confirmOperation:^{
        [PaymentView showWithEnablePayModel:enablePayModel payPrice:__weakSelf.model.price productName:__weakSelf.model.title bottomReminder:@"注：已付费的产品和内容，不会重复扣费" payAction:^(EnablePayModel *selectedModel) {
            [__weakSelf httpForPayAlbum:[NSString stringWithFormat:@"%zd", selectedModel.type] goodsId:selectedModel.couponId usePoints:selectedModel.usePoints];
        } dismissBlock:^{
            
        }];
    }];
}

- (void)headerTap:(UIGestureRecognizer *)gesture {
    if (self.model.adsLink.length) {
        [ProtocolJump jumpWithUrl:self.model.adsLink];
    }
}

- (void)changeTableFooterView {
    dispatch_async(dispatch_get_main_queue(), ^{
        CGFloat courseAlbumBottomHeight = self.model.authority.integerValue == 1 ?  UI_SAFEAREA_BOTTOM_HEIGHT : CourseAlbumBottomHeight;
        if ([self.tableView rectForSection:2].size.height - SectionHeaderHeight < UI_SCREEN_HEIGHT - UI_SAFEAREA_TOP_HEIGHT - courseAlbumBottomHeight - UI_SegmentControl_Height) {
            UIView *tableFooter = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT - UI_SAFEAREA_TOP_HEIGHT - [self.tableView rectForSection:2].size.height + SectionHeaderHeight - UI_SegmentControl_Height - courseAlbumBottomHeight)];
            self.tableView.tableFooterView = tableFooter;
        } else {
            self.tableView.tableFooterView = nil;
        }
    });
}

#pragma mark - Getter/Setter
- (UITableView *)tableView {
    if (_tableView == nil) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero
                                                  style:UITableViewStyleGrouped
                                               delegate:self
                                             dataSource:self
                                         viewController:self];
        _tableView.backgroundColor = FMClearColor;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        [_tableView registerCellClassArray:@[CourseAlbumInfoCell.class,CourseCatalogCell.class,FMDetailHtmlCell.class]];
        
        _tableView.tableHeaderView = self.headerView;
    }
    return _tableView;
}

- (UIView *)headerView {
    if (_headerView == nil) {
        _headerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, ceil(165.f * UI_RELATIVE_WIDTH) + UI_SegmentControl_Height)];
        [_headerView addSubview:self.headImageView];
        [_headerView addSubview:self.segmentedCtr];
    }
    return _headerView;
}

- (UIImageView *)headImageView {
    if (_headImageView == nil) {
        _headImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, ceil(165.f * UI_RELATIVE_WIDTH))];
        //        _headImageView.contentMode = UIViewContentModeScaleAspectFill;
        //        _headImageView.layer.masksToBounds = YES;
        _headImageView.image = [UIImage imageNamed:@"sphc_placeholder"];
        _headImageView.userInteractionEnabled = YES;
        [_headImageView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(headerTap:)]];
    }
    return _headImageView;
}

- (HMSegmentedControl *)segmentedCtr {
    if (!_segmentedCtr) {
        _segmentedCtr = [[HMSegmentedControl alloc] initWithSectionTitles:@[@"课程简介",@"课程目录"]];
        _segmentedCtr.frame = (CGRect){0,0,UI_SCREEN_WIDTH, UI_SegmentControl_Height};
        _segmentedCtr.selectionIndicatorHeight = 3.0f;
        _segmentedCtr.selectionIndicatorLocation = HMSegmentedControlSelectionIndicatorLocationDown;
        _segmentedCtr.titleTextAttributes = @{NSForegroundColorAttributeName : UIColor.up_textSecondaryColor, NSFontAttributeName : FontWithSize(17)};
        _segmentedCtr.selectedTitleTextAttributes = @{NSForegroundColorAttributeName : UIColor.up_textPrimaryColor, NSFontAttributeName : BoldFontWithSize(17)};
        _segmentedCtr.selectionStyle = HMSegmentedControlSelectionStyleTextWidthStripe;
        _segmentedCtr.selectionIndicatorColor = FMNavColor;
        _segmentedCtr.borderType = HMSegmentedControlBorderTypeBottom;
        _segmentedCtr.borderColor = UIColor.fm_sepline_color;
        _segmentedCtr.borderWidth = 0.5f;
        [_segmentedCtr addTarget:self action:@selector(segmentedControlChangedValue:) forControlEvents:UIControlEventValueChanged];
        _segmentedCtr.backgroundColor = UIColor.up_contentBgColor;
    }
    return _segmentedCtr;
}

- (void)segmentedControlChangedValue:(HMSegmentedControl*)sender {
    if (sender.selectedSegmentIndex == 0) {
        CGRect rect = [self.tableView rectForHeaderInSection:0];
        [self.tableView setContentOffset:CGPointMake(0, CGRectGetMaxY(rect)-sender.height) animated:NO];
    } else if (sender.selectedSegmentIndex == 1) {
        CGRect rect = [self.tableView rectForHeaderInSection:2];
        [self.tableView setContentOffset:CGPointMake(0, CGRectGetMaxY(rect)-sender.height) animated:NO];
    }
}

- (CourseAlbumBottomView *)bottomView {
    if (!_bottomView) {
        _bottomView = [[CourseAlbumBottomView alloc] init];
        _bottomView.delegate = self;
        _bottomView.backgroundColor = UIColor.up_contentBgColor;
    }
    
    return _bottomView;
}

- (void)dealloc {
    FMLog(@"%s", __func__);
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

@end
