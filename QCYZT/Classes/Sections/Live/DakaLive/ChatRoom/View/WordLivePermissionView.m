//
//  WordLivePermissionView.m
//  QCYZT
//
//  Created by shumi on 2022/4/6.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "WordLivePermissionView.h"
#import "FMLiveDetailModel.h"
#import "EnablePayModel.h"
#import "FMPayTool.h"
#import "PaymentView.h"
#import "FMMemberCenterProductViewController.h"
#import "FMPayHandle.h"
@interface WordLivePermissionView ()
@property (nonatomic, strong) UILabel *titleLabel;
@property(nonatomic,strong) UIStackView *detailStackV;

@property (nonatomic, strong) UILabel *subTitleLabel;
@property(nonatomic,strong) UIButton *subDetailBtn;
/// 购买门票/联系投顾开通服务...
@property (nonatomic, strong) UIButton *actionButton;
@property (nonatomic, strong) ZLTagLabel *priceLabel;
@end

@implementation WordLivePermissionView

- (instancetype)init {
    self = [super init];
    if (self) {
        self.backgroundColor = ColorWithHex(0xFFF5D8);
        [self setupUI];
        WEAKSELF;
        [self bk_addObserverForKeyPath:@"subDetailBtn.titleLabel.text" task:^(id  _Nonnull target) {
            __weakSelf.subDetailBtn.hidden = !__weakSelf.subDetailBtn.titleLabel.text.length;
        }];
        [self bk_addObserverForKeyPath:@"priceLabel.text" task:^(id  _Nonnull target) {
            __weakSelf.priceLabel.hidden = !__weakSelf.priceLabel.text.length;
        }];
    }
    return self;
}

-(void)dealloc{
    [self bk_removeAllBlockObservers];
}

-(UIButton *)actionButton{
    if(!_actionButton){
        _actionButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_actionButton setTitleColor:ColorWithHex(0x522212) forState:UIControlStateNormal];
        _actionButton.titleLabel.font = BoldFontWithSize(12);
        WEAKSELF;
        [_actionButton bk_addEventHandler:^(id  _Nonnull sender) {
                [__weakSelf actionButtonClick];
        } forControlEvents:UIControlEventTouchUpInside];
        UI_View_Radius(_actionButton, 15);
        _actionButton.hidden = YES;
        _actionButton.bounds = CGRectMake(0, 0, 103, 30);
        NSArray *colors =  @[(__bridge  id)ColorWithHex(0xFEDE87).CGColor,(__bridge  id)ColorWithHex(0xF3CB5C).CGColor];
        [_actionButton drawCAGradientWithcolors:colors];
        [_actionButton bringSubviewToFront:_actionButton.titleLabel];
    }
    return _actionButton;
}

- (void)setupUI {
    
    UILabel *titleLB = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(15.0) textColor:ColorWithHex(0x930000) backgroundColor:ColorWithHex(0xFFF5D8) numberOfLines:1];
    [self addSubview:titleLB];
    self.titleLabel = titleLB;
    
    UILabel *subTitleLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:ColorWithHex(0x2F170F) backgroundColor:ColorWithHex(0xFFF5D8) numberOfLines:0];
    self.subTitleLabel = subTitleLB;
    [self addSubview:self.detailStackV];
    
    [self addSubview:self.actionButton];
    [self.actionButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.right.equalTo(@(-10));
        make.size.equalTo(@(CGSizeMake(103, 30)));
    }];
    
    ZLTagLabel *priceLB = [[ZLTagLabel alloc] initWithFrame:CGRectZero font:FontWithSize(10) textColor:FMWhiteColor backgroundColor:FMNavColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    priceLB.widthPadding = 12;
    [self addSubview:priceLB];
    [priceLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.actionButton);
        make.bottom.equalTo(self.actionButton.mas_top).offset(6);
        make.height.equalTo(@(17));
    }];
    priceLB.hidden = YES;
    self.priceLabel = priceLB;
}

- (void)setModel:(FMLiveDetailModel *)model {
    _model = model;

    [self oldDataBind];
}


-(void)oldDataBind{
    self.actionButton.hidden = YES;
    self.titleLabel.text = _model.ticketInfo.payText;
    self.subTitleLabel.hidden = YES;
//    self.priceLabel.hidden = YES;
    // 需要权限 (付费或者 需要会员)
    if (_model.ticketInfo.needPay) {
        // 试看期间
        if (_model.tryEndTime > _model.currTime) {
            // 试看期间
            [self.titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(@(10));
                make.left.equalTo(@(12));
                make.height.equalTo(@(21));
            }];
            
            self.subTitleLabel.hidden = NO;
            [self.detailStackV mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(self.titleLabel);
                make.right.mas_equalTo(self.actionButton.mas_left).offset(-10);
                make.top.equalTo(self.titleLabel.mas_bottom).offset(5);
            }];
            
            
            NSString *timeStr = self.model.tryTimeStr;
            
            NSString *actionStr = @"联系顾问开通";
            if (_model.ticketInfo.needVip) { //需要会员
                // 需要会员 如果price>0 则还需要付费
                NSString *payStr = _model.ticketInfo.price > 0 ? @"付费" : @"";
                self.subTitleLabel.text = [NSString stringWithFormat:@"%@可试看,开通后%@看完整直播",timeStr,payStr];
            } else { // 需要付费
                actionStr = @"购买门票";
                self.priceLabel.hidden = NO;
                [self configPriceLabText];
                self.subTitleLabel.text = [NSString stringWithFormat:@"%@可试看,购买后观看完整直播",timeStr];
            }
            self.actionButton.hidden = NO;
            [self.actionButton setTitle:actionStr forState:UIControlStateNormal];
           
        } else {
            // 非试看期间 标题居中，隐藏子标题
            self.subTitleLabel.hidden = YES;
            [self.titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.centerY.centerX.equalTo(self);
            }];
        }
    } else {
        if (self.model.ticketInfo.payText.length > 0) {
            if (![self.model.ticketInfo.payText isEqualToString:@"免费直播"]) {
                self.subTitleLabel.hidden = YES;
                self.priceLabel.hidden = true;
                self.actionButton.hidden = true;
                self.titleLabel.text = self.model.ticketInfo.payText;
            }
            [self.titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.centerY.centerX.equalTo(self);
            }];
        }
    }
}

-(void)configPriceLabText{
    self.priceLabel.text = _model.ticketInfo.priceDescribe;
    CGSize size = [self.priceLabel.text sizeWithFont:FontWithSize(10) andSize:CGSizeMake(CGFLOAT_MAX, 17)];
    [self.priceLabel layerAndBezierPathWithRect:CGRectMake(0, 0, ceil(size.width) + 12, 17) cornerRadii:CGSizeMake(8, 8) byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight | UIRectCornerBottomRight];
    [self.priceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(@(ceil(size.width) + 12));
    }];
}

- (void)actionButtonClick {
    if ([self.actionButton.titleLabel.text isEqualToString:@"联系顾问开通"]) {
        //联系顾问
        [self contactAdviser];
    } else {
        [self payCoin];
    }
    
}

-(void)contactAdviser{
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    if (userModel.hasVip) {
        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:@"weixin://"] options:@{} completionHandler:nil];
    } else {
        NSString *str = [NSString stringWithFormat:@"tel://%@", [FMUserDefault getSeting:AppInit_ContactUs_Phone]];
        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:str] options:@{} completionHandler:nil];
    }
}

-(void)pushToVipBuyPage{
    // 不可单买，开通投顾服务
    FMMemberCenterProductViewController *vc = [[FMMemberCenterProductViewController alloc] init];
    vc.bigcastId = self.model.bignameDto.userId;
    [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
}

-(void)payCoin{
    [FMPayHandle payCoinWithDetailModel:self.model success:^(EnablePayModel * _Nonnull selectedModel) {
        [HttpRequestTool queryDakaCoinsAndPointsStart:nil failure:nil success:nil];

        NSMutableDictionary *dic = [NSMutableDictionary dictionary];
        [dic setObject:[NSNumber numberWithInteger:self.model.liveId] forKey:@"roomid"];
        [dic setObject:@"permissionsUpdate" forKey:@"websocketMsgType"];
        [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationLiveRoomInfoUpdate object:dic];        
    }];
}

-(UIStackView *)detailStackV{
    if(!_detailStackV){
        _detailStackV = [[UIStackView alloc] init];
        _detailStackV.axis = UILayoutConstraintAxisVertical;
        _detailStackV.distribution = UIStackViewDistributionFill;
        _detailStackV.alignment = UIStackViewAlignmentLeading;
        _detailStackV.spacing = 2;
        [@[self.subTitleLabel, self.subDetailBtn] bk_each:^(UIView *  _Nonnull obj) {
            [self.detailStackV addArrangedSubview:obj];
        }];
    }
    return _detailStackV;
}

-(UIButton *)subDetailBtn{
    if(!_subDetailBtn){
        _subDetailBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_subDetailBtn setTitleColor:ColorWithHex(0x0060C1) forState:kNilOptions];
        _subDetailBtn.titleLabel.font = FontWithSize(12);
        WEAKSELF;
        [_subDetailBtn bk_addEventHandler:^(id  _Nonnull sender) {
            [__weakSelf pushToVipBuyPage];
        } forControlEvents:UIControlEventTouchUpInside];
    }
    return _subDetailBtn;
}
@end
