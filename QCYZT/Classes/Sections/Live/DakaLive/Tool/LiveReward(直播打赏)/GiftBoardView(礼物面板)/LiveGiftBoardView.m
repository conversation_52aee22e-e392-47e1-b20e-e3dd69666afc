//
//  LiveGiftBoardView.m
//  QCYZT
//
//  Created by shumi on 2023/5/10.
//  Copyright © 2023 LZKJ. All rights reserved.
//

#import "LiveGiftBoardView.h"
#import "LiveGiftModel.h"
#import "HttpRequestTool+Live.h"
#import "HttpRequestTool+Pay.h"
#import "FMProgressHUD.h"
#import "FMPayTool.h"
#import "FMLiveDetailModel.h"
#import "FMPlayerManager.h"

@interface GiftItemCell : UICollectionViewCell
@property (nonatomic, strong) UIImageView *giftIcon;
@property (nonatomic, strong) UILabel *nameLB;
@property (nonatomic, strong) UILabel *priceLB;
@property (nonatomic, strong) UILabel *sendLB;
/// 当前选中
@property (nonatomic, assign) BOOL isSelected;
/// 点击发送回调
@property (nonatomic,copy) void(^sendBlock)(LiveGiftModel *model);
@property (nonatomic, strong) LiveGiftModel *model;
@end

@implementation GiftItemCell

- (instancetype)initWithFrame:(CGRect)frame {
self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.backgroundColor = self.contentView.backgroundColor = UIColor.up_contentBgColor;
    
    UIImageView *giftIcon = [[UIImageView alloc] init];
    [self.contentView addSubview:giftIcon];
    [giftIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.top.equalTo(15);
        make.size.equalTo(CGSizeMake(50, 50));
    }];
    self.giftIcon = giftIcon;
    
    UILabel *nameLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(15) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:1];
    [self.contentView addSubview:nameLB];
    [nameLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.top.equalTo(giftIcon.mas_bottom).offset(10);
        make.height.equalTo(21);
    }];
    self.nameLB = nameLB;
    
    UILabel *priceLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:UIColor.up_textSecondary1Color backgroundColor:FMClearColor numberOfLines:1];
    [self.contentView addSubview:priceLB];
    [priceLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.top.equalTo(nameLB.mas_bottom).offset(2);
        make.height.equalTo(17);
    }];
    self.priceLB = priceLB;
    
    UILabel *sendLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:FMWhiteColor backgroundColor:FMRedColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    sendLB.userInteractionEnabled = YES;
    sendLB.text = @"送礼给老师";
    [self.contentView addSubview:sendLB];
    [sendLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.left.right.equalTo(self);
        make.height.equalTo(30);
    }];
     WEAKSELF
     UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithActionBlock:^(UITapGestureRecognizer  *sender) {
        if (__weakSelf.sendBlock) {
            __weakSelf.sendBlock(__weakSelf.model);
        }
    }];
    [sendLB addGestureRecognizer:tap];
    self.sendLB = sendLB;
}

- (void)setModel:(LiveGiftModel *)model {
    _model = model;
    [self.giftIcon sd_setImageWithURL:[NSURL URLWithString:model.image] placeholderImage:nil];
    self.nameLB.text = model.name;
    self.priceLB.text = [NSString stringWithFormat:@"%@金币",model.price];
    
    self.sendLB.hidden = !model.isSelected;
    self.nameLB.hidden = model.isSelected;
    if (model.isSelected) {
        UI_View_BorderRadius(self.contentView, 5, 1, FMRedColor);
        [self.priceLB mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.nameLB.mas_bottom).offset(- 18);
        }];
    } else {
        UI_View_BorderRadius(self.contentView, 5, 1, UIColor.up_contentBgColor);
        [self.priceLB mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.nameLB.mas_bottom).offset(2);
        }];
    }
}


@end

@interface LiveGiftBoardView () <UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout>
@property (nonatomic, strong) NSMutableArray *itemViewArr;
@property (nonatomic, strong) UIView *backView;
/// 余额
@property (nonatomic, strong) UILabel *balanceLB;
/// 集合视图
@property (nonatomic, strong) UICollectionView *collectionView;

@property (nonatomic,assign) CGFloat backViewHeight;
@property (nonatomic,assign) BOOL isLandScape;

@end

@implementation LiveGiftBoardView

- (instancetype)initPageLandScape:(BOOL)isLandScape {
    self = [super init];
    if (self) {
        self.isLandScape = isLandScape;
        self.backViewHeight = isLandScape ?  UI_SCREEN_HEIGHT : (205 + 10 + 76 + UI_SAFEAREA_BOTTOM_HEIGHT);
        [self setupUI:isLandScape];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(userCoinUpdate:) name:kUserCoinUpdate object:nil];
    }
    return self;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    self.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT);
    if (self.isLandScape) {
        self.backView.frame = CGRectMake(UI_SCREEN_WIDTH - UI_SCREEN_HEIGHT, 0, UI_SCREEN_HEIGHT, UI_SCREEN_HEIGHT);
    } else {
        self.backView.frame = CGRectMake(0, UI_SCREEN_HEIGHT - self.backViewHeight, UI_SCREEN_WIDTH, self.backViewHeight);
    }
    // 每次显示时更新下金币余额
    [self userCoinUpdate:nil];
}

/// 设置UI
- (void)setupUI:(BOOL)isLandScape {
    self.backgroundColor = ColorWithHexAlpha(0x000000, 0.6);
    
    UIView *backView = [[UIView alloc] init];
    backView.backgroundColor = UIColor.up_contentBgColor;
    if (isLandScape) {
        self.backView.frame = CGRectMake(UI_SCREEN_WIDTH - UI_SCREEN_HEIGHT, 0, UI_SCREEN_HEIGHT, UI_SCREEN_HEIGHT);
    } else {
        self.backView.frame = CGRectMake(0, UI_SCREEN_HEIGHT - self.backViewHeight, UI_SCREEN_WIDTH, self.backViewHeight);
    }
    [self addSubview:backView];
   
    if (!isLandScape) {
        [backView layerAndBezierPathWithRect:CGRectMake(0, 0, UI_SCREEN_WIDTH, self.backViewHeight) cornerRadii:CGSizeMake(10, 10) byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight];
    }
    self.backView = backView;
    
    UILabel *titleLB = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(17) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    titleLB.text = @"礼物专区";
    [backView addSubview:titleLB];
    [titleLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(backView);
        make.left.equalTo(backView).offset((15));
        make.height.equalTo(50);
    }];
    
    UIButton *rechargeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [rechargeBtn setTitle:@"充值 >" forState:UIControlStateNormal];
    rechargeBtn.titleLabel.font = FontWithSize(15);
    [rechargeBtn setTitleColor:FMRedColor forState:UIControlStateNormal];
    [rechargeBtn addTarget:self action:@selector(rechargeBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [backView addSubview:rechargeBtn];
    [rechargeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(titleLB);
        make.right.equalTo(-15);
    }];
    
    // 余额
    UILabel *balanceLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:1];
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    [MyKeyChainManager save:kUserModel data:userModel];
    balanceLB.text = [NSString stringWithFormat:@"余额: %zd", userModel.coin];
    [backView addSubview:balanceLB];
    [balanceLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(titleLB);
        make.right.equalTo(rechargeBtn.mas_left).offset(-10);
    }];
    self.balanceLB = balanceLB;
    
    UIView *line = [[UIView alloc] init];
    line.backgroundColor = UIColor.fm_sepline_color;
    [backView addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(backView.mas_top).offset(50);
        make.left.right.equalTo(backView);
        make.height.equalTo(0.7);
    }];
    
    [backView addSubview:self.collectionView];
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(line.mas_bottom).offset(21);
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.bottom.equalTo(-UI_SAFEAREA_BOTTOM_HEIGHT);
    }];
    
    UIImageView *maskView = [[UIImageView alloc] initWithImage:FMImgInBundle(@"直播/giftboard_maskview")];
    [backView addSubview:maskView];
    [maskView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.left.right.equalTo(backView);
        make.height.equalTo(76);
    }];
    
    self.collectionView.contentInset = UIEdgeInsetsMake(0, 0, 76 - UI_SAFEAREA_BOTTOM_HEIGHT, 0);
}

/// 充值成功 更新金币余额
- (void)userCoinUpdate:(NSNotification *)noti {
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    self.balanceLB.text = [NSString stringWithFormat:@"余额: %zd", userModel.coin];
}

/// 送出礼物
- (void)sendGiftWithModel:(LiveGiftModel *)model {
    UIView *hudBackView = [FMPlayerManager shareManager].player.isFullScreen ? [FMPlayerManager shareManager].player.controlView : self.backView;
    [[FMPayTool payTool] judgeConfirmOrderStatusWithDakaId:self.detailModel.bignameDto.userId certCode:self.detailModel.bignameDto.certCode clickView:self confirmOperation:^{
        [HttpRequestTool liveSendGiftWithRoomId:self.detailModel.liveId giftId:model.giftId usePoints:0 start:^{
        } failure:^{
            [FMProgressHUD showTextOnlyInView:hudBackView withText:@"请检查您的网络"];
        } success:^(NSDictionary *dic) {
            [FMProgressHUD hiddenProgressHUDViewInView:self.backView];
            if ([dic[@"status"] isEqualToString:@"1"]) {
                if (self.sendGiftBlock) {
                    self.sendGiftBlock(model);
                }
                
                [HttpRequestTool queryDakaCoinsAndPointsStart:nil failure:nil success:^(NSDictionary *dic) {
                    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
                    self.balanceLB.text = [NSString stringWithFormat:@"余额: %zd", userModel.coin];
                }];

                [FMProgressHUD showTextOnlyInView:hudBackView withText:@"赠送成功!"];
            } else {
                if ([dic[@"errcode"] isEqualToString:@"501"]) {
                    [FMProgressHUD showTextOnlyInView:hudBackView withText:@"余额不足，请先充值!"];
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        // 并跳转充值中心
                        if(self.rechargeBlock) {
                            self.rechargeBlock();
                        }
                    });
                } else {
                    [FMProgressHUD showTextOnlyInView:hudBackView withText:dic[@"errmessage"]];
                }
            }
        }];
    } failurBlock:^{
        if(self.judgeConfirmOrderFailur) {
            self.judgeConfirmOrderFailur();
        }
    }];
}

/// 关闭视图
- (void)closeBtnClick {
    [self removeFromSuperview];
}

/// 充值
- (void)rechargeBtnClick {
    if(self.rechargeBlock) {
        self.rechargeBlock();
    }
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    CGPoint point = [[touches  anyObject] locationInView:self];
    if (self.isLandScape) {
        if (point.x > (UI_SCREEN_WIDTH - UI_SCREEN_HEIGHT)) {
            return;
        }
    } else {
        if (point.y > (UI_SCREEN_HEIGHT - self.backViewHeight)) {
            return;
        }
    }
    self.hidden = YES;
}

#pragma mark - public
- (void)show {
    self.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT);
    if (!self.superview) {
        [[FMHelper getCurrentVC].view addSubview:self];
    } else {
        self.hidden = NO;
    }
}

#pragma mark - collectionViewDelegate
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.dataArr.count;
}

- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    GiftItemCell *cell = [collectionView reuseCellClass:[GiftItemCell class] indexPath:indexPath];
    cell.model = self.dataArr[indexPath.item];
    WEAKSELF
    cell.sendBlock = ^(LiveGiftModel *model) {
        [__weakSelf sendGiftWithModel:model];
    };
    return cell;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    for (NSInteger i = 0; i < self.dataArr.count; i ++) {
        LiveGiftModel *model = self.dataArr[i];
        model.isSelected = indexPath.item == i;
        [self.collectionView reloadData];
    }
}

#pragma mark - lazy
- (NSMutableArray *)itemViewArr {
    if (!_itemViewArr) {
        _itemViewArr = [NSMutableArray array];
    }
    return _itemViewArr;
}

- (UICollectionView *)collectionView {
    if (!_collectionView) {
        _collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero scrollDirection:UICollectionViewScrollDirectionVertical minimumInteritemSpacing:0 minimumLineSpacing:0 itemSize:CGSizeMake((UI_SCREEN_WIDTH - 45) / 3.0, 130.0) sectionInset:UIEdgeInsetsZero flowLayoutDeleaget:self dataSource:self viewController:nil];
        _collectionView.showsVerticalScrollIndicator = NO;
        [_collectionView registerCellClass:[GiftItemCell class]];
    }
    return _collectionView;
}


- (void)setDataArr:(NSArray *)dataArr {
    NSArray *arr = [NSArray modelArrayWithClass:[LiveGiftModel class] json:dataArr];
    _dataArr = arr;
    if (self.giftId > 0) {
        for (LiveGiftModel *model in arr) {
            if (model.giftId == self.giftId) {
                model.isSelected = YES;
            }
        }
        self.giftId = 0;
    } else {
        LiveGiftModel *model = arr.firstObject;
        model.isSelected = YES;
    }
    [self.collectionView reloadData];
}

@end
