//
//  FMDakaLiveAssociatedContentVC.m
//  QCYZT
//
//  Created by shumi on 2022/1/5.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMDakaLiveAssociatedContentVC.h"
#import "FMLiveActiveModel.h"
#import "FMDakaLiveAssociatedActiveCell.h"
#import "FMLiveStockModel.h"
#import "HttpRequestTool+Live.h"
#import "HttpRequestTool+Pay.h"
#import "FMDakaLiveAssociatedStockCell.h"
#import "LiveMessageBaseCell.h"
#import "LiveMessageFrameModel.h"
#import "LiveMessageBaseModel.h"
#import "FMLiveDetailModel.h"
#import "EnablePayModel.h"
#import "FMPayTool.h"
#import "PaymentView.h"

@interface FMDakaLiveAssociatedContentVC ()<UITableViewDelegate,UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic,assign) LiveAssociatedContentType type;
@property (nonatomic, strong) UIView *activityHeaderView;
@property (nonatomic, strong) UIView *stockHeaderView;

@end

@implementation FMDakaLiveAssociatedContentVC

- (id)initWithType:(LiveAssociatedContentType)type {
    if (self = [super init]) {
        self.type = type;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = ColorWithHexAlpha(0x000000, 0.6);
    
    UIView *backView = [[UIView alloc] init];
    if (self.type == ReplyMyMessage) {
        backView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    } else {
        backView.backgroundColor = UIColor.up_contentBgColor;
    }
    [self.view addSubview:backView];
    CGFloat height;
    if (self.type == ActivityType) {
        height = (262 + UI_SAFEAREA_BOTTOM_HEIGHT);
    } else if (self.type == StockInfoType) {
        height = (312 + UI_SAFEAREA_BOTTOM_HEIGHT);
    } else {
        height = UI_SCREEN_HEIGHT - UI_SAFEAREA_TOP_HEIGHT - 44;
    }
   
    [backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.bottom.right.equalTo(self.view);
        make.height.equalTo(@(height));
    }];
    [backView layerAndBezierPathWithRect:CGRectMake(0, 0, UI_SCREEN_WIDTH, height) cornerRadii:CGSizeMake(10, 10) byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight];

    CGFloat y = 0;
    if (self.type == ActivityType) {
        y = 44;
        [backView addSubview:self.activityHeaderView];
    } else if (self.type == StockInfoType) {
        y = 90;
        [backView addSubview:self.stockHeaderView];
    } else {
        y = 44;
        [backView addSubview:self.activityHeaderView];
    }
    
    [backView addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsMake(y, 0, UI_SAFEAREA_BOTTOM_HEIGHT, 0));
    }];
    if (self.type == StockInfoType || self.type == ReplyMyMessage) {
        [self requestData];
    }
    
    UIButton *closeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [closeBtn setImage:[UIImage imageNamed:@"active_close"] forState:UIControlStateNormal];
    [closeBtn addTarget:self action:@selector(closeBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [backView addSubview:closeBtn];
    [closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@(-5));
        make.top.equalTo(@(5));
        make.size.equalTo(@(CGSizeMake(34, 34)));
    }];
    
    if (self.type == StockInfoType) {
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(updateStockData:) name:kNotificationLiveRelatedStockUpdate object:nil];
    }
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)viewWillAppear:(BOOL)animated {
    self.selfNavigationBarHidden = YES;
    [super viewWillAppear:animated];
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [self dismissViewControllerAnimated:NO completion:nil];
}

- (void)closeBtnClick {
    [self dismissViewControllerAnimated:NO completion:nil];
}

#pragma mark - noti
- (void)updateStockData:(NSNotification *)noti {
    
    NSDictionary *data = (NSDictionary *)noti.object;
    if ([data[@"stockDatas"] isKindOfClass:[NSArray class]]) {
        NSArray *array = [NSArray modelArrayWithClass:[FMLiveStockModel class] json:data[@"stockDatas"]];
        NSMutableArray *stockStrArray = [NSMutableArray array];
        for (NSInteger i = 0; i < array.count; i ++) {
            FMLiveStockModel *model = array[i];
            [stockStrArray addObject:model.stockCode];
        }
        self.stockStr = [stockStrArray componentsJoinedByString:@","];
        self.dataArray = [NSMutableArray arrayWithArray:array];
        [self.tableView reloadData];
    }
}

#pragma mark - request
- (void)requestData {
    if (self.type == StockInfoType) {
        [HttpRequestTool getLiveStockDataWithCode:self.stockStr start:^{
            [SVProgressHUD setOffsetFromCenter:UIOffsetMake(0, UI_SCREEN_HEIGHT / 2.0 - (312 + UI_SAFEAREA_BOTTOM_HEIGHT) / 2.0)];
            [SVProgressHUD show];
        } failure:^{
            [SVProgressHUD dismiss];
            [SVProgressHUD setOffsetFromCenter:UIOffsetZero];
        } success:^(NSDictionary *dic) {
            [SVProgressHUD dismiss];
            [SVProgressHUD setOffsetFromCenter:UIOffsetZero];
            if ([dic[@"status"] isEqualToString:@"1"]) {
                if ([dic[@"data"] isKindOfClass:[NSArray class]]) {
                    NSArray *arr = [NSArray modelArrayWithClass:[FMLiveStockModel class] json:dic[@"data"]];
                    self.dataArray = [NSMutableArray arrayWithArray:arr];
                }
            }
        }];
    } else {
        // 回复我的
        [HttpRequestTool liveRoomReplyMyMessage:[NSString stringWithFormat:@"%ld",self.model.liveId] start:^{
            [SVProgressHUD setOffsetFromCenter:UIOffsetMake(0, (UI_SAFEAREA_TOP_HEIGHT + 44) / 2.0)];
            [SVProgressHUD show];
        } failure:^{
            [SVProgressHUD dismiss];
            [SVProgressHUD setOffsetFromCenter:UIOffsetZero];
            [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        } success:^(NSDictionary *dic) {
            [SVProgressHUD dismiss];
            [SVProgressHUD setOffsetFromCenter:UIOffsetZero];
            if ([dic[@"status"] isEqualToString:@"1"]) {
                if ([dic[@"data"] isKindOfClass:[NSArray class]]) {
                    NSArray *arr = [NSArray modelArrayWithClass:[LiveMessageBaseModel class] json:dic[@"data"]];
                    if (arr.count == 0) {
                        [self.tableView showNoDataViewWithImage:nil string:@"暂无回复" attributes:@{NSFontAttributeName : FontWithSize(17.0), NSForegroundColorAttributeName : UIColor.up_textSecondary1Color} offsetY:150];
                    } else {
                        [self.tableView dismissNoDataView];
                        
                        NSMutableArray *array = [NSMutableArray array];
                        __block NSInteger  loadCompletedCount = 0;
                        WEAKSELF
                        for (NSInteger i = 0; i < arr.count; i ++) {
                            LiveMessageBaseModel *model = arr[i];
                            LiveMessageFrameModel *frameModel = [[LiveMessageFrameModel alloc] init];
                            frameModel.loaderCompletedBlock = ^{
                                loadCompletedCount ++;
                                if (loadCompletedCount == arr.count) {
                                    __weakSelf.dataArray = [NSMutableArray arrayWithArray:array];
                                    if (__weakSelf.dataArray.count == 0) {
                                        [__weakSelf.tableView showNoDataViewWithImage:nil string:@"暂无回复" attributes:@{NSFontAttributeName : FontWithSize(17.0), NSForegroundColorAttributeName : UIColor.up_textSecondary1Color} offsetY:150];
                                    } else {
                                        [__weakSelf.tableView dismissNoDataView];
                                    }
                                }
                            };
                            frameModel.liveType = [self.model.type integerValue];
                            frameModel.model = model;
                            [array addObject:frameModel];
                        }
                    }
                }
            }
        }];
    }
}

- (void)liveChatRoomPayMessageWithModel:(LiveMessageFrameModel *)frameModel {
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    EnablePayModel *enablePayModel = [[EnablePayModel alloc] init];
    enablePayModel.paydesc = [NSString stringWithFormat:@"您当前拥有%zd金币，确认支付", userModel.coin];
    enablePayModel.name = @"金币";
    enablePayModel.num = [NSString stringWithFormat:@"%zd", userModel.coin];
    enablePayModel.type = PaymentTypeCoin;
    self.model.enablePayModel = @[enablePayModel];
    NSString *dakaId = [NSString stringWithFormat:@"%@",self.model.bignameDto.userId];
    NSString *price = [NSString stringWithFormat:@"%ld",frameModel.model.messagePrice];
    NSString *title = @"直播付费消息";
    [[FMPayTool payTool] judgeConfirmOrderStatusWithDakaId:dakaId certCode:self.model.bignameDto.certCode clickView:nil confirmOperation:^{
        [PaymentView showWithEnablePayModel:enablePayModel payPrice:price productName:title bottomReminder:@"注：已付费的产品和内容，不会重复扣费" payAction:^(EnablePayModel *selectedModel) {
            // 3.支付
            [HttpRequestTool liveChatRoomPayMessageWithMessageId:frameModel.model.messageId roomId:self.model.liveId usePoints:selectedModel.usePoints start:^{
            } failure:^{
                [SVProgressHUD showErrorWithStatus:@"网络不给力"];
            } success:^(NSDictionary *dic) {
                if ([dic[@"status"] isEqualToString:@"1"]) {
                    [HttpRequestTool queryDakaCoinsAndPointsStart:nil failure:nil success:nil];
                    
                    LiveMessageBaseModel *model = [LiveMessageBaseModel modelWithDictionary:dic[@"data"][@"dkmsg"]];
                    model.websocketMsgType = @"DKMSG";
                    LiveMessageFrameModel *newframeModel = [[LiveMessageFrameModel alloc] init];
                    __weak LiveMessageFrameModel *weakSelfModel = newframeModel;
                    WEAKSELF
                    newframeModel.loaderCompletedBlock = ^{
                        [__weakSelf.dataArray replaceObjectAtIndex:[__weakSelf.dataArray indexOfObject:frameModel] withObject:weakSelfModel];
                        [[NSNotificationCenter defaultCenter] postNotificationName:@"ReplayMessagePaySuccess" object:weakSelfModel];
                        [__weakSelf.tableView reloadData];
                    };
                    newframeModel.liveType = [self.model.type integerValue];
                    newframeModel.model = model;
                    [SVProgressHUD showSuccessWithStatus:@"支付成功!"];
                } else {
                    [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
                }
            }];
        } dismissBlock:^{
        }];
    }];
}

#pragma mark - tableviewdelegate
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return (self.type == ReplyMyMessage) ? self.dataArray.count : 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return (self.type == ReplyMyMessage) ? 1 : self.dataArray.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.type == ActivityType) {
        FMDakaLiveAssociatedActiveCell *cell = [tableView reuseCellClass:[FMDakaLiveAssociatedActiveCell class]];
        cell.model = self.dataArray[indexPath.row];
        return cell;
    } else if (self.type == StockInfoType) {
        FMDakaLiveAssociatedStockCell *cell = [tableView reuseCellClass:[FMDakaLiveAssociatedStockCell class]];
        cell.model = self.dataArray[indexPath.row];
        return cell;
    } else {
        LiveMessageBaseCell *cell = [tableView reuseCellClass:[LiveMessageBaseCell class]];
        LiveMessageFrameModel *frameModel = self.dataArray[indexPath.section];
        cell.frameModel = frameModel;
        cell.textView.payMessageClickBlock = ^{
            // 直播间消息付费
            if (frameModel.model.messagePrice > 0) {
                [self liveChatRoomPayMessageWithModel:frameModel];
            }
        };
        cell.textView.imagePreviewBlock = ^(NSInteger index) {
            /// 图片预览
            HZPhotoBrowser *photoCtrl = [[HZPhotoBrowser alloc] init];
            NSMutableArray *imageUrlArray = [NSMutableArray array];
            for (NSInteger i = 0; i < frameModel.imgsAddress.count; i ++) {
                NSDictionary *dic = frameModel.imgsAddress[i];
                [imageUrlArray addObject:dic[@"url"]];
            }
            photoCtrl.imageArray = imageUrlArray;
            photoCtrl.currentImageIndex = (int)index;
            [photoCtrl show];
        };
        return cell;
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.type == ActivityType) {
        return 105;
    } else if (self.type == StockInfoType) {
        return 64;
    } else {
        LiveMessageFrameModel *frameModel = self.dataArray[indexPath.section];
        return frameModel.cellHeight;
    }
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    if (self.type == ReplyMyMessage) {
        UIView *view = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 46)];
        view.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
        UILabel *timeLB = [[UILabel alloc] initWithFrame:CGRectMake(0, 10, UI_SCREEN_WIDTH, 46) font:FontWithSize(12) textColor:UIColor.up_textSecondary1Color backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        LiveMessageFrameModel *frameModel = self.dataArray[section];
        NSDate *msgdate = [NSDate dateWithTimeIntervalSince1970:frameModel.model.createTime / 1000];
        timeLB.text = [NSString stringFromDate:msgdate format:@"HH:mm:ss"];
        [view addSubview:timeLB];
        return view;
    }
    return [UIView new];
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.type == StockInfoType) {
        FMLiveStockModel *model = self.dataArray[indexPath.row];
        UPMarketCodeMatchInfo *matchInfo = [FMUPDataTool matchInfoWithSetCodeAndCode:model.stockCode];

        if (![matchInfo isKindOfClass:UPMarketCodeMatchInfo.class] || !matchInfo.code.length) {
            return;
        }
        [self dismissViewControllerAnimated:NO completion:^{
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [UPRouterUtil goMarketStock:matchInfo.setCode code:matchInfo.code];
            });
        }];
       
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return (self.type == ReplyMyMessage) ? 46 : CGFLOAT_MIN;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

#pragma mark - setter/getter
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain delegate:self dataSource:self viewController:self];
        if (self.type == ReplyMyMessage) {
            _tableView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
        } else {
            _tableView.backgroundColor = UIColor.up_contentBgColor;
        }
        _tableView.tableFooterView = [UIView new];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        [_tableView registerCellClass:[FMDakaLiveAssociatedActiveCell class]];
        [_tableView registerCellClass:[FMDakaLiveAssociatedStockCell class]];
        [_tableView registerCellClass:[LiveMessageBaseCell class]];
    }
    return _tableView;
}

- (void)setDataArray:(NSMutableArray *)dataArray {
    _dataArray = dataArray;
    [self.tableView reloadData];
}

- (UIView *)activityHeaderView {
    if (!_activityHeaderView) {
        UIView *view = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 44)];
        UILabel *titleLB = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(17) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:1];
        if (self.type == ActivityType) {
            titleLB.text = @"活动产品";
            view.backgroundColor = UIColor.up_contentBgColor;
        } else {
            titleLB.text = @"回复我的";
            view.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
        }
        [view addSubview:titleLB];
        [titleLB mas_makeConstraints:^(MASConstraintMaker *make) {
            if (self.type == ActivityType) {
                make.top.left.equalTo(@(15));
            } else {
                make.top.equalTo(@(15));
                make.centerX.equalTo(@(0));
            }
            make.height.equalTo(@(24));
        }];
        _activityHeaderView = view;
    }
    return _activityHeaderView;
}

- (UIView *)stockHeaderView {
    if (!_stockHeaderView) {
        UIView *view = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 90)];
        view.backgroundColor = UIColor.up_contentBgColor;
        UILabel *titleLB = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(17) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:1];
        titleLB.text = @"相关股票";
        [view addSubview:titleLB];
        [titleLB mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(@(13));
            make.height.equalTo(@(24));
            make.centerX.equalTo(@(0));
        }];
        
        UIView *line = [[UIView alloc] init];
        line.backgroundColor = UIColor.fm_sepline_color;
        [view addSubview:line];
        [line mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(@(0));
            make.top.equalTo(@(49.5));
            make.height.equalTo(@(0.5));
        }];
        
        UILabel *currentLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(13) textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        currentLB.text = @"最新";
        [view addSubview:currentLB];
        [currentLB mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(view);
            make.top.equalTo(line.mas_bottom).offset(18);
            make.height.equalTo(@(19));
        }];
        
        UILabel *appliesLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(13.0) textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        appliesLabel.text = @"涨跌幅";
        [view addSubview:appliesLabel];
        [appliesLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(view.mas_right).offset(-65);
            make.centerY.equalTo(currentLB);
        }];
       
        _stockHeaderView = view;
    }
    return _stockHeaderView;
}

@end
