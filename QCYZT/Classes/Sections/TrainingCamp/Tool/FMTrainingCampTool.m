//
//  FMTrainingCampTool.m
//  QCYZT
//
//  Created by th on 17/2/27.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import "FMTrainingCampTool.h"
#import "FMTrainingCampChapterListModel.h"
#import "FMTrainingCampChapterDetailModel.h"
#import "FMPayTool.h"
#import "EnablePayModel.h"
#import "PaymentView.h"
#import "FMTrainingCampDetailModel.h"
#import "HttpRequestTool+TrainingCamp.h"
#import "HttpRequestTool+Pay.h"
#import "FileManagerTool.h"
#import "FMPaySuccessPopView.h"

@implementation FMTrainingCampTool

/**
 保存章节内容
 
 @param chapterListModel 列表model
 @param content 章节详情内容
 @param shouldSaveToFile 是否需要将详情内容缓存到文件
 */
+ (void)saveContentData:(FMTrainingCampChapterListModel *)chapterListModel chapterDetailModel:(FMTrainingCampChapterDetailModel *)chapterDetailModel shouldSaveToFile:(BOOL)shouldSaveToFile {
    NSString *folderPath = [[NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES) lastObject] stringByAppendingString:@"/TrainingCamp"];
    NSFileManager *fileManager = [NSFileManager defaultManager];
    if (![fileManager fileExistsAtPath:folderPath]) {
        [fileManager createDirectoryAtPath:folderPath withIntermediateDirectories:YES attributes:nil error:NULL];
    }
    
    NSData *data = [NSKeyedArchiver archivedDataWithRootObject:chapterDetailModel];
    NSString *filePath = [NSString stringWithFormat:@"%@/%@.dat", folderPath, chapterListModel.chapterId];
    if (![fileManager fileExistsAtPath:filePath] || shouldSaveToFile) {
        [data writeToFile:filePath atomically:YES];
    }
    chapterListModel.filePath = [NSString stringWithFormat:@"/TrainingCamp/%@.dat", chapterListModel.chapterId];
    chapterListModel.userId = [FMUserDefault getUserId].length ? [FMUserDefault getUserId] : @"YK";
    
    NSArray *rs = [DBManager selectDatasWithClass:[FMTrainingCampChapterListModel class] where:[NSString stringWithFormat:@"chapterId = '%@' and userId = '%@'", chapterListModel.chapterId, [FMUserDefault getUserId].length ? [FMUserDefault getUserId] : @"YK"] order:nil limit:nil];
    if (!rs.count) {
        [DBManager insertDataWithModel:chapterListModel finishBlock:^(bool issuccess) {
            FMLog(@"章节存储成功");
        }];
    } else {
        [DBManager deleteDataWithClass:[FMTrainingCampChapterListModel class] where:[NSString stringWithFormat:@"chapterId = '%@' and userId = '%@'", chapterListModel.chapterId, [FMUserDefault getUserId].length ? [FMUserDefault getUserId] : @"YK"]];
        [DBManager insertDataWithModel:chapterListModel  finishBlock:^(bool issuccess) {
            FMLog(@"删除后存储成功");
        }];
    }
}

+ (FMTrainingCampChapterListModel *)queryNewestChapterWithBookId:(NSString *)bookId{
    NSArray *rs = [DBManager selectDatasWithClass:[FMTrainingCampChapterListModel class] where:[NSString stringWithFormat:@"userId = '%@' and bookid = '%@'", [FMUserDefault getUserId].length ? [FMUserDefault getUserId] : @"YK", bookId] order:nil limit:nil];
    if (rs.count) {
        FMTrainingCampChapterListModel *model = rs.lastObject;
        return model;
    }
    return nil;
}

+ (FMTrainingCampChapterListModel *)queryChapterWithChapterId:(NSString *)chapterId {
    NSArray *rs = [DBManager selectDatasWithClass:[FMTrainingCampChapterListModel class] where:[NSString stringWithFormat:@"userId = '%@' and chapterId = '%@'",  [FMUserDefault getUserId].length ? [FMUserDefault getUserId] : @"YK", chapterId] order:nil limit:nil];
    if (rs.count) {
        FMTrainingCampChapterListModel *model = rs.lastObject;
        return model;
    }
    return nil;
}

+ (FMTrainingCampChapterDetailModel *)queryDataWithChapterId:(NSString *)chapterId lastUpdateTime:(NSString *)lastUpdateTime{
    NSArray *rs = [DBManager selectDatasWithClass:[FMTrainingCampChapterListModel class] where:[NSString stringWithFormat:@"chapterId = '%@' and userId = '%@' and lastUpdateTime = '%@'", chapterId, [FMUserDefault getUserId].length ? [FMUserDefault getUserId] : @"YK", lastUpdateTime] order:nil limit:nil];
    if (rs.count) {
        FMTrainingCampChapterListModel *model = rs.lastObject;
        NSString *filePath = [[NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES) lastObject] stringByAppendingString:model.filePath];
        NSFileManager *fileManager = [NSFileManager defaultManager];
        if ([fileManager fileExistsAtPath:filePath]) {
            NSLog(@"文件存在");
        } else {
            NSLog(@"文件不存在");
        }
        NSData *data = [NSData dataWithContentsOfFile:filePath];
        return (FMTrainingCampChapterDetailModel *)[NSKeyedUnarchiver unarchiveObjectWithData:data];
    }
    
    return nil;
}

+ (void)getContentDetailWithChapterId:(NSString *)chapterId
              trainingCampDetailModel:(FMTrainingCampDetailModel *)model
                        forbiddenView:(UIView *)forbiddenView
                              success:(void(^)(FMTrainingCampChapterDetailModel *model))success
                              failure:(void(^)())failure {
    [HttpRequestTool campChapterContentDetailWithChapterId:chapterId start:^{
        [SVProgressHUD show];
        if (forbiddenView) {
            forbiddenView.userInteractionEnabled = NO;
        }
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        if (forbiddenView) {
            forbiddenView.userInteractionEnabled = YES;
        }
        failure();
    } success:^(NSDictionary *dic) {
        [SVProgressHUD dismiss];
        
        if ([dic[@"status"] isEqualToString:@"1"]) {
            FMTrainingCampChapterDetailModel *model = [FMTrainingCampChapterDetailModel modelWithDictionary:dic[@"data"]];
            success(model);
            if (forbiddenView) {
                forbiddenView.userInteractionEnabled = YES;
            }
        } else {
            if ([dic[@"errcode"] isEqualToString:@"1001"]) { // 需要支付
                [self httpForPayTrainingCampWithTrainingCampDetailModel:model forbiddenView:forbiddenView success:success failure:failure];
            } else  {
                if(![dic[@"errcode"] isEqualToString:@"100"]){
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
                    });
                    failure();
                }
                if (forbiddenView) {
                    forbiddenView.userInteractionEnabled = YES;
                }
            }
        }
    }];
}

+ (void)httpForPayTrainingCampWithTrainingCampDetailModel:(FMTrainingCampDetailModel *)detailModel
                                            forbiddenView:(UIView *)forbiddenView
                                                  success:(void(^)(FMTrainingCampChapterDetailModel *model))success
                                                  failure:(void(^)())failure {
    if (detailModel.enablePayModel.count == 0) {
        // 没有可用的支付方式
        [[FMPayTool payTool] noEnablePayModelWithErrorCode:detailModel.noPayModelCode errorText:detailModel.noPayModelText];
        return;
    }
    
    [[FMPayTool payTool] judgeConfirmOrderStatusWithDakaId:detailModel.userid certCode:detailModel.cert_code clickView:forbiddenView confirmOperation:^{
        [PaymentView showWithEnablePayList:detailModel.enablePayModel payPrice:detailModel.price productName:detailModel.name bottomReminder:@"注：已付费的产品和内容，不会重复扣费" payAction:^(EnablePayModel *selectedModel) {
            [HttpRequestTool trainingCampPayWithBookId:detailModel.bookid type:[NSString stringWithFormat:@"%zd", selectedModel.type] usePoints:selectedModel.usePoints start:^{
                [SVProgressHUD show];
            } failure:^{
                [SVProgressHUD showErrorWithStatus:@"网络不给力"];
                if (failure) {
                    failure();
                }
            } success:^(NSDictionary *dic) {
                if ([dic[@"status"] isEqualToString:@"1"]) {
                    [SVProgressHUD dismiss];
                    FMPaySuccessPopView *popView = [[FMPaySuccessPopView alloc] init];
                    popView.jumpIndex = 4;
                    [popView show];

                    [HttpRequestTool queryDakaCoinsAndPointsStart:nil failure:nil success:nil];
                    
                    if (success) {
                        success(nil);
                    }
                } else {
                    [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
                    if (failure) {
                        failure();
                    }
                }
            }];
        } dismissBlock:^{

        }];
    }];
}


//+ (void)gotoPayWithDic:(NSDictionary *)dic
//             chapterId:(NSString *)chapterId
//         forbiddenView:(UIView *)forbiddenView
//               success:(void(^)(FMTrainingCampChapterDetailModel *model))success
//               failure:(void(^)())failure  {
//    if (![dic[@"data"] isKindOfClass:[NSDictionary class]]) {
//        return;
//    }
//    NSArray *enablePayArray = [NSArray modelArrayWithClass:[EnablePayModel class] json:dic[@"data"][@"enablePayModel"]];
//    if (enablePayArray.count == 0) {
//        // 没有可用的支付方式
//        [[FMPayTool payTool] noEnablePayModelWithErrorCode:[dic[@"data"][@"noPayModelCode"] integerValue] errorText:dic[@"data"][@"noPayModelText"]];
//        return;
//    }
//    NSString *userid = [NSString stringWithFormat:@"%@",dic[@"data"][@"userid"]];
//    NSString *cret_code = [NSString stringWithFormat:@"%@",dic[@"data"][@"cert_code"]];
//    [[FMPayTool payTool] judgeConfirmOrderStatusWithDakaId:userid certCode:cret_code clickView:forbiddenView confirmOperation:^{
//        WEAKSELF
//        NSString *priceStr = [NSString stringWithFormat:@"%@", dic[@"data"][@"price"]];
//        NSString *coinNumStr = [NSString stringWithFormat:@"%@", dic[@"data"][@"coinNum"]];
//        [PaymentView showWithEnablePayList:enablePayArray payPrice:priceStr productName:[NSString stringWithFormat:@"第%@章第%@节  %@", dic[@"data"][@"partNum"], dic[@"data"][@"chapterNum"], dic[@"data"][@"title"]] bottomReminder:@"注:已购买章节及免费章节，不会重复扣费" payAction:^(EnablePayModel *selectedModel) {
//            [__weakSelf httpForPayWithChapterId:chapterId type:selectedModel.type priceStr:priceStr coinNumStr:coinNumStr forbiddenView:forbiddenView success:success failure:failure];
//        } dismissBlock:^{
//
//        }];
//    }];
//}
//
//+ (void)httpForPayWithChapterId:(NSString *)chapterId
//                           type:(NSString *)type
//                       priceStr:(NSString *)priceStr
//                     coinNumStr:(NSString *)coinNumStr
//                  forbiddenView:(UIView *)forbiddenView
//                        success:(void(^)(FMTrainingCampChapterDetailModel *model))success
//                        failure:(void(^)())failure {
//    [HttpRequestTool campPayForChapterWithChapterId:chapterId type:type start:^{
//        [SVProgressHUD show];
//    } failure:^{
//        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
//        failure();
//        if (forbiddenView) {
//            forbiddenView.userInteractionEnabled = YES;
//        }
//    } success:^(NSDictionary *dic) {
//        if ([dic[@"status"] isEqualToString:@"1"]) {
//            [SVProgressHUD showSuccessWithStatus:@"支付成功"];
//            [[NSNotificationCenter defaultCenter] postNotificationName:@"campChapterPaySuccess" object:nil];
//
//            if ([type isEqualToString:@"1"]) {
//                // 金币支付，更新本地的金币余额
//                NSMutableDictionary *personalInfoDic = [NSMutableDictionary dictionaryWithDictionary: [NSDictionary dictionaryWithContentsOfFile:PersonnalInfoFile]];
//                NSString *coins = [NSString stringWithFormat:@"%.0f", [coinNumStr floatValue]-[priceStr floatValue]];
//                personalInfoDic[@"coin"] = coins;
//                [personalInfoDic writeToFile:PersonnalInfoFile atomically:YES];
//            }
//
//            success(nil);
//            if (forbiddenView) {
//                forbiddenView.userInteractionEnabled = YES;
//            }
//            [[NSNotificationCenter defaultCenter] postNotificationName:kPayForChapterSuccess object:nil userInfo:@{@"chapterId":chapterId}];
//        } else {
//            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
//            failure();
//            if (forbiddenView) {
//                forbiddenView.userInteractionEnabled = YES;
//            }
//        }
//    }];
//}

@end
