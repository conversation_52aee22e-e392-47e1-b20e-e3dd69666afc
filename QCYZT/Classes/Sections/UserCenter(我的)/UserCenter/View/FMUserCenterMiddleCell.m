//
//  FMUserCenterMiddleCell.m
//  QCYZT
//
//  Created by zeng on 2021/10/12.
//  Copyright © 2021 LZKJ. All rights reserved.
//

#import "FMUserCenterMiddleCell.h"
#import "UIButton+ImageTitleSpacing.h"
#import "FMPayTool.h"
#import "FMCouponViewController.h"
#import "YTGOtherWebVC.h"

@interface FMUserCenterMiddleCellInnerView : UIView

@property (nonatomic, strong) UILabel *numberLabel;
@property (nonatomic, copy) void (^tapAction)(void);

- (instancetype)initWithTitle:(NSString *)title
                      icon:(NSString *)iconName
                 showArrow:(BOOL)showArrow;

@end

@implementation FMUserCenterMiddleCellInnerView

- (instancetype)initWithTitle:(NSString *)title icon:(NSString *)iconName showArrow:(BOOL)showArrow {
    if (self = [super init]) {
        self.backgroundColor = UIColor.fm_FFFFFF_2E2F33;
        self.layer.shadowColor = ColorWithHexAlpha(0x000000, 0.1).CGColor;
        self.layer.shadowOffset = CGSizeMake(0,0);
        self.layer.shadowRadius = 15;
        self.layer.shadowOpacity = 1;
        self.layer.cornerRadius = 5;
        self.userInteractionEnabled = YES;
        
        UIButton *titleBtn = [[UIButton alloc] initWithFrame:CGRectZero
                                                      font:[FMHelper scaleFont:13]
                                             normalTextColor:UIColor.up_textPrimaryColor
                                          backgroundColor:FMClearColor
                                                   title:title
                                                       image:FMImgInBundle(iconName)
                                                 target:nil
                                                 action:nil];
        [self addSubview:titleBtn];
        [titleBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(@15);
            make.centerY.equalTo(@0);
        }];
        titleBtn.titleLabel.lineBreakMode = NSLineBreakByClipping;
        titleBtn.userInteractionEnabled = NO;
        [titleBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageLeft imageTitleSpacing:5.0];
        
        if (showArrow) {
            UIImageView *arrowImgV = [[UIImageView alloc] initWithImage:ImageWithName(@"userCenter_arrow")];
            [self addSubview:arrowImgV];
            [arrowImgV mas_makeConstraints:^(MASConstraintMaker *make) {
                make.right.equalTo(-15);
                make.centerY.equalTo(0);
            }];
        } else {
            self.numberLabel = [[UILabel alloc] initWithFrame:CGRectZero
                                                       font:BoldFontWithSize(20)
                                                    textColor:UIColor.up_textPrimaryColor
                                          backgroundColor:FMClearColor
                                            numberOfLines:1
                                           textAlignment:NSTextAlignmentLeft];
            [self addSubview:self.numberLabel];
            [self.numberLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.right.equalTo(@-15);
                make.centerY.equalTo(@0);
            }];
            self.numberLabel.text = @"0";
        }
        
        [self addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(viewTapped)]];
    }
    return self;
}

- (void)viewTapped {
    if (self.tapAction) {
        self.tapAction();
    }
}

@end

@interface FMUserCenterMiddleCell()

@property (nonatomic, strong) UIView *coinView;
@property (nonatomic, strong) UIView *ddView;
@property (nonatomic, strong) UIView *couponView;
@property (nonatomic, strong) UIView *bxView;

@property (nonatomic, strong) ZLTagLabel *firstRechargeLabel;
@property (nonatomic, strong) UIButton *dkbBtn;
@property (nonatomic, strong) UILabel *coinNumLabel;
@property (nonatomic, strong) UILabel *pointsLabel;
@property (nonatomic, strong) UIButton *rechargeBtn;
@property (nonatomic, strong) UILabel *orderNumLabel;
@property (nonatomic, strong) UILabel *couponNumLabel;

@end

@implementation FMUserCenterMiddleCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setUp];
    }
    
    return self;
}

- (void)setUp {
    self.contentView.backgroundColor = UIColor.up_contentBgColor;
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    
    {
        UIView *coinView = [[UIView alloc] init];
        [self.contentView addSubview:coinView];
        [coinView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(@15);
            make.top.equalTo(@10);
            make.width.equalTo(@((UI_SCREEN_WIDTH - 40) * 0.5));
            make.bottom.equalTo(@-10);
        }];
        coinView.backgroundColor = UIColor.fm_FFFFFF_2E2F33;
        coinView.layer.shadowColor = ColorWithHexAlpha(0x000000, 0.1).CGColor;
        coinView.layer.shadowOffset = CGSizeMake(0,0);
        coinView.layer.shadowRadius = 15;
        coinView.layer.shadowOpacity = 1;
        coinView.layer.cornerRadius = 5;
        self.coinView = coinView;
        
        ZLTagLabel *firstRechargeLabel = [[ZLTagLabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:FMWhiteColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        firstRechargeLabel.widthPadding = 14;
        [coinView addSubview:firstRechargeLabel];
        [firstRechargeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.top.equalTo(0);
            make.height.equalTo(19);
        }];
        self.firstRechargeLabel = firstRechargeLabel;
        
        UIButton *dkbBtn = [[UIButton alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:13] normalTextColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor title:@"我的金币" image:ImageWithName(@"userCenter_dkb") target:nil action:nil];
        [coinView addSubview:dkbBtn];
        [dkbBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(@15);
            make.top.equalTo(20);
        }];
        dkbBtn.titleLabel.lineBreakMode = NSLineBreakByClipping;
        [dkbBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageLeft imageTitleSpacing:5.0];
        self.dkbBtn = dkbBtn;
        
        UILabel *coinNumLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(20) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
        [coinView addSubview:coinNumLabel];
        [coinNumLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(dkbBtn);
            make.centerY.equalTo(0);
        }];
        self.coinNumLabel = coinNumLabel;
        coinNumLabel.text = @"0";
        
        UIButton *rechargeBtn = [[UIButton alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:12] normalTextColor:UIColor.up_riseColor backgroundColor:FMClearColor title:@"充值" image:nil target:nil action:nil];
        CGSize rechargeBtnSize = [FMHelper isBigFont] ? CGSizeMake(55, 30) : CGSizeMake(45, 25);
        [coinView addSubview:rechargeBtn];
        [rechargeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(coinNumLabel);
            make.right.equalTo(@-15);
            make.size.equalTo(@(rechargeBtnSize));
        }];
        UI_View_BorderRadius(rechargeBtn, rechargeBtnSize.height / 2.0, 1.0, UIColor.up_riseColor);
        [rechargeBtn addTarget:self action:@selector(jumpToRecharge) forControlEvents:UIControlEventTouchUpInside];
        self.rechargeBtn = rechargeBtn;
        
        UILabel *pointsLabel = [[UILabel alloc] initWithFrame:CGRectZero
                                                   font:FontWithSize(15)
                                                    textColor:UIColor.up_textPrimaryColor
                                        backgroundColor:FMClearColor
                                          numberOfLines:1
                                         textAlignment:NSTextAlignmentLeft];
        [coinView addSubview:pointsLabel];
        [pointsLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(dkbBtn);
            make.bottom.equalTo(-20);
        }];
        pointsLabel.text = @"积分：0";
        self.pointsLabel = pointsLabel;
    }
    
    // 创建右侧stackView
    UIStackView *rightStackView = [[UIStackView alloc] init];
    rightStackView.axis = UILayoutConstraintAxisVertical;
    rightStackView.spacing = 10;
    rightStackView.distribution = UIStackViewDistributionFillEqually;
    [self.contentView addSubview:rightStackView];
    
    [rightStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.coinView.mas_right).offset(10);
        make.top.equalTo(@10);
        make.width.equalTo(self.coinView);
        make.bottom.equalTo(@-10);
    }];
    
    // 订单视图
    FMUserCenterMiddleCellInnerView *ddView = [[FMUserCenterMiddleCellInnerView alloc] initWithTitle:@"我的订单"
                                                                                                icon:@"我的/我的订单"
                                                                                           showArrow:NO];
    ddView.tapAction = ^{
        [self jumpToMyOrder];
    };
    self.orderNumLabel = ddView.numberLabel;
    [rightStackView addArrangedSubview:ddView];
    
    // 卡券视图
    FMUserCenterMiddleCellInnerView *couponView = [[FMUserCenterMiddleCellInnerView alloc] initWithTitle:@"我的卡券"
                                                                                                    icon:@"我的/我的卡券"
                                                                                               showArrow:NO];
    couponView.tapAction = ^{
        [self jumpToCoupon];
    };
    self.couponNumLabel = couponView.numberLabel;
    [rightStackView addArrangedSubview:couponView];
    
    // 保险视图
    FMUserCenterMiddleCellInnerView *bxView = [[FMUserCenterMiddleCellInnerView alloc] initWithTitle:@"我的保险"
                                                                                                icon:@"我的/我的保险"
                                                                                           showArrow:YES];
    bxView.tapAction = ^{
        [self jumpToBx];
    };
    self.bxView = bxView;
    [rightStackView addArrangedSubview:bxView];
    
    NSArray *cardViews = @[ddView, couponView, bxView];
    [cardViews mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@40);
    }];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    if (!self.firstRechargeLabel.hidden) {
        self.firstRechargeLabel.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xfc0002), ColorWithHex(0xff009d)] withFrame:self.firstRechargeLabel.bounds direction:GradientDirectionLeftToRight];
        [self.firstRechargeLabel layerAndBezierPathWithRect:self.firstRechargeLabel.bounds cornerRadii:CGSizeMake(5, 5) byRoundingCorners:UIRectCornerBottomLeft|UIRectCornerTopRight];
    }
}

- (void)jumpToMyOrder {
    [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
        YTGOtherWebVC *webVC = [[YTGOtherWebVC alloc] init];
        webVC.startPage = [NSString stringWithFormat:@"%@%@", prefix, kAPI_UserCenter_WDDD];
        webVC.titleStr = @"我的订单";
        [[FMHelper getCurrentVC].navigationController pushViewController:webVC animated:YES];
    }];
}

- (void)jumpToCoupon {
    FMCouponViewController *cardCoupon = [[FMCouponViewController alloc] init];
    [[FMHelper getCurrentVC].navigationController pushViewController:cardCoupon animated:YES];
}

- (void)jumpToBx {
    NSDictionary *initDic = [[NSUserDefaults standardUserDefaults] objectForKey:AppInit_Key];
    if ([initDic[@"insuranceTag"] length]) {
        [ProtocolJump jumpWithUrl:initDic[@"insuranceTag"]];
    }
}

- (void)setData {
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    NSString *firstRechargeText = [FMUserDefault getSeting:AppInit_FirstRecharge_Text];
    if (userModel.isShowFirstDesc && firstRechargeText.length) {
        self.firstRechargeLabel.hidden = NO;
        self.firstRechargeLabel.text = firstRechargeText;
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.25 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self setNeedsLayout];
            [self layoutIfNeeded];
        });
    } else {
        self.firstRechargeLabel.hidden = YES;
    }
    
    self.coinNumLabel.text = [NSString stringWithFormat:@"%zd", userModel.coin];
    self.pointsLabel.attributedText = [[NSString stringWithFormat:@"积分：%zd", userModel.points] attrStrWithMatchColor:UIColor.up_textSecondaryColor pattern:@"积分：" textFont:FontWithSize(12)];
    
    if ([FMUserDefault getUserFlag]) {
        self.rechargeBtn.hidden = YES;
    } else {
        self.rechargeBtn.hidden = NO;
    }
    
    self.orderNumLabel.text = [NSString stringWithFormat:@"%zd", userModel.orderNum];    
    self.couponNumLabel.text = [NSString stringWithFormat:@"%zd", userModel.ticketNum];
    
    NSDictionary *initDic = [[NSUserDefaults standardUserDefaults] objectForKey:AppInit_Key];
    if ([initDic[@"insuranceTag"] length]) {
        self.bxView.hidden = NO;
        [self.dkbBtn mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(20);
        }];
        [self.pointsLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(-20);
        }];
    } else {
        self.bxView.hidden = YES;
        [self.dkbBtn mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(10);
        }];
        [self.pointsLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(-10);
        }];
    }
}

- (void)jumpToRecharge {
    [[FMPayTool payTool] gotoRecharge];
}

@end
