//
//  FMMyCollectionQuestionViewController.m
//  QCYZT
//
//  Created by th on 17/1/9.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import "FMMyCollectionQuestionViewController.h"
#import "FMAskCodeListCell.h"
#import "FMAskCodeVideoListCell.h"
#import "FMAskCodeDetailViewController.h"
#import "FMAskCodeModel.h"
@interface FMMyCollectionQuestionViewController()

@property (nonatomic, strong) NSMutableArray *dataArr;
@property (nonatomic, assign) NSUInteger page;
@property (nonatomic, assign) NSUInteger currentPage;
@property (nonatomic, assign) NSUInteger pageSize;

@end

@implementation FMMyCollectionQuestionViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.tableView.backgroundColor = UIColor.up_contentBgColor;
    self.page = 1;
    self.currentPage = self.page;
    self.pageSize = 10;
    
    [self configTableView];
    [self.tableView.mj_header beginRefreshing];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(getCollectNumChangedNotification:) name:kCollectNumChanged object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(questionPaySuccess:) name:kQuestionPaySuccess object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(praisedNotification:) name:kAskCodePriasedNotification object:nil];
}

- (void)dealloc {
    FMLog(@"%s", __func__);
    
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - UITableViewDelegate  DataSoure
- (NSInteger)numberOfSectionsIntableView:(UITableView *)tableView {
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataArr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    FMAskCodeModel *model = self.dataArr[indexPath.row];
    if (model.contentFileType.integerValue == 1 && (model.questionAnswerFlag.integerValue == 1 && model.checkStatus.integerValue == 1)) {
        //视频问股
        FMAskCodeVideoListCell *cell = [tableView reuseCellClass:[FMAskCodeVideoListCell class]];
        cell.askModel = model;
        cell.isLastCell = (indexPath.row == self.dataArr.count - 1);
        cell.infoView.iconImgV.userInteractionEnabled = NO;
        return cell;
    } else {
        //语音问股
        FMAskCodeListCell *cell = [tableView reuseCellClass:[FMAskCodeListCell class]];
        cell.askCodeListType = AskCodeListTypeDakaAnswered;
        cell.askModel = self.dataArr[indexPath.row];
        cell.isLastCell = (indexPath.row == self.dataArr.count - 1);
        cell.infoView.iconImgV.userInteractionEnabled = NO;
        return cell;
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    FMAskCodeModel *model = self.dataArr[indexPath.row];
    if (model.contentFileType.integerValue == 1 && (model.questionAnswerFlag.integerValue == 1 && model.checkStatus.integerValue == 1)) {
        return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMAskCodeVideoListCell class]) configuration:^(FMAskCodeVideoListCell *cell) {
            cell.askModel = self.dataArr[indexPath.row];
        }];
    } else {
        return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMAskCodeListCell class]) configuration:^(FMAskCodeListCell *cell) {
            cell.askCodeListType = AskCodeListTypeDakaAnswered;
            cell.askModel = self.dataArr[indexPath.row];
        }];
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    FMAskCodeModel *model = self.dataArr[indexPath.row];
    FMAskCodeDetailViewController *vc = [[FMAskCodeDetailViewController alloc] init];
    vc.questionId = model.askCodeId;
    [self.navigationController pushViewController:vc animated:YES];
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return [UIView new];
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

#pragma mark - HTTP
- (void)requestData {
    [HttpRequestTool getAskCodeCollectListWithQuestionIdPage:self.page pageSize:self.pageSize start:^{
    } failure:^{
        [self endRefreshForFailure];
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [self endRefreshForSuccess];

            // 根据total总数 确定分页的页码
            NSInteger pageNum = (([dic[@"total"] integerValue] + self.pageSize - 1) / self.pageSize);
            
            if (self.page == 1) {
                [self.tableView.mj_footer resetNoMoreData];
                [self.dataArr removeAllObjects];
            }
            
            NSArray *dataArr = [NSArray modelArrayWithClass:[FMAskCodeModel class] json:dic[@"data"]];
            [self removeRepeatDataWithArray:dataArr];
            if (self.page == pageNum) {
                [self.tableView.mj_footer endRefreshingWithNoMoreData];
            }
            if (!self.dataArr.count) {
                [self.tableView showNoDataViewWithImage:UPTImgInMarket2Module(@"个股/common_nodata") string:@"暂无问股" attributes:nil offsetY:80];
                self.tableView.mj_footer.hidden = YES;
            } else {
                [self.tableView dismissNoDataView];
                self.tableView.mj_footer.hidden = NO;
            }
            
            [self.tableView reloadData];
        } else {
            [self endRefreshForFailure];
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

#pragma mark - Notification
- (void)getCollectNumChangedNotification:(NSNotification *)noti {
    self.page = 1;
    self.currentPage = 1;
    [self requestData];
}

- (void)questionPaySuccess:(NSNotification *)noti {
    NSDictionary *userInfo = noti.userInfo;
    NSString *questionId = userInfo[@"questionId"];
    
    for (int i = 0; i<self.dataArr.count; i++) {
        FMAskCodeModel *model = self.dataArr[i];
        if ([model.askCodeId isEqualToString:questionId]) {
            model.questionPerm.type = @"3";
            [self.tableView reloadData];

            break;
        }
    }
}

- (void)praisedNotification:(NSNotification *)noti {
    NSDictionary *userInfo = noti.userInfo;
    NSString *questionId = userInfo[@"questionId"];
    
    for (int i = 0; i <self.dataArr.count; i++) {
        FMAskCodeModel *model = self.dataArr[i];
        if ([model.askCodeId isEqualToString:questionId]) {
            [[FMUserDataSyncManager sharedManager] likeQuestion:questionId];
            model.virtualSatisfiedNums = [NSString stringWithFormat:@"%ld",model.virtualSatisfiedNums.integerValue + 1];
            [self.tableView reloadData];
            
            break;
        }
    }
}

#pragma mark - Private
- (void)endRefreshForFailure {
    [self.tableView.mj_footer endRefreshing];
    [self.tableView.mj_header endRefreshing];
    self.page = self.currentPage;
}

- (void)endRefreshForSuccess {
    [self.tableView.mj_footer endRefreshing];
    [self.tableView.mj_header endRefreshing];
    self.currentPage = self.page;
}

- (void)removeRepeatDataWithArray:(NSArray *)array {
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    for (FMAskCodeModel *model in array) {
        [dic setObject:model forKey:model.askCodeId];
    }
    
    for (FMAskCodeModel *model in self.dataArr.reverseObjectEnumerator) {
        if ([dic valueForKey:model.askCodeId]) {
            [self.dataArr removeObject:model];
        }
    }
    
    [self.dataArr addObjectsFromArray:array];
}

- (void)configTableView {
    self.tableView.backgroundColor = UIColor.up_contentBgColor;
    self.tableView.tableFooterView = [UIView new];
    [self.tableView registerCellClass:[FMAskCodeVideoListCell class]];
    [self.tableView registerCellClass:[FMAskCodeListCell class]];
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;

    MJRefreshAutoNormalFooter *footer = [MJRefreshAutoNormalFooter footerWithRefreshingBlock:^{
        self.page++;
        [self requestData];
    }];
    footer.stateLabel.textColor = ColorWithHex(0xbfbfbf);
    self.tableView.mj_footer = footer;
    self.tableView.mj_footer.hidden = YES;
    
    self.tableView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
        self.page = 1;
        [self requestData];
    }];
}

#pragma mark - Getter/Setter
- (NSMutableArray *)dataArr {
    if (!_dataArr) {
        _dataArr = [NSMutableArray array];
    }
    
    return _dataArr;
}


@end
