//
//  FMRechargeViewController.m
//  QCYZT
//
//  Created by th on 17/2/20.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import "FMRechargeViewController.h"
#import "FMRechargeCenterTopCell.h"
#import "FMRechargeRecommendCell.h"
#import "FMRechargeReminderCell.h"
#import "FMRechargeHistoryViewController.h"
#import "HttpRequestTool+Recharge.h"
#import "BigCastDetailModel.h"
#import "FMRechargePayView.h"
#import "FMRechargeFirstRechargePopView.h"
#import "MAScreenShieldView.h"

@interface FMRechargeViewController() <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UIImageView *topBgImgV;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray *dataArr;
@property (nonatomic, strong) NSMutableArray *rechargeDataArr;
@property (nonatomic, strong) NSArray *firstRechareModels;
@property (nonatomic, strong) NSArray *rechargeActivityModels;
@property (nonatomic, strong) NSArray *rechargeModels;

@property (nonatomic, assign) NSInteger requestFlag;

@property (nonatomic, strong) NSMutableDictionary *signDic;


@end

@implementation FMRechargeViewController

- (void)loadView {
    if (@available(iOS 13.2, *)) {
        MAScreenShieldView *view = [MAScreenShieldView creactWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, [UIScreen mainScreen].bounds.size.height)];
        view.banScreenshot = YES;
        self.view = view;
    }else{
        [super loadView];
    }
    
    self.view.userInteractionEnabled = YES;
    self.view.backgroundColor = UIColor.up_contentBgColor;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self configSubViews];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(rechargeSuccess) name:kRechargeSuccess object:nil];
    // 首先查询签约状态
    [self requestSignStatus];
    
    [self judgeFirstRechargeShow];
}

-(void)configSubViews{
    self.view.backgroundColor = UIColor.up_contentBgColor;
    
    [self.view addSubview:self.topBgImgV];
    self.topBgImgV.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_Relative_WidthValue(300));
    
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(UI_SAFEAREA_TOP_HEIGHT);
        make.left.right.bottom.equalTo(0);
    }];
}

- (void)viewWillAppear:(BOOL)animated {
    self.selfNavigationBarHidden = YES;
    [super viewWillAppear:animated];
    [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleDefault;
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleLightContent;
}

- (void)dealloc {
    FMLog(@"%s", __func__);
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - Respond Method

- (void)back {
    if (self.backBlock) {
        self.backBlock();
        return;
    } else {
        [self.navigationController popViewControllerAnimated:YES];
    }
}

#pragma mark - UITableViewDelegate
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    if (self.requestFlag != 3) {
        return 0;
    }
    return self.dataArr.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (section == 0) {
        return 1;
    }
    NSArray *arr = self.dataArr[section];
    return arr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    NSArray *arr = self.dataArr[indexPath.section];
    if (indexPath.section == 0) {
        FMRechargeCenterTopCell *cell = [tableView reuseCellClass:[FMRechargeCenterTopCell class]];
        cell.bannerModels = arr.firstObject;
        FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
        cell.coinNum = [NSString stringWithFormat:@"%zd", userModel.coin];
        return cell;
    } else if (indexPath.section == 2) {
        FMRechargeReminderCell *cell = [tableView reuseCellClass:[FMRechargeReminderCell class]];
        [cell congfigCellWithStr:arr.firstObject];
        return cell;
    } else {
        NSDictionary *dic = arr[indexPath.row];
        FMRechargeRecommendCell *cell = [tableView reuseCellClass:[FMRechargeRecommendCell class]];
        cell.signDic = self.signDic;
        cell.title = dic[@"title"];
        cell.models = dic[@"data"];
        return cell;
    }
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

#pragma mark - HTTP
// 签约状态
- (void)requestSignStatus {
    [HttpRequestTool requestRechargeSignStatusWithStart:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD dismiss];
        [self.view showReloadNetworkViewWithBlock:^{
            [self requestSignStatus];
        }];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            self.signDic = dic.mutableCopy;
            [self requestOthers];
        } else {
            [SVProgressHUD dismiss];
            [self.view showReloadNetworkViewWithBlock:^{
                [self requestSignStatus];
            }];
        }
    }];
}

// 调用其他接口
- (void)requestOthers {
    self.requestFlag = 0;
    [self.rechargeDataArr removeAllObjects];
    [self requestMyCoins];
    [self requestRechargeList];
    [self requestActivityInfo];
}

/// 获取本账号拥有投顾币和积分
- (void)requestMyCoins {
    [HttpRequestTool queryDakaCoinsAndPointsStart:^{
    } failure:^{
        self.requestFlag += 1;
    } success:^(NSDictionary *dic) {
        self.requestFlag += 1;
    }];
}


/// 获取金币充值列表
- (void)requestRechargeList {
    [HttpRequestTool getRechargeListWithType:@"1" start:^{
    } failure:^{
        self.requestFlag += 1;
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            self.rechargeModels = [NSArray modelArrayWithClass:[FMRechargeListModel class] json:dic[@"data"][@"list"]];
            
            if ([dic[@"data"][@"notice"] length]) {
                NSString *reminderStr = [NSString stringWithFormat:@"温馨提示：%@", dic[@"data"][@"notice"]];
                self.dataArr[2] = @[reminderStr];
            }
        }
        
        self.requestFlag += 1;
    }];
}

/// 获取充值活动数据
-(void)requestActivityInfo {
    [HttpRequestTool getRechargeActivityInfoWitStart:^{
    } failure:^{
        self.requestFlag += 1;
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            NSArray *bannerModels = [NSArray modelArrayWithClass:[BigCastDetailBannerModel class] json:dic[@"data"][@"activities"]];
            if (bannerModels.count) {
                self.dataArr[0] = @[bannerModels];
            }
            
            self.firstRechareModels = [NSArray modelArrayWithClass:[FMRechargeListModel class] json:dic[@"data"][@"firstDiscountGoods"]];
            self.rechargeActivityModels = [NSArray modelArrayWithClass:[FMRechargeListModel class] json:dic[@"data"][@"goods"]];
        }
        
        self.requestFlag += 1;
    }];
}

#pragma mark - NSNotification
-(void)rechargeSuccess{
    // 弹窗移除
    [[[self.view subviews] bk_select:^BOOL(__kindof UIView * _Nonnull obj) {
        return [obj isKindOfClass:FMRechargePayView.class];
    }] bk_each:^(UIView * _Nonnull obj) {
        [obj removeFromSuperview];
    }] ;
    [self requestOthers];
}

#pragma mark - Private
- (void)enterToRechargeHistory {
    FMRechargeHistoryViewController *vc = [[FMRechargeHistoryViewController alloc] init];
    vc.type = @"1";
    [self.navigationController pushViewController:vc animated:YES];
}

- (void)judgeFirstRechargeShow {
    NSMutableDictionary *dic = [FMUserDefault getUnArchiverDataForKey:FirstRechargeDiscountShowKey];
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    if (![dic[[FMUserDefault getUserId]] boolValue] && userModel.isShowFirstDesc) {
        FMRechargeFirstRechargePopView *popView = [[FMRechargeFirstRechargePopView alloc] init];
        popView.imgUrl = [FMUserDefault getSeting:AppInit_FirstRecharge_URL];
        [popView show];
    }
}

#pragma mark - Getter/Setter
- (UIImageView *)topBgImgV {
    if (!_topBgImgV) {
        _topBgImgV = [[UIImageView alloc] initWithImage:FMImgInBundle(@"其它/recharge_top_bg")];
        _topBgImgV.contentMode = UIViewContentModeScaleAspectFill;
        _topBgImgV.clipsToBounds = YES;
        _topBgImgV.userInteractionEnabled = YES;
        
        UIView *navView = [[UIView alloc] init];
        [_topBgImgV addSubview:navView];
        [navView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(@0);
            make.top.equalTo(@(UI_STATUS_HEIGHT));
            make.height.equalTo(@(UI_NAVBAR_HEIGHT));
        }];
        navView.backgroundColor = FMClearColor;
        
        UILabel *navRightLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(15) textColor:UIColor.fm_market_nav_text_zeroColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentRight];
        navRightLabel.userInteractionEnabled = YES;
        navRightLabel.text = @"充值记录";
        [navRightLabel addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(enterToRechargeHistory)]];
        [navView addSubview:navRightLabel];
        [navRightLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(0);
            make.right.equalTo(-15);
        }];
        
        UIButton *backArrow = [[UIButton alloc] initWithFrame:CGRectZero font:nil normalTextColor:nil backgroundColor:FMClearColor title:nil image:FMImgInBundle(@"导航/亮黑暗灰返回") target:self action:@selector(back)];
        [navView addSubview:backArrow];
        [backArrow mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.top.bottom.equalTo(@0);
            make.width.equalTo(@(UI_NAVBAR_HEIGHT));
        }];
        
        UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(18) textColor:UIColor.fm_market_nav_text_zeroColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        [navView addSubview:titleLabel];
        [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(@0);
        }];
        titleLabel.text = @"充值中心";
    }
    
    return _topBgImgV;
}

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.bounces = NO;
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.backgroundColor = FMClearColor;
        _tableView.estimatedRowHeight = 180;
        _tableView.rowHeight = UITableViewAutomaticDimension;
        [_tableView registerCellClass:[FMRechargeCenterTopCell class]];
        [_tableView registerCellClass:[FMRechargeRecommendCell class]];
        [_tableView registerCellClass:[FMRechargeReminderCell class]];
    }
    
    return _tableView;
}

- (NSMutableArray *)dataArr {
    if (!_dataArr) {
        _dataArr = [NSMutableArray arrayWithArray:@[@[], self.rechargeDataArr, @[]]];
    }
    
    return _dataArr;
}

- (NSMutableArray *)rechargeDataArr {
    if (!_rechargeDataArr) {
        _rechargeDataArr = [NSMutableArray array];
    }
    
    return _rechargeDataArr;
}

- (void)setRequestFlag:(NSInteger)requestFlag {
    _requestFlag = requestFlag;
    if (_requestFlag >= 3) {
        [SVProgressHUD dismiss];
        
        if (self.firstRechareModels.count) {
            [self.rechargeDataArr addObject:@{@"title":@"首充活动专区", @"data":self.firstRechareModels}];
        }
        if (self.rechargeActivityModels.count) {
            [self.rechargeDataArr addObject:@{@"title":@"充值活动专区", @"data":self.rechargeActivityModels}];
        }
        if (self.rechargeModels.count) {
            [self.rechargeDataArr addObject:@{@"title":@"充值专区", @"data":self.rechargeModels}];
        }
        
        
        [self.tableView reloadData];
        _requestFlag = 0;
    }
}

@end
