//
//  FMPayTool.m
//  QCYZT
//
//  Created by macPro on 2017/6/14.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import "FMPayTool.h"
#import "YTGOtherWebVC.h"
#import "FMRechargeViewController.h"
#import "FMInvestorInfoViewController.h"
#import "FMIAPViewController.h"
#import "FMRechargePayView.h"
#import "FMRiskEvaluationReminderVC.h"
#import "HttpRequestTool+Pay.h"
#import "FMProgressHUD.h"


static NSString * const rechargeAlertTitle_infoAndfxcp = @"为保障您的权益及资金安全，请您在充值前完成投资者信息登记和风险能力测评";
static NSString * const rechargeAlertTitle_info = @"为保障您的权益及资金安全，请您在充值前完成投资者信息登记";
static NSString * const rechargeAlertTitle_fxcp = @"为保障您的权益及资金安全，请您在充值前完成风险能力测评";

static NSString * const consumptionAlertTitle_infoAndfxcp = @"为保障您的权益及资金安全，请您在投资消费前完成投资者信息登记和风险能力测评";
static NSString * const consumptionAlertTitle_info = @"为保障您的权益及资金安全，请您在投资消费前完成投资者信息登记";
static NSString * const consumptionAlertTitle_fxcp = @"为保障您的权益及资金安全，请您在投资消费前完成风险能力测评";

static NSString * const alertMessage = @"\n注：以上信息仅作监管备案用，不会以任何形式泄露给第三方，请放心填写";




typedef void (^registerAndFxcpFinishBlock)();

@interface FMPayTool()

@end

@implementation FMPayTool

+ (FMPayTool *)payTool {
    static FMPayTool *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc]init];
    });
    return instance;
}

#pragma mark -- 充值金币
- (void)gotoRecharge {
//    // 充值金币
    UIViewController *currentVC = [FMHelper getCurrentVC];  // 这个位置必须记录下调用方法的控制器对象
    WEAKSELF
    [self checkOutRegistrFlagAndAnswerFlagWithCurrentVC:currentVC result:^(BOOL success) {
        // 已登记 已测评  直接进充值
        if (success) {
            [__weakSelf turnOnRechargeViewControllerIsHaveBackBlock:success currentVC:currentVC];
        }
    }];
}

#pragma mark --  isHaveBackBlock控制是否要实现backBlock
- (void)turnOnRechargeViewControllerIsHaveBackBlock:(BOOL)isHaveBackBlock currentVC:(UIViewController *)currentVC {
    BOOL isIAPPay = [FMHelper getIAPPayStatus];
    if (isIAPPay) {
        // 内购充值
        [self goIAPPayViewControllerWithIsHaveBackBlock:isHaveBackBlock currentVC:currentVC];
    } else {
        // 微信充值
        [self goWeiChatPayViewControllerWithIsHaveBackBlock:isHaveBackBlock currentVC:currentVC];
    }
}

#pragma mark -- 金币微信充值
- (void)goWeiChatPayViewControllerWithIsHaveBackBlock:(BOOL)isHaveBackBlock currentVC:(UIViewController *)currentVC {
    FMRechargeViewController *vc = [[FMRechargeViewController alloc] init];
    if (isHaveBackBlock) {
        vc.backBlock = ^(){
            [currentVC.navigationController popToViewController:currentVC animated:YES];
        };
    }
    [currentVC.navigationController pushViewController:vc animated:YES];
}

#pragma mark -- 内购充值页面（金币和VIP月卡内购都在这边）
- (void)goIAPPayViewControllerWithIsHaveBackBlock:(BOOL)isHaveBackBlock currentVC:(UIViewController *)currentVC {
    FMIAPViewController *IAPVc = [[FMIAPViewController alloc] init];
    if (isHaveBackBlock) {
        IAPVc.IAPBackBlock = ^(){
            [currentVC.navigationController popToViewController:currentVC animated:YES];
        };
    }
    [currentVC.navigationController pushViewController:IAPVc animated:YES];
}

#pragma mark -- 领取金币
- (void)gotoGetCoin {
    [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
        YTGOtherWebVC *webVC = [[YTGOtherWebVC alloc] init];
        webVC.startPage = [NSString stringWithFormat:@"%@%@",prefix,kAPI_UserCenter_LQJL];
        webVC.titleStr = @"领取金币";
        [[FMHelper getCurrentVC].navigationController pushViewController:webVC animated:YES];
    }];
    
}

#pragma mark -- 直接拉起充值弹框
- (void)rechargeDialogWithProductId:(NSString *)productId payPrice:(NSString *)payPrice payInfo:(NSString *)payInfo type:(NSString *)type orderBlock:(void(^)(NSString *, NSString *))orderBlock {
    // 充值金币
    UIViewController *currentVC = [FMHelper getCurrentVC];  // 这个位置必须记录下调用方法的控制器对象
    [self checkOutRegistrFlagAndAnswerFlagWithCurrentVC:currentVC result:^(BOOL success) {
            // 已登记 已测评  直接拉起弹框
        if (success) {
            [[FMHelper getCurrentVC].navigationController popToViewController:currentVC animated:YES];
        }
        FMRechargePayView *payView = [[FMRechargePayView alloc] init];
        [currentVC.view addSubview:payView];
        payView.payPrice = [payPrice floatValue];
        payView.productId = productId;
        payView.infoStr = [payInfo stringByRemovingPercentEncoding];
        payView.type = type;
        payView.orderBlock = orderBlock;
        payView.hiddenAgreenArea = YES;
        [payView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(currentVC.view);
        }];
    }];
    
}


#pragma mark --  检查是否登记个人信息和风险测评，返回YES代表已经登记和测评
- (void)checkOutRegistrFlagAndAnswerFlagWithCurrentVC:(UIViewController *)currentVC result:(void(^)(BOOL success))result {
    NSInteger registrFlag = [FMUserDefault getUserRegistration]; //是否登记过
    NSInteger answerFlag = [FMUserDefault getUserAnswerFlag];   //是否测评过
    
    if (!registrFlag && answerFlag == -1) {
        // 未登记 未测评
        [self gotoInvestorInfoOrAssessmentWithType:0 andTitle:rechargeAlertTitle_infoAndfxcp andMessage:alertMessage andFinishBlock:^{
            if (result) {
                result(YES);
            }
        }];
        return;
    }
    if (registrFlag && answerFlag == -1) {
        // 已登记，未测评
        [self gotoInvestorInfoOrAssessmentWithType:2 andTitle:rechargeAlertTitle_fxcp andMessage:alertMessage andFinishBlock:^ {
            if (result) {
                result(YES);
            }
        }];
        return;
    }
    if (!registrFlag && answerFlag != -1) {
        // 未登记 已测评
        [self gotoInvestorInfoOrAssessmentWithType:1 andTitle:rechargeAlertTitle_info andMessage:alertMessage andFinishBlock:^ {
            if (result) {
                result(YES);
            }
        }];
        return;
    }
    result(YES);
}

#pragma mark --  检查是否登记个人信息和风险测评，笔记券使用指引时 用到
- (void)checkOutRegistrFlagAndAnswerFlagResult:(void(^)())result {
    NSInteger registrFlag = [FMUserDefault getUserRegistration]; //是否登记过
    NSInteger answerFlag = [FMUserDefault getUserAnswerFlag];   //是否测评过
    NSString *tipStr = @"";
    UIViewController *currentVC = [FMHelper getCurrentVC];  // 这个位置必须记录下调用方法的控制器对象
    __weak typeof(currentVC) __weakCurrentVC = currentVC;
    if (!registrFlag && answerFlag == -1) {
        // 未登记 未测评
        tipStr = @"请先完成“信息登记”和“风险测评”，完成后也能得到对应的新手任务奖励。";
        [PushMessageView showCloseWithTitle:nil message:tipStr noticeImage:nil sureTitle:@"立即前往" clickSure:^{
            FMInvestorInfoViewController *vc = [[FMInvestorInfoViewController alloc] init];
            vc.finishRegist = ^(){
                // 登记完成调用的Block
                // 未测评，进入测评
                FMRiskEvaluationReminderVC *vc1 = [[FMRiskEvaluationReminderVC alloc] init];
                vc1.finishBlock = ^{
                    [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
                        YTGOtherWebVC *webVC = [[YTGOtherWebVC alloc] init];
                        webVC.startPage = [NSString stringWithFormat:@"%@%@?versionCode=%@", prefix,kAPI_RiskEvalua_FXCP,APP_BUILD_VERSION];
                        webVC.titleStr = @"";
                        webVC.backBlock = ^(){
                            [[FMHelper getCurrentVC].navigationController popToViewController:__weakCurrentVC animated:YES];
                        };
                        webVC.fxcpFinishBlock = ^(){
                            [__weakCurrentVC.navigationController popToViewController:__weakCurrentVC animated:YES];
                            if (result) {
                                result();
                            }
                        };
                        [__weakCurrentVC.navigationController pushViewController:webVC animated:YES];
                    }];
                };
                [__weakCurrentVC.navigationController pushViewController:vc1 animated:YES];
            };
            [__weakCurrentVC.navigationController pushViewController:vc animated:YES];
        }];
    }
    if (registrFlag && answerFlag == -1) {
        // 已登记，未测评
        tipStr = @"请先完成“风险测评”，完成后也能得到对应的新手任务奖励。";
        [PushMessageView showCloseWithTitle:nil message:tipStr noticeImage:nil sureTitle:@"立即前往" clickSure:^{
            FMRiskEvaluationReminderVC *vc = [[FMRiskEvaluationReminderVC alloc] init];
            vc.finishBlock = ^{
                [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
                    YTGOtherWebVC *webVC = [[YTGOtherWebVC alloc] init];
                    webVC.startPage = [NSString stringWithFormat:@"%@%@?versionCode=%@", prefix,kAPI_RiskEvalua_FXCP,APP_BUILD_VERSION];
                    webVC.titleStr = @"";
                    webVC.fxcpFinishBlock = ^(){
                        // 测评完成之后调用的Block （在此做测评完成之后的事情）
                        [__weakCurrentVC.navigationController popToViewController:currentVC animated:YES];
                        if (result) {
                            result();
                        }
                    };
                    [currentVC.navigationController pushViewController:webVC animated:YES];
                }];
            };
            [currentVC.navigationController pushViewController:vc animated:YES];
        }];

    }
    if (!registrFlag && answerFlag != -1) {
        // 未登记 已测评
        tipStr = @"请先完成“信息登记”，完成后也能得到对应的新手任务奖励。";
       [PushMessageView showCloseWithTitle:nil message:tipStr noticeImage:nil sureTitle:@"立即前往" clickSure:^{
            FMInvestorInfoViewController *vc = [[FMInvestorInfoViewController alloc] init];
            vc.finishRegist = ^(){
                // 登记完成调用的Block
                [__weakCurrentVC.navigationController popToViewController:currentVC animated:YES];
                if (result) {
                    result();
                }
            };
            [currentVC.navigationController pushViewController:vc animated:YES];
        }];
    }
    if (tipStr.length == 0)  {
        if(result) {
            result();
        }
    }
}

#pragma mark -- 金币余额不足时，查询金币和积分（只在接口返回501时调用）
- (void)lackOfBalanceToQueryDakaCoins {
    [HttpRequestTool queryDakaCoinsAndPointsStart:^{

    } failure:^{
        FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
        [self showCoinLackAlertWithBalanceForHttp:userModel.coin];
    } success:^(NSDictionary *dic) {
        FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
        NSInteger coins = userModel.coin;
        [self showCoinLackAlertWithBalanceForHttp:coins];
    }];
}

#pragma mark -- 金币余额不足弹框（接口返回501时使用）
- (void)showCoinLackAlertWithBalanceForHttp:(NSInteger)balance {
    WEAKSELF;
    BOOL isRechargeOpened = [[FMUserDefault getSeting:@"isRechargeOpened"] boolValue];
    if (isRechargeOpened) {
        ShowConfirm([FMHelper getCurrentVC].navigationController, nil, [NSString stringWithFormat:@"您当前拥有%zd金币，余额不足，是否立即充值？", balance], @"取消", @"确定", ^{
            // 取消
        }, ^{
            // 确定
            [__weakSelf gotoRecharge];
        });
    } else {
        ShowConfirm([FMHelper getCurrentVC].navigationController, nil, [NSString stringWithFormat:@"您当前拥有%zd金币，余额不足，是否立即领取？", balance], @"取消", @"确定", ^{
            // 取消
        }, ^{
            // 确定
            [__weakSelf gotoGetCoin];
        });
    }
}

#pragma mark --  比较金币余额和此次消费金币
- (void)compareCoinRemainderWithConsume:(NSString *)consume clickView:(UIView *)clickView completeBlock:(void (^)(NSString *coinRemainder))completeBlock {
    [HttpRequestTool queryDakaCoinsAndPointsStart:^{
        [SVProgressHUD show];
        if (clickView) {
            clickView.userInteractionEnabled = NO;
        }
    } failure:^{
//        [SVProgressHUD dismiss];  保证compareCoinRemainderWithConsume方法跟着judgeConfirmOrderStatusWithDakaId方法，则此处不要dismiss
        if (clickView) {
            clickView.userInteractionEnabled = YES;
        }
        FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
        if (userModel.coin < [consume integerValue]) {
            [SVProgressHUD dismiss];
            [self showCoinLackAlertWithBalance:[NSString stringWithFormat:@"%zd", userModel.coin] custom:consume];
            return;
        }

        if (completeBlock) {
            completeBlock([NSString stringWithFormat:@"%zd", userModel.coin]);
        }
    } success:^(NSDictionary *dic) {
//        [SVProgressHUD dismiss];  保证compareCoinRemainderWithConsume方法跟着judgeConfirmOrderStatusWithDakaId方法，则此处不要dismiss
        if (clickView) {
            clickView.userInteractionEnabled = YES;
        }
        
        FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
        NSString *coins = [NSString stringWithFormat:@"%zd", userModel.coin];
        if (userModel.coin < [consume floatValue]) { // 金币余额不足
            [SVProgressHUD dismiss];
            [self showCoinLackAlertWithBalance:coins custom:consume];
            return;
        }

        if (completeBlock) {
            completeBlock(coins);
        }
    }];
}

#pragma mark -- 金币不足弹框（消费时候调用）
- (void)showCoinLackAlertWithBalance:(NSString *)balance custom:(NSString *)custom{
    WEAKSELF;
    BOOL isRechargeOpened = [[FMUserDefault getSeting:@"isRechargeOpened"] boolValue];
    if (isRechargeOpened) {
        ShowConfirm([FMHelper getCurrentVC].navigationController, nil, [NSString stringWithFormat:@"您当前拥有%@金币，本次消费需要%@金币，余额不足，是否立即充值？", balance, custom], @"取消", @"确定", ^{
            // 取消
        }, ^{
            // 确定
            [__weakSelf gotoRecharge];
        });
    } else {
        ShowConfirm([FMHelper getCurrentVC].navigationController, nil, [NSString stringWithFormat:@"您当前拥有%@金币，本次消费需要%@金币，余额不足，是否立即领取？", balance, custom], @"取消", @"确定", ^{
            // 取消
        }, ^{
            // 确定
            [__weakSelf gotoGetCoin];
        });
    }
}

#pragma mark -- 判断是否弹支付确认书
- (void)judgeConfirmOrderStatusWithDakaId:(NSString *)dakaId certCode:(NSString *)certCode clickView:(UIView *)clickView confirmOperation:(void (^)())confirmOperationBlock {
    [self judgeConfirmOrderStatusWithDakaId:dakaId certCode:certCode clickView:clickView confirmOperation:confirmOperationBlock failurBlock:nil];
}

- (void)judgeConfirmOrderStatusWithDakaId:(NSString *)dakaId certCode:(NSString *)certCode clickView:(UIView *)clickView confirmOperation:(void (^)())confirmOperationBlock failurBlock:(void (^)())failurBlock {
    UIViewController *currentVC = [FMHelper getCurrentVC];
    
    if (clickView) {
        clickView.userInteractionEnabled = NO;
    }
    
    [HttpRequestTool payConfirmOrderWithBigNameId:dakaId start:^{
        [FMProgressHUD showProgressHUDViewInView:nil];
    } failure:^{
        [FMProgressHUD showTextOnlyInView:nil withText:@"网络不给力"];
        if (clickView) {
            clickView.userInteractionEnabled = YES;
        }
    } success:^(NSDictionary *dic) {
        [FMProgressHUD hiddenProgressHUDViewInView:nil];
        if ([dic[@"status"] isEqualToString:@"1"] && [dic[@"data"] isKindOfClass:[NSDictionary class]]) {
            NSInteger status = [[NSString stringWithFormat:@"%@",dic[@"data"][@"status"]] integerValue];
            __weak typeof(currentVC) __weakCurrentVC = currentVC;
            WEAKSELF;
            switch (status) {
                case -3: {
                    if (failurBlock) {
                        failurBlock();
                    }
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        // 未登记，未测评
                        [self gotoInvestorInfoOrAssessmentWithType:0 andTitle:consumptionAlertTitle_infoAndfxcp andMessage:alertMessage andFinishBlock:^ {
                            [__weakCurrentVC.navigationController popToViewController:__weakCurrentVC animated:YES];
                        }];
                    });
                }
                    break;
                    
                case -2: {
                    if (failurBlock) {
                        failurBlock();
                    }
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        // 未登记，已测评
                        [self gotoInvestorInfoOrAssessmentWithType:1 andTitle:consumptionAlertTitle_info andMessage:alertMessage andFinishBlock:^ {
                            [__weakCurrentVC.navigationController popToViewController:__weakCurrentVC animated:YES];
                        }];
                    });
                }
                    break;
                    
                case -1: {
                    if (failurBlock) {
                        failurBlock();
                    }
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        // 已登记，未测评
                        [self gotoInvestorInfoOrAssessmentWithType:2 andTitle:consumptionAlertTitle_fxcp andMessage:alertMessage andFinishBlock:^ {
                            [__weakCurrentVC.navigationController popToViewController:__weakCurrentVC animated:YES];
                        }];
                    });
                }
                    break;
                    
                case 0: {
                    confirmOperationBlock();
                }
                    break;
                    
                case 1:
                {
                    if (failurBlock) {
                        failurBlock();
                    }
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        ShowAlert(__weakCurrentVC, nil, [NSString stringWithFormat:@"%@",dic[@"data"][@"message"]], @"确定", ^{
                        });
                    });
                }
                    break;
                    
                case 2:
                {
                    if (failurBlock) {
                        failurBlock();
                    }
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        // 弹确认书
                        [__weakSelf showPayConfirmOrderWithDic:dic confirmOperation:confirmOperationBlock currentVC:__weakCurrentVC];
                    });
                }
                    break;
                    
                default: {
                    //（如果返回的这几种类型都没有，直接走默认支付）
                    confirmOperationBlock();
                }
                    break;
            }
        } else {
            [FMProgressHUD showTextOnlyInView:nil withText:dic[@"errmessage"]];
        }
        if (clickView) {
            clickView.userInteractionEnabled = YES;
        }
    }];
}

#pragma mark -- 验证信息登记、风险测评弹框Alert
- (void)gotoInvestorInfoOrAssessmentWithType:(int)type andTitle:(NSString *)title andMessage:(NSString *)message andFinishBlock:(registerAndFxcpFinishBlock)finishBlock {
    
    UIViewController *currentVC = [FMHelper getCurrentVC];  // 这个位置必须记录下调用方法的控制器对象
    __weak typeof(currentVC) __weakCurrentVC = currentVC;
    ShowConfirm(currentVC.navigationController, title, message, @"取消", @"确定", ^{
        // 取消
    }, ^{
        // 确定
        switch (type) {
            // 未登记 未测评
            case 0: {
                FMInvestorInfoViewController *vc = [[FMInvestorInfoViewController alloc] init];
                vc.finishRegist = ^(){
                    // 登记完成调用的Block
                    // 未测评，进入测评
                    FMRiskEvaluationReminderVC *vc1 = [[FMRiskEvaluationReminderVC alloc] init];
                    vc1.finishBlock = ^{
                        [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
                            YTGOtherWebVC *webVC = [[YTGOtherWebVC alloc] init];
                            webVC.startPage = [NSString stringWithFormat:@"%@%@?versionCode=%@", prefix,kAPI_RiskEvalua_FXCP,APP_BUILD_VERSION];
                            webVC.titleStr = @"";
                            webVC.backBlock = ^(){
                                [[FMHelper getCurrentVC].navigationController popToViewController:__weakCurrentVC animated:YES];
                            };
                            webVC.fxcpFinishBlock = ^(){
                                if (finishBlock) {
                                    finishBlock();
                                }
                            };
                            [__weakCurrentVC.navigationController pushViewController:webVC animated:YES];
                        }];
                    };
                    [__weakCurrentVC.navigationController pushViewController:vc1 animated:YES];
                };
                [__weakCurrentVC.navigationController pushViewController:vc animated:YES];
            } break;
            
            // 未登记 已测评
            case 1: {
                FMInvestorInfoViewController *vc = [[FMInvestorInfoViewController alloc] init];
                vc.finishRegist = ^(){
                    // 登记完成调用的Block
                    if (finishBlock) {
                        finishBlock();
                    }
                };
                [currentVC.navigationController pushViewController:vc animated:YES];
            } break;
            
            // 已登记 未测评
            case 2: {
                FMRiskEvaluationReminderVC *vc = [[FMRiskEvaluationReminderVC alloc] init];
                vc.finishBlock = ^{
                    [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
                        YTGOtherWebVC *webVC = [[YTGOtherWebVC alloc] init];
                        webVC.startPage = [NSString stringWithFormat:@"%@%@?versionCode=%@", prefix,kAPI_RiskEvalua_FXCP,APP_BUILD_VERSION];
                        webVC.titleStr = @"";
                        webVC.fxcpFinishBlock = ^(){
                            // 测评完成之后调用的Block （在此做测评完成之后的事情）
                            if (finishBlock) {
                                finishBlock();
                            }
                        };
                        [currentVC.navigationController pushViewController:webVC animated:YES];
                    }];
                };
                [currentVC.navigationController pushViewController:vc animated:YES];
            } break;
            default:
                break;
        }
    });
}

#pragma mark - 弹支付确认书
- (void)showPayConfirmOrderWithDic:(NSDictionary *)dic confirmOperation:(void (^)())confirmOperationBlock currentVC:(UIViewController *)currentVC {
    YTGOtherWebVC *webVC = [[YTGOtherWebVC alloc] init];
    webVC.startPage = [NSString stringWithFormat:@"%@",dic[@"data"][@"link"]];
    webVC.titleStr = @"";
    webVC.finishEvaluate = ^{
        [[FMHelper getCurrentVC].navigationController popViewControllerAnimated:YES];
        confirmOperationBlock();
    };
    [currentVC.navigationController pushViewController:webVC animated:YES];
}

#pragma mark -- 没有可用的支付方式时候的处理（501金币余额不足   502没有月卡  503没有券）
- (void)noEnablePayModelWithErrorCode:(NSInteger)errorCode errorText:(NSString *)errorText {
    switch (errorCode) {
        case 501: {
            // 金币余额不足
            [self lackOfBalanceToQueryDakaCoins];
        }
            break;
        case 502: {
            // 跳转到vip中心
            ShowConfirm([FMHelper getCurrentVC], nil, errorText, @"取消", @"确定", nil, ^{
                [ProtocolJump jumpWithUrl:@"qcyzt://vipCenter"];
            });
        }
            break;
        case 503: {
            // 无券
            ShowAlert([FMHelper getCurrentVC], nil, errorText.length > 0? errorText:@"您没有可用的券，参与活动即有机会获取。", @"确定", nil);
        }
            break;
        default:
            ShowAlert([FMHelper getCurrentVC], nil, @"支付异常，请重试或联系客服解决！", @"确定", nil);
            break;
    }
}

@end
