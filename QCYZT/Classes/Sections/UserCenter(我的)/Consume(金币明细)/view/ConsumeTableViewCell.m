//
//  ConsumeTableViewCell.m
//  QCYZT
//
//  Created by leo on 16/9/1.
//  Copyright © 2016年 sdcf. All rights reserved.
//

#import "ConsumeTableViewCell.h"

@interface ConsumeTableViewCell()
@property (strong, nonatomic) UILabel *contentLabel;
@property (strong, nonatomic) UILabel *timeLabel;
@property (strong, nonatomic) UILabel *consumeLabel;//交易金额
@property (strong, nonatomic) UILabel *titleLabel;
@property (nonatomic,weak) UIView *sepLine;

@end
@implementation ConsumeTableViewCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    
    if (self) {
        self.contentView.backgroundColor = UIColor.up_contentBgColor;
        UIView *line = [UIView new];
        [self.contentView addSubview:line];
        [line mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self);
            make.left.equalTo(@15);
            make.right.equalTo(@-15);
            make.height.equalTo(@0.5);
        }];
        self.sepLine = line;
        line.backgroundColor = UIColor.fm_sepline_color;
    }
    return self;
}


- (void)setModel:(ConsumeModel *)model {
    _model = model;
    
    self.contentLabel.text = model.title;
    
    self.consumeLabel.textColor = self.type == ConsumeTypeIncome ? UIColor.up_riseColor : UIColor.up_fallColor;
    NSString *consumePointsText = @"";
    if (model.points) {
        if (self.type == ConsumeTypeIncome) {
            consumePointsText = [@"积分+" stringByAppendingFormat:@"%zd", model.points];
        } else if (self.type == ConsumeTypeExpend) {
            consumePointsText = [@"积分-" stringByAppendingFormat:@"%zd", model.points];
        }
    }
    NSString *consumeCoinsText = @"";
    if (model.coin) {
        if (self.type == ConsumeTypeIncome) {
            consumeCoinsText = [@"金币+" stringByAppendingFormat:@"%zd", model.coin];
        } else if (self.type == ConsumeTypeExpend) {
            consumeCoinsText = [@"金币-" stringByAppendingFormat:@"%zd", model.coin];
        }
    }
    if (model.points && model.coin) {
        self.consumeLabel.text = [NSString stringWithFormat:@"%@/%@", consumePointsText, consumeCoinsText];
    } else if (model.points) {
        self.consumeLabel.text = consumePointsText;
    } else if (model.coin) {
        self.consumeLabel.text = consumeCoinsText;
    } else {
        self.consumeLabel.text = @"0";
    }

    NSDate *nowDate = [NSDate dateWithTimeIntervalSince1970:model.createTime/1000];
    if ([nowDate isToday]) {
        self.timeLabel.text = [NSString stringFromDate:nowDate format:@"HH:mm"];
    } else {
        if ([nowDate isThisYear]) {
            self.timeLabel.text = [NSString stringFromDate:nowDate format:@"MM-dd HH:mm"];
        } else {
            self.timeLabel.text = [NSString stringFromDate:nowDate format:@"yyyy-MM-dd"];
        }
    }
    
    self.titleLabel.text = model.content;
}

- (void)setIsFirstCell:(BOOL)isFirstCell {
    _isFirstCell = isFirstCell;
    
    if (isFirstCell) {
        self.sepLine.hidden = YES;
    } else {
        self.sepLine.hidden = NO;
    }
}

#pragma mark - lazy load
//
-(UILabel *)contentLabel {
    if (!_contentLabel) {
        _contentLabel = [[UILabel alloc] init];
        [self.contentView addSubview:_contentLabel];
        [_contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self).offset(15.0);
            make.top.equalTo(self).offset(15.0);
            make.height.equalTo(@22.5);
        }];
        _contentLabel.textColor = UIColor.up_textPrimaryColor;
        _contentLabel.font = BoldFontWithSize(16);
    }
    
    return _contentLabel;
}

// 时间
-(UILabel *)timeLabel {
    if (!_timeLabel) {
        _timeLabel = [[UILabel alloc] init];
        [self.contentView addSubview:_timeLabel];
        [_timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.consumeLabel);
            make.top.equalTo(self.contentLabel.mas_bottom).offset(5.0);
            make.height.equalTo(16.5);
        }];
        _timeLabel.textColor = UIColor.up_textSecondaryColor;
        _timeLabel.font = FontWithSize(12);
    }
    
    return _timeLabel;
}

// 标题
- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        [self.contentView addSubview:_titleLabel];
        [_titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.contentLabel);
            make.centerY.equalTo(self.timeLabel);
            make.right.lessThanOrEqualTo(self.timeLabel.mas_left).offset(-15);
        }];
        [_titleLabel setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
        _titleLabel.textColor = UIColor.up_textSecondaryColor;
        _titleLabel.font = FontWithSize(12);
    }
    
    return _titleLabel;
}

// 金币数量
- (UILabel *)consumeLabel {
    if (!_consumeLabel) {
        _consumeLabel = [[UILabel alloc] init];
        [self.contentView addSubview:_consumeLabel];
        [_consumeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.mas_right).offset(-15.0);
            make.centerY.equalTo(self.contentLabel);
        }];
        _consumeLabel.textColor = UIColor.up_riseColor;
        _consumeLabel.font = FontWithSize(16);
    }
    return _consumeLabel;
}

@end
