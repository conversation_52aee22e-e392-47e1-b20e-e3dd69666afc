//
//  ConsumeModel.h
//  QCYZT
//
//  Created by leo on 16/9/1.
//  Copyright © 2016年 sdcf. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface ConsumeModel : NSObject
@property (strong, nonatomic) NSString *type; // 消费类型（1：看笔记OR视频，2,：提问，3：偷听）
@property (assign, nonatomic) NSInteger coin; // 消费金币数量
@property (assign, nonatomic) NSInteger points; // 消费积分数量
@property (nonatomic,copy) NSString *contentId; // 阅读的内容id  或者 问股id
@property (nonatomic,copy) NSString *consumeId; // 消费记录id
@property (nonatomic,copy) NSString *userId; // 用户id
@property (copy, nonatomic) NSString *title;//类型
@property (copy, nonatomic) NSString *content;//内容
@property (assign, nonatomic) long  long createTime;//创建日期    long

@end
