//
//  FMSearchAskCodeViewController.m
//  QCYZT
//
//  Created by macPro on 2018/3/2.
//  Copyright © 2018年 sdcf. All rights reserved.
//

#import "FMSearchAskCodeViewController.h"
#import "FMAskCodeListCell.h"
#import "FMAskCodeVideoListCell.h"
#import "FMAskCodeModel.h"
#import "FMAskCodeDetailViewController.h"

@interface FMSearchAskCodeViewController ()

@end

@implementation FMSearchAskCodeViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    [self.tableView registerCellClass:[FMAskCodeListCell class]];
    [self.tableView registerCellClass:[FMAskCodeVideoListCell class]];
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;

    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(questionPaySuccess:) name:kQuestionPaySuccess object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(praisedNotification:) name:kAskCodePriasedNotification object:nil];
}


#pragma mark - UIColletionViewDelegate  DataSoure
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.datas.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    FMAskCodeModel *model = self.datas[indexPath.row];
    if (model.contentFileType.integerValue == 1 && (model.questionAnswerFlag.integerValue == 1 && model.checkStatus.integerValue == 1)) {
        //视频问股
        FMAskCodeVideoListCell *cell = [tableView reuseCellClass:[FMAskCodeVideoListCell class]];
        cell.searchKeyWord = self.searchWord;
        cell.askModel = model;
        cell.isLastCell = (indexPath.row == self.datas.count - 1);
        return cell;
    } else {
        //语音问股
        FMAskCodeListCell *cell = [tableView reuseCellClass:[FMAskCodeListCell class]];
        cell.askCodeListType = AskCodeListTypeAll;
        cell.searchKeyWord = self.searchWord;
        cell.askModel = self.datas[indexPath.row];
        cell.isLastCell = (indexPath.row == self.datas.count - 1);
        return cell;
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    FMAskCodeModel *model = self.datas[indexPath.row];
    if (model.contentFileType.integerValue == 1) {
        return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMAskCodeVideoListCell class]) configuration:^(FMAskCodeVideoListCell *cell) {
            cell.askModel = self.datas[indexPath.row];
        }];
    } else {
        return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMAskCodeListCell class]) configuration:^(FMAskCodeListCell *cell) {
            cell.askCodeListType = AskCodeListTypeAll;
            cell.askModel = self.datas[indexPath.row];
        }];
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    [super tableView:tableView didSelectRowAtIndexPath:indexPath];
    
    FMAskCodeModel *model = self.datas[indexPath.row];
    FMAskCodeDetailViewController *vc = [[FMAskCodeDetailViewController alloc] init];
    vc.questionId = model.askCodeId;
    [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
}

#pragma mark - Notification
- (void)questionPaySuccess:(NSNotification *)noti {
    NSDictionary *userInfo = noti.userInfo;
    NSString *questionId = userInfo[@"questionId"];
    
    for (int i = 0; i <self.datas.count; i++) {
        FMAskCodeModel *model = self.datas[i];
        if ([model.askCodeId isEqualToString:questionId]) {
            model.questionPerm.type = @"3";
            [self.tableView reloadData];
            
            break;
        }
    }
}

- (void)praisedNotification:(NSNotification *)noti {
    NSDictionary *userInfo = noti.userInfo;
    NSString *questionId = userInfo[@"questionId"];
    
    for (int i = 0; i <self.datas.count; i++) {
        FMAskCodeModel *model = self.datas[i];
        if ([model.askCodeId isEqualToString:questionId]) {
            [[FMUserDataSyncManager sharedManager] likeQuestion:questionId];
            model.virtualSatisfiedNums = [NSString stringWithFormat:@"%ld",model.virtualSatisfiedNums.integerValue + 1];
            [self.tableView reloadData];
            
            break;
        }
    }
}

#pragma mark - Private
- (void)removeRepeatDataWithArray:(NSArray *)array {
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    for (FMAskCodeModel *model in array) {
        [dic setObject:model forKey:model.askCodeId];
    }
    
    for (FMAskCodeModel *model in self.datas.reverseObjectEnumerator) {
        if ([dic valueForKey:model.askCodeId]) {
            [self.datas removeObject:model];
        }
    }
    
    [self.datas addObjectsFromArray:array];
    
    if (!self.datas.count) {
        self.tableView.mj_footer.hidden = YES;
    } else {
        self.tableView.mj_footer.hidden = NO;
    }
}

#pragma mark - 重写父类方法
- (void)dealData:(NSDictionary *)dataDict {
    NSArray *dataArr = [NSArray modelArrayWithClass:[FMAskCodeModel class] json:dataDict[@"data"]];
    if (dataArr.count < self.pageSize) {
        [self.tableView.mj_footer endRefreshingWithNoMoreData];
    }
    [self removeRepeatDataWithArray:dataArr];

    [super dealData:dataDict];
}


@end
