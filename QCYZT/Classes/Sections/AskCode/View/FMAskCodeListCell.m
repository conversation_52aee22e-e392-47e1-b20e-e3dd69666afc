//
//  FMAskCodeListCell.m
//  QCYZT
//
//  Created by zeng on 2021/11/15.
//  Copyright © 2021 LZKJ. All rights reserved.
//

#import "FMAskCodeListCell.h"
#import "UIImage+stocking.h"

@interface FMAskCodeListCell()

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *watchListenLabel;
@property (nonatomic, strong) UIButton *praiseBtn;
@property (nonatomic, strong) UIImageView *voiceImgV; // 语音
@property (nonatomic, strong) UILabel *listionPriceLabel;
@property (nonatomic, strong) UILabel *durationLabel; // 时长

@property (nonatomic, strong) UIView *bottomLine;

@end

@implementation FMAskCodeListCell

- (id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setUp];
    }
    return  self;
}

- (void)setUp {
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    self.backgroundColor = self.contentView.backgroundColor = UIColor.up_contentBgColor;
    
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:16] textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:0 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:titleLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.right.equalTo(@-15);
        make.top.equalTo(@18);
    }];
    self.titleLabel = titleLabel;
    
    DakaInfoNewView *infoView = [[DakaInfoNewView alloc] init];
    [self.contentView addSubview:infoView];
    [infoView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.top.equalTo(titleLabel.mas_bottom).offset(12);
        make.right.equalTo(@0);
//        make.height.equalTo(@40);
    }];
    infoView.tagView.hidden = YES;
    self.infoView = infoView;
    
    UIView *gesView = [[UIView alloc] init];
    [self.contentView addSubview:gesView];
    [gesView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@-15);
        make.centerY.equalTo(infoView.timeLabel);
        make.height.equalTo(@20);
    }];
    gesView.userInteractionEnabled = YES;
    [gesView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithActionBlock:^(id  _Nonnull sender) {
        // 屏蔽点击跳转到详情
    }]];
    
    UIButton *praiseBtn = [[UIButton alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:12] normalTextColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor title:@"点赞" image:FMImgInBundle(@"笔记/点赞数") target:self action:@selector(praiseBtnClicked)];
    [gesView addSubview:praiseBtn];
    [praiseBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@-10);
        make.centerY.equalTo(@0);
        make.left.equalTo(@0);
    }];
    [praiseBtn setTitleColor:FMNavColor forState:UIControlStateDisabled];
    [praiseBtn setImage:ImageWithName(@"new_praised") forState:UIControlStateDisabled];
    praiseBtn.titleLabel.lineBreakMode = NSLineBreakByClipping;
    self.praiseBtn = praiseBtn;
    
    UILabel *watchListenLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:12] textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:watchListenLabel];
    [watchListenLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(gesView);
        make.right.equalTo(gesView.mas_left).offset(-15);
    }];
    self.watchListenLabel = watchListenLabel;
    
    UIImageView *voiceImgV = [[UIImageView alloc] init];
    [self.contentView addSubview:voiceImgV];
    [voiceImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(infoView.mas_bottom).offset(15);
        make.left.equalTo(infoView);
        make.width.equalTo(@(UI_Relative_WidthValue(195)));
        make.height.equalTo(@(UI_Relative_WidthValue(35)));
        make.bottom.equalTo(@-20);
    }];
    self.voiceImgV = voiceImgV;
    
    UIView *whiteView = [UIView new];
    whiteView.backgroundColor = FMWhiteColor;
    [self.contentView insertSubview:whiteView belowSubview:voiceImgV];
    [whiteView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(voiceImgV.mas_left).offset(15);
        make.width.equalTo(40);
        make.top.bottom.equalTo(voiceImgV);
    }];
    
    UILabel *listionPriceLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:13] textColor:FMWhiteColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [voiceImgV addSubview:listionPriceLabel];
    [listionPriceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@(UI_Relative_WidthValue(40)));
        make.centerY.equalTo(@0);
    }];
    self.listionPriceLabel = listionPriceLabel;
    
    UILabel *durationLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:13] textColor:FMWhiteColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentRight];
    [voiceImgV addSubview:durationLabel];
    [durationLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@-10);
        make.centerY.equalTo(@0);
    }];
    self.durationLabel = durationLabel;
    
    self.bottomLine = [self.contentView addSepLineWithBlock:^(MASConstraintMaker * _Nonnull make) {
        make.left.equalTo(@15);
        make.right.equalTo(@-15);
        make.bottom.equalTo(@0);
        make.height.equalTo(@0.5);
    }];
    self.bottomLine.backgroundColor = UIColor.fm_sepline_color;
}


- (void)setAskModel:(FMAskCodeModel *)askModel {
    _askModel = askModel;
    if (self.askCodeListType != AskCodeListTypeMyAnswered && self.askCodeListType != AskCodeListTypeMyAsked) {
        [self canWatchCell:askModel];
    } else {
        if (askModel.questionAnswerFlag.integerValue == 1) { // 已回答
            if (self.askCodeListType == AskCodeListTypeMyAnswered) {
                if (askModel.checkStatus.integerValue == 1) { // 已通过
                    [self canWatchCell:askModel];
                } else if (askModel.checkStatus.integerValue == 0) { // 待审
                    [self cannotWatchCell:askModel reminder:@"待审核" textColor:UIColor.up_textSecondary1Color borderImage:ImageWithName(@"askcode_grey_border")];
                } else if (askModel.checkStatus.integerValue == -1) { // 驳回
                    [self cannotWatchCell:askModel reminder:@"被驳回" textColor:FMNavColor borderImage:ImageWithName(@"askcode_red_border")];
                }
            } else {
                if (askModel.checkStatus.integerValue == 1) { // 已通过
                    [self canWatchCell:askModel];
                } else {
                    [self cannotWatchCell:askModel reminder:@"未回答" textColor:FMNavColor borderImage:ImageWithName(@"askcode_red_border")];
                }
            }
        } else if (askModel.questionAnswerFlag.integerValue == -1) { // 超时
            [self cannotWatchCell:askModel reminder:@"已超时" textColor:UIColor.up_textSecondary1Color borderImage:ImageWithName(@"askcode_grey_border")];
        } else if (askModel.questionAnswerFlag.integerValue == 0) { // 未回答
            [self cannotWatchCell:askModel reminder:@"未回答" textColor:FMNavColor borderImage:ImageWithName(@"askcode_red_border")];
        }
    }
    
}


- (void)setIsLastCell:(BOOL)isLastCell {
    _isLastCell = isLastCell;
    self.bottomLine.hidden = isLastCell;
}

// 能够看到语音进度条的cell
- (void)canWatchCell:(FMAskCodeModel *)model {
    NSMutableAttributedString *titleAttrStr = [model.questionContent attrStrWithMatchColor:FMSearchOrangeColor pattern:self.searchKeyWord textFont:[FMHelper scaleFont:16]];
    NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
    style.lineSpacing = 5.0f;
    [titleAttrStr addAttributes:@{NSParagraphStyleAttributeName : style} range:NSMakeRange(0, titleAttrStr.length)];
    self.titleLabel.attributedText = titleAttrStr;
    
    [self.infoView.iconImgV.iconImg sd_setImageWithURL:[NSURL URLWithString:model.bignameDto.userIco] placeholderImage:ImageWithName(@"userCenter_dltx")];
    self.infoView.iconImgV.userInteractionEnabled = YES;
    self.infoView.containerView.userInteractionEnabled = YES;
    self.infoView.nameLabel.text = model.bignameDto.userName;
    self.infoView.userId = model.bignameDto.userId;
    self.infoView.attestationType = model.bignameDto.attestationType;
    self.infoView.islive = model.bignameDto.isLive;
    
//    long timeTemp = (model.questionAnswerFlag.integerValue == 1 && model.checkStatus.integerValue == 1) ? model.answerTime : model.questionTime;
    NSDate *nowDate = [NSDate dateWithTimeIntervalSince1970:model.answerTime / 1000];
    NSString *timeStr = nil;
    if ([nowDate isToday]) {
        timeStr = [NSString stringFromDate:nowDate format:@"HH:mm 回答"];
    } else {
        if ([nowDate isThisYear]) {
            timeStr = [NSString stringFromDate:nowDate format:@"MM-dd HH:mm 回答"];
        } else {
            timeStr = [NSString stringFromDate:nowDate format:@"yyyy-MM-dd 回答"];
        }
    }
    self.infoView.timeLabel.text = timeStr;
    
    if (model.virtualListenerNums.integerValue > 0) {
        NSString *readNumStr = nil;
        if (model.virtualListenerNums.integerValue < 10000) {
            readNumStr = [NSString stringWithFormat:@"%@人听过",model.virtualListenerNums];
        } else {
            double wan = model.virtualListenerNums.integerValue / 10000.0;
            readNumStr = [NSString stringWithFormat:@"%.1f万人听过", wan];
        }
        self.watchListenLabel.text = readNumStr;
    } else {
        self.watchListenLabel.text = @"";
    }
    
    if (model.virtualSatisfiedNums.integerValue > 0) {
        NSString *praiseNumStr = nil;
        if (model.virtualSatisfiedNums.integerValue < 10000) {
            praiseNumStr = [NSString stringWithFormat:@"%@", model.virtualSatisfiedNums];
        } else {
            double wan = model.virtualSatisfiedNums.integerValue / 10000.0;
            praiseNumStr = [NSString stringWithFormat:@"%.1f万", wan];
        }
        [self.praiseBtn setTitle:praiseNumStr forState:UIControlStateNormal];
    } else {
        [self.praiseBtn setTitle:@"点赞" forState:UIControlStateNormal];
    }
    [self.praiseBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageLeft imageTitleSpacing:5];
    self.praiseBtn.enabled = ![[FMUserDataSyncManager sharedManager] isQuestionLiked:model.askCodeId];

    self.listionPriceLabel.text = @"";
    if ([model.listenPriceStr floatValue] == 0) {
        if (model.questionPerm.type.integerValue == 5) {
            self.voiceImgV.image = [UIImage imageWithTintColor:ColorWithHex(0x23BF91) blendMode:kCGBlendModeDestinationIn WithImageObject:ImageWithName(@"askcode_list_blueBg")];
            self.listionPriceLabel.text = @"已回答";
        } else {
            self.voiceImgV.image = ImageWithName(@"askcode_list_blueBg");
        }
    } else {
        if (model.questionPerm.type.integerValue == 1) { // 没有权限
            self.voiceImgV.image = ImageWithName(@"askcode_list_blueBg");
            self.listionPriceLabel.text = [NSString stringWithFormat:@"%.0f金币付费偷听",  [model.listenPriceStr floatValue]];
        } else if (model.questionPerm.type.integerValue == 2) { // 免费看
            self.voiceImgV.image = ImageWithName(@"askcode_list_blueBg");
        } else if (model.questionPerm.type.integerValue == 3) { // 已看过
            self.voiceImgV.image = [UIImage imageWithTintColor:ColorWithHex(0xF85943) blendMode:kCGBlendModeDestinationIn WithImageObject:ImageWithName(@"askcode_list_blueBg")];
            self.listionPriceLabel.text = @"已付费";
        }  else if (model.questionPerm.type.integerValue == 4) { // 我问的
            self.voiceImgV.image = [UIImage imageWithTintColor:ColorWithHex(0xF85943) blendMode:kCGBlendModeDestinationIn WithImageObject:ImageWithName(@"askcode_list_blueBg")];
            self.listionPriceLabel.text = @"已付费";
        } else if (model.questionPerm.type.integerValue == 5) { // 已回答
            self.voiceImgV.image = [UIImage imageWithTintColor:ColorWithHex(0x23BF91) blendMode:kCGBlendModeDestinationIn WithImageObject:ImageWithName(@"askcode_list_blueBg")];
            self.listionPriceLabel.text = @"已回答";
        }
    }
    
    self.durationLabel.text = [NSString stringWithFormat:@"%@\"", model.questionAnswerLength];
    
    self.watchListenLabel.hidden = NO;
    self.praiseBtn.hidden = NO;
    self.voiceImgV.hidden = NO;
    [self.voiceImgV mas_updateConstraints:^(MASConstraintMaker *make) {;
        make.height.equalTo(@(UI_Relative_WidthValue(35)));
        make.bottom.equalTo(@-20);
    }];
}

// 不能看到语音进度条的cell
- (void)cannotWatchCell:(FMAskCodeModel *)model reminder:(NSString *)reminder textColor:(UIColor *)textColor borderImage:(UIImage *)borderImage {
    NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:model.questionContent.length ? [model.questionContent stringByAppendingString:@" "] : @""];
    
    NSDictionary *dic = @{NSFontAttributeName:[FMHelper scaleFont:12], NSForegroundColorAttributeName:textColor};
    CGSize signSize = [reminder sizeWithAttr:dic andMaxSize:CGSizeMake(CGFLOAT_MAX, CGFLOAT_MAX)];
    UIImage *textImage = [borderImage addTextWithImageSize:CGSizeMake(signSize.width + 8, signSize.height + 4) text:reminder textRect:CGRectMake(4, 2, signSize.width, signSize.height) textAttributes:dic];
    NSTextAttachment *attachment = [[NSTextAttachment alloc] init];
    attachment.image = textImage;
    attachment.bounds = CGRectMake(0, -4, signSize.width + 8, signSize.height + 4);
    NSAttributedString *subStr = [NSAttributedString attributedStringWithAttachment:attachment];
    [attrStr insertAttributedString:subStr atIndex:attrStr.string.length];
    
    NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
    style.lineSpacing = 5.0f;
    [attrStr addAttributes:@{NSParagraphStyleAttributeName : style} range:NSMakeRange(0, attrStr.length)];
    
    self.titleLabel.attributedText = attrStr;
    [self.infoView.iconImgV.iconImg sd_setImageWithURL:[NSURL URLWithString:model.questionIco] placeholderImage:ImageWithName(@"userCenter_dltx")];
    self.infoView.iconImgV.userInteractionEnabled = NO;
    self.infoView.containerView.userInteractionEnabled = NO;
    self.infoView.nameLabel.text = model.questionUserName;
    self.infoView.userId = model.questionUserid;
    self.infoView.islive = @[];
    self.infoView.attestationType = 0;    
    
//    long timeTemp = (model.questionAnswerFlag.integerValue == 1 && model.checkStatus.integerValue == 1) ? model.answerTime : model.questionTime;
    NSDate *nowDate = [NSDate dateWithTimeIntervalSince1970:model.questionTime / 1000];
    NSString *timeStr = nil;
    if ([nowDate isToday]) {
        timeStr = [NSString stringFromDate:nowDate format:@"HH:mm"];
    } else {
        if ([nowDate isThisYear]) {
            timeStr = [NSString stringFromDate:nowDate format:@"MM-dd HH:mm"];
        } else {
            timeStr = [NSString stringFromDate:nowDate format:@"yyyy-MM-dd"];
        }
    }
    if (self.askCodeListType == AskCodeListTypeMyAnswered) {
        self.infoView.timeLabel.text = [NSString stringWithFormat:@"%@ 提问", timeStr];
    } else {
        self.infoView.timeLabel.text = [NSString stringWithFormat:@"%@ 向%@提问", timeStr, model.bignameDto.userName];
    }
    
    self.watchListenLabel.hidden = YES;
    self.praiseBtn.hidden = YES;
    self.voiceImgV.hidden = YES;
    [self.voiceImgV mas_updateConstraints:^(MASConstraintMaker *make) {;
        make.height.equalTo(@0);
        make.bottom.equalTo(@0);
    }];
}

- (void)praiseBtnClicked {
    if ([FMHelper checkLoginStatus]) {
        [HttpRequestTool questionPraiseWithQuestionId:self.askModel.askCodeId start:^{
            self.praiseBtn.userInteractionEnabled = NO;
        } failure:^{
            self.praiseBtn.userInteractionEnabled = YES;
            [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        } success:^(NSDictionary *dic) {
            self.praiseBtn.userInteractionEnabled = YES;
            
            if ([dic[@"status"] isEqualToString:@"1"]) {
                [[FMUserDataSyncManager sharedManager] likeQuestion:self.askModel.askCodeId];
                self.askModel.virtualSatisfiedNums = [NSString stringWithFormat:@"%ld",self.askModel.virtualSatisfiedNums.integerValue + 1];
                
                NSString *praiseNumStr = nil;
                if (self.askModel.virtualSatisfiedNums.integerValue < 10000) {
                    praiseNumStr = [NSString stringWithFormat:@"%@", self.askModel.virtualSatisfiedNums];
                } else {
                    double wan = self.askModel.virtualSatisfiedNums.integerValue / 10000.0;
                    praiseNumStr = [NSString stringWithFormat:@"%.1f万", wan];
                }
                [self.praiseBtn setTitle:praiseNumStr forState:UIControlStateNormal];
                [self.praiseBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageLeft imageTitleSpacing:5.0f];
                
                self.praiseBtn.enabled = NO;
            } else {
                [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            }
        }];
    }
}

@end
