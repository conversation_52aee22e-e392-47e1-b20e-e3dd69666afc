//
//  FMAskCodeRecommendCell.m
//  QCYZT
//
//  Created by zeng on 2021/11/15.
//  Copyright © 2021 LZKJ. All rights reserved.
//

#import "FMAskCodeRecommendCell.h"
#import "FMAskCodeModel.h"
@interface FMAskCodeRecommendCell()

@property (nonatomic, strong) UIImageView *leftImgV;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIImageView *iconImgV;
@property (nonatomic, strong) UILabel *hdLabel;
@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) UILabel *watchNumLabel;
@property (nonatomic, strong) UIView *bottomLine;
@property (nonatomic, strong) UIButton *praiseBtn;

@end

@implementation FMAskCodeRecommendCell

- (id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setUp];
    }
    return  self;
}

- (void)setUp {
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    self.layoutMargins = UIEdgeInsetsMake(0, 15, 0, 15);
    self.separatorInset = UIEdgeInsetsMake(0, 15, 0, 15);
    self.contentView.backgroundColor = UIColor.up_contentBgColor;
    
    UIImageView *leftImgV = [[UIImageView alloc] init];
    [self.contentView addSubview:leftImgV];
    [leftImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.top.equalTo(@15);
        make.width.equalTo(@20);
        make.height.equalTo(@20);
    }];
    leftImgV.contentMode = UIViewContentModeScaleAspectFill;
    self.leftImgV = leftImgV;
    
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:16.0] textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:2 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:titleLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(leftImgV.mas_right).offset(8);
        make.right.equalTo(@-15);
        make.top.equalTo(leftImgV).offset(0);
    }];
    self.titleLabel = titleLabel;
    
    UILabel *nameLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:12.0] textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:nameLabel];
    [nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(titleLabel.mas_bottom).offset(15);
        make.height.equalTo(@(17));
        make.left.equalTo(titleLabel.mas_left);
        make.bottom.equalTo(self.contentView.mas_bottom).offset(-15);
    }];
    self.nameLabel = nameLabel;
    
    UILabel *hdLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:12.0] textColor:UIColor.up_textSecondary1Color backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:hdLabel];
    [hdLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(nameLabel.mas_right).offset(7);
        make.centerY.equalTo(nameLabel);
    }];
    hdLabel.text = @"回答";
    self.hdLabel = hdLabel;
    
    UIView *gesView = [[UIView alloc] init];
    [self.contentView addSubview:gesView];
    [gesView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@-15);
        make.centerY.equalTo(nameLabel);
        make.height.equalTo(@20);
    }];
    gesView.userInteractionEnabled = YES;
    [gesView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithActionBlock:^(id  _Nonnull sender) {
        // 屏蔽点击跳转到详情
    }]];
    
    UIButton *praiseBtn = [[UIButton alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:12.0] normalTextColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor title:@"点赞" image:FMImgInBundle(@"笔记/点赞数") target:self action:@selector(praiseBtnClicked)];
    [praiseBtn setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [gesView addSubview:praiseBtn];
    [praiseBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@-10);
        make.centerY.equalTo(@0);
        make.left.equalTo(@0);
    }];
    [praiseBtn setTitleColor:UIColor.up_textSecondaryColor forState:UIControlStateNormal];
    [praiseBtn setTitleColor:FMNavColor forState:UIControlStateDisabled];
    [praiseBtn setImage:FMImgInBundle(@"笔记/点赞数") forState:UIControlStateNormal];
    [praiseBtn setImage:ImageWithName(@"new_praised") forState:UIControlStateDisabled];
    self.praiseBtn = praiseBtn;
    
    UILabel *watchNumLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:12.0] textColor:UIColor.up_textSecondary1Color backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentRight];
    [self.contentView addSubview:watchNumLabel];
    [watchNumLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(gesView);
        make.right.equalTo(praiseBtn.mas_left).offset(-15);
    }];
    self.watchNumLabel = watchNumLabel;
    
    self.bottomLine = [self.contentView addSepLineWithBlock:^(MASConstraintMaker * _Nonnull make) {
        make.left.equalTo(@15);
        make.right.equalTo(@-15);
        make.bottom.equalTo(@0);
        make.height.equalTo(@0.5);
    }];
    self.bottomLine.backgroundColor = UIColor.fm_sepline_color;
}

- (void)praiseBtnClicked {
    if ([FMHelper checkLoginStatus]) {
        [HttpRequestTool questionPraiseWithQuestionId:[NSString stringWithFormat:@"%@",self.askModel.askCodeId] start:^{
            self.praiseBtn.userInteractionEnabled = NO;
        } failure:^{
            self.praiseBtn.userInteractionEnabled = YES;
            [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        } success:^(NSDictionary *dic) {
            self.praiseBtn.userInteractionEnabled = YES;

            if ([dic[@"status"] isEqualToString:@"1"]) {
                [[FMUserDataSyncManager sharedManager] likeQuestion:self.askModel.askCodeId];
                self.askModel.virtualSatisfiedNums = [NSString stringWithFormat:@"%ld",self.askModel.virtualSatisfiedNums.integerValue + 1];
                
                NSString *praiseNumStr = nil;
                if (self.askModel.virtualSatisfiedNums.integerValue < 10000) {
                    praiseNumStr = [NSString stringWithFormat:@"%@", self.askModel.virtualSatisfiedNums];
                } else {
                    double wan = self.askModel.virtualSatisfiedNums.integerValue / 10000.0;
                    praiseNumStr = [NSString stringWithFormat:@"%.1f万", wan];
                }
                [self.praiseBtn setTitle:praiseNumStr forState:UIControlStateNormal];
                [self.praiseBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageLeft imageTitleSpacing:5.0f];
                
                self.praiseBtn.enabled = NO;
            } else {
                [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            }
        }];
    }
}

- (void)setAskModel:(FMAskCodeModel *)askModel {
    _askModel = askModel;
    if (askModel.contentFileType.integerValue == 1) {
        self.leftImgV.image = ImageWithName(@"askcode_recommend_video");
    } else {
        self.leftImgV.image = ImageWithName(@"askcode_recommend_audio");
    }
    
    NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:askModel.questionContent];
    NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
    style.lineSpacing = 2.0f;
    [attrStr addAttributes:@{NSParagraphStyleAttributeName : style} range:NSMakeRange(0, attrStr.length)];
    self.titleLabel.attributedText = attrStr;
    
    [self.iconImgV sd_setImageWithURL:[NSURL URLWithString:askModel.bignameDto.userIco] placeholderImage:ImageWithName(@"userCenter_dltx")];
    self.nameLabel.text = [NSString stringWithFormat:@"%@",askModel.bignameDto.userName];
    
//    long timeTemp = (askModel.questionAnswerFlag.integerValue == 1 && askModel.checkStatus.integerValue == 1) ? askModel.answerTime : askModel.questionTime;
    NSDate *answerDate = [NSDate dateWithTimeIntervalSince1970:askModel.answerTime / 1000];
    NSString *answerTime = nil;
    if ([answerDate isToday]) {
        answerTime = [NSString stringFromDate:answerDate format:@"HH:mm 回答"];
    } else {
        if ([answerDate isThisYear]) {
            answerTime = [NSString stringFromDate:answerDate format:@"MM-dd HH:mm 回答"];
        } else {
            answerTime = [NSString stringFromDate:answerDate format:@"yyyy-MM-dd 回答"];
        }
    }
    self.hdLabel.text = answerTime;
    
    if ([FMHelper isBigFont]) {
        // 大字模式
        [self.hdLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.nameLabel.mas_bottom);
            make.left.equalTo(self.nameLabel);
        }];
        [self.nameLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.contentView.mas_bottom).offset(-15 - 22);
        }];
    } else {
        // 普通模式
        [self.hdLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.nameLabel.mas_right).offset(7);
            make.centerY.equalTo(self.nameLabel);
        }];
        [self.nameLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.contentView.mas_bottom).offset(-15);
        }];
    }
    
    NSString *listenNumStr = nil;
    if (askModel.virtualListenerNums.integerValue < 10000) {
        listenNumStr = [NSString stringWithFormat:@"%@人", askModel.virtualListenerNums];
    } else {
        double wan = askModel.virtualListenerNums.integerValue / 10000.0;
        listenNumStr = [NSString stringWithFormat:@"%.1f万人", wan];
    }
    if (askModel.contentFileType.integerValue == 1) {
        self.watchNumLabel.text = [NSString stringWithFormat:@"%@看过",listenNumStr];
    } else {
        self.watchNumLabel.text = [NSString stringWithFormat:@"%@听过",listenNumStr];
    }
    self.watchNumLabel.hidden = !(askModel.virtualListenerNums.integerValue > 0);
    
    if (askModel.virtualSatisfiedNums.integerValue > 0) {
        NSString *praiseNumStr = nil;
        if (askModel.virtualSatisfiedNums.integerValue < 10000) {
            praiseNumStr = [NSString stringWithFormat:@"%@", askModel.virtualSatisfiedNums];
        } else {
            double wan = askModel.virtualSatisfiedNums.integerValue / 10000.0;
            praiseNumStr = [NSString stringWithFormat:@"%.1f万", wan];
        }
        [self.praiseBtn setTitle:praiseNumStr forState:UIControlStateNormal];
    } else {
        [self.praiseBtn setTitle:@"点赞" forState:UIControlStateNormal];
    }
    [self.praiseBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageLeft imageTitleSpacing:5];
    self.praiseBtn.enabled = ![[FMUserDataSyncManager sharedManager] isQuestionLiked:askModel.askCodeId];
;
}

- (void)setIsLastCell:(BOOL)isLastCell {
    _isLastCell = isLastCell;
    
    self.bottomLine.hidden = isLastCell;
}

@end
