
//
//  FMAskCodeDetailViewController.m
//  QCYZT
//
//  Created by th on 16/12/27.
//  Copyright © 2016年 sdcf. All rights reserved.
//

#import "FMAskCodeDetailViewController.h"
#import "FMAskCodeDetailAuditStatusCell.h"
#import "FMAskCodeDetailHeaderCell.h"
#import "FMAskCodeDetailHeaderPlayCell.h"
#import "FMAskCodeDetailBottomCell.h"
#import "FMAskCodeModel.h"
#import "FMCommentView.h"
#import "BigCastDetailModel.h"
#import "FMShareHelper.h"
#import "FMPayTool.h"
#import "FMDetailBottomView.h"
#import "FMAskCodeDetailVideoTabCell.h"
#import "EnablePayModel.h"
#import "PaymentView.h"
#import "FMProgressHUD.h"
#import "HttpRequestTool+Pay.h"
#import "HttpRequestTool+Daka.h"
#import "FMAskCodePlayerControlView.h"
#import "FMPlayerManager.h"
#import "FMPaySuccessPopView.h"
//视频播放
#import "ZFPlayerController.h"
#import "ZFAVPlayerManager.h"
#import "ZFPlayerControlView.h"
#import "FMRatingView.h"
#import "HttpRequestTool+AskCode.h"

@interface FMAskCodeDetailViewController() <UITableViewDelegate, UITableViewDataSource, UIAlertViewDelegate, FMDetailBottomViewDelegate,FMAsCodePlayerControlDelegate>

@property (nonatomic,strong) UITableView *tableView;
@property (nonatomic, strong) FMDetailBottomView *bottomView;
/// 提问输入UI
@property (nonatomic, strong) FMCommentView *commentView;
/// 问股详情
@property (nonatomic,strong) FMAskCodeModel *detailModel;
@property (nonatomic, strong) BigCastDetailModel *bigCastModel;
/// 投顾问股价格
@property (nonatomic,copy) NSString *askPrice;
/// 提问内容
@property (nonatomic, copy) NSString *questionContent;
@property (nonatomic, strong) FMSearchStockModel *choosedStockModel;

@property (nonatomic, assign) BOOL needsRefresh;
//播放相关
@property (nonatomic, strong) FMAskCodePlayerControlView *controlView;
///登录状态改变时,试看结束或者重新试看时 当前试看播放状态记录字段  2:试看结束
@property (nonatomic,assign) NSInteger currentPalyStatus;
@property (nonatomic, strong) ZFPlayerController *player;
@property (nonatomic, strong) ZFAVPlayerManager *playerManager;
@property (nonatomic, strong) UIView *footerView;
@property (nonatomic, strong) FMRatingView *rateView;

@end

@implementation FMAskCodeDetailViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = @"问股详情";
    self.view.backgroundColor = UIColor.up_contentBgColor;
    
    WEAKSELF;
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.equalTo(__weakSelf.view);
        make.bottom.equalTo(@(-60-UI_SAFEAREA_BOTTOM_HEIGHT));
    }];
    self.tableView.hidden = YES;
    [self requestNoteDetail];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(reloadDataPage) name:kQuestionPaySuccess object:nil];
    [FMHelper addLoginAndLogoutNotificationWithObserver:self selector:@selector(handleLoginStatusNotification) monitorAuthLogin:NO];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleLoginStatusNotification) name:kRechargeSuccess object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleLoginStatusNotification) name:kSubmitAnswerSuccess object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(notificationViewClick:) name:kNotificationViewClickNotification object:nil];

}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    self.player.viewControllerDisappear = NO;
    if (self.needsRefresh) {
        self.needsRefresh = NO;
        [self requestNoteDetail];
    } else {
        [self.tableView reloadData];
    }
    
    [self configNavRedColor];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    if (![FMHelper getCurrentVC].presentingViewController) { //不是推出控制器
        self.player.viewControllerDisappear = YES;
    }
}

- (void)dealloc {
    FMLog(@"%s", __func__);
    self.player = nil;
    if([FMPlayerManager shareManager].isSmallViewPlay) {
        [FMPlayerManager play];
    }
    [[NSNotificationCenter defaultCenter]  removeObserver:self];
}

#pragma mark- 控制横竖屏幕
//  是否支持自动转屏
- (BOOL)shouldAutorotate
{
    return NO;
}

// 支持哪些转屏方向
- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    if (self.player.isFullScreen) {
        return UIInterfaceOrientationMaskLandscape;
    }
    return UIInterfaceOrientationMaskPortrait;
}


//收到通知 点击通知栏协议跳转  如果是横屏 需要回到竖屏 并做相应跳转
- (void)notificationViewClick:(NSNotification *)noti {
    self.player.viewControllerDisappear = YES;
    if (self.player.isFullScreen) {
        [self.player enterFullScreen:NO animated:YES];

    }
}

//配置播放器
- (void)configPlayer {
    WEAKSELF;
    if (!self.player) {
        FMAskCodeDetailVideoTabCell *cell = [self.tableView cellForRowAtIndexPath:[NSIndexPath indexPathForRow:2 inSection:0]];
        self.playerManager = [[ZFAVPlayerManager alloc] init];
        self.player = [[ZFPlayerController alloc] initWithPlayerManager:self.playerManager containerView:cell.liveBgImageV];
        self.player.controlView = self.controlView;
        self.player.shouldAutoPlay = NO;
        self.player.allowOrentitaionRotation = NO;
        self.player.playerDidToEnd = ^(id  _Nonnull asset) {
            STRONGSELF;
            [strongSelf.player seekToTime:0 completionHandler:nil];
            [strongSelf.player.currentPlayerManager pause];
        };
    }
    self.controlView.model = self.detailModel;
    self.player.playerPlayStateChanged = ^(id<ZFPlayerMediaPlayback>  _Nonnull asset, ZFPlayerPlaybackState playState) {
        STRONGSELF
        if(strongSelf.player.isFullScreen) {
            // 全屏状态下 小窗口停止播放
            [FMPlayerManager pause];
        } else {
            if (playState == ZFPlayerPlayStatePlaying) {
                [FMPlayerManager pause];
            } else {
                [FMPlayerManager play];
            }
        }
    };
    [FMPlayerManager shareManager].player.playerPlayStateChanged = ^(id<ZFPlayerMediaPlayback>  _Nonnull asset, ZFPlayerPlaybackState playState) {
        if (playState == ZFPlayerPlayStatePlaying) {
            [__weakSelf.player.currentPlayerManager pause];
        } else if (playState == ZFPlayerPlayStatePaused) {
            [__weakSelf.player.currentPlayerManager play];
        }
    };

    // 问股自动播放
    [self playBtnClick];

}

//重新试看视频
- (void)videoReWatch:(UIButton *)sender {
    self.currentPalyStatus = 1;
    self.detailModel.playStatus = self.currentPalyStatus;
    self.controlView.model = self.detailModel;
    [self.player.currentPlayerManager replay];
}

- (void)showVideoPlayerInView {

    self.controlView.prepareShowControlView = YES;
    [self.player playTheIndexPath:nil assetURL:[NSURL URLWithString:self.detailModel.answerVideoUrl]];
    [self.controlView showTitle:@""
                 coverURLString:self.detailModel.questionImg
                 fullScreenMode:ZFFullScreenModeLandscape];
    self.controlView.model = self.detailModel;
    
    // 配置画中画
    [[FMAppDelegate shareApp] configPiP:[FMPlayerManager shareManager].playerManager.avPlayerLayer];
    WEAKSELF
    self.player.playerPlayTimeChanged = ^(id<ZFPlayerMediaPlayback>  _Nonnull asset, NSTimeInterval currentTime, NSTimeInterval duration) {
        if (__weakSelf.detailModel.questionPerm.type.integerValue == 1) {
            if (__weakSelf.detailModel.tryPlaySecond.integerValue > 0) {
                if (floorf(currentTime) >= __weakSelf.detailModel.tryPlaySecond.floatValue || __weakSelf.currentPalyStatus == 2) {
                    __weakSelf.currentPalyStatus = 2; //试看结束
                    if (__weakSelf.player.isFullScreen) {
                        [__weakSelf.player enterFullScreen:NO animated:NO];
                    }
                    [__weakSelf.controlView resetControlView];
                    [__weakSelf.player.currentPlayerManager pause];
                    __weakSelf.detailModel.playStatus = __weakSelf.currentPalyStatus;
                    __weakSelf.controlView.model = __weakSelf.detailModel;
                }
            }
        }
    };
}


- (void)accessPermissions {
    // 判断有几种支付方式，如果是一种，直接弹框提醒，如果是多种，弹框选择
    WEAKSELF
    if (self.detailModel.enablePayModel.count == 0) {
        // 没有可用的支付方式
        [[FMPayTool payTool] noEnablePayModelWithErrorCode:self.detailModel.noPayModelCode.integerValue errorText:self.detailModel.noPayModelText];
        return;
    }
    FMAskCodeDetailVideoTabCell *cell = [self.tableView cellForRowAtIndexPath:[NSIndexPath indexPathForRow:2 inSection:0]];
    [[FMPayTool payTool] judgeConfirmOrderStatusWithDakaId:self.detailModel.bignameDto.userId certCode:self.detailModel.bignameDto.certCode clickView:cell.liveBgImageV confirmOperation:^{
        EnablePayModel *payModel = [NSArray modelArrayWithClass:[EnablePayModel class] json:self.detailModel.enablePayModel].firstObject;
        payModel.bignameId = self.detailModel.bignameDto.userId;
        payModel.consumeType = 4;
        payModel.contentId = self.detailModel.askCodeId.integerValue;
        payModel.name = @"金币";
        [PaymentView showWithEnablePayModel:payModel payPrice:self.detailModel.listenPriceStr productName:self.detailModel.questionContent bottomReminder:@"注：已付费的产品和内容，不会重复扣费" payAction:^(EnablePayModel *selectedModel) {
            [__weakSelf httpForPayAskCode:[NSString stringWithFormat:@"%zd", selectedModel.type] couponId:selectedModel.couponId usePoints:selectedModel.usePoints];
        } dismissBlock:^{
        }];
    }];
}

- (void)httpForPayAskCode:(NSString *)payType couponId:(NSString *)couponId usePoints:(NSInteger)usePoints {
    WEAKSELF
    [HttpRequestTool payQuestionWithQuestionid:self.detailModel.askCodeId type:payType couponId:couponId usePoints:usePoints start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"] && [dic[@"data"] isKindOfClass:[NSDictionary class]]) {
            [SVProgressHUD dismiss];
            FMPaySuccessPopView *popView = [[FMPaySuccessPopView alloc] init];
            popView.jumpIndex = 2;
            [popView show];
            
            __weakSelf.detailModel.questionPerm.type = @"3";
            
            [HttpRequestTool queryDakaCoinsAndPointsStart:nil failure:nil success:nil];
            
            [__weakSelf requestNoteDetail];

            dispatch_async(dispatch_get_main_queue(), ^{
                __weakSelf.currentPalyStatus = 0;
                __weakSelf.detailModel.playStatus = 0;
                __weakSelf.controlView.model = self.detailModel;
            });
           
            if (__weakSelf.detailModel.askCodeId.length) {
                [[NSNotificationCenter defaultCenter] postNotificationName:kQuestionPaySuccess object:nil userInfo:@{@"questionId":__weakSelf.detailModel.askCodeId}];
            }
        } else {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            });
        }
    }];
}

#pragma mark - detailLiveheaderbgImgageCellDelegate
//播放视频
- (void)playBtnClick {
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(networkChanged) name:kNetworkStatusChangedNotification object:nil];
    
    [self showVideoPlayerInView];
}

/// 支付金币 开通权限
- (void)detailPayViewPayBtnClick:(UIButton *)sender {
    if ([FMHelper checkLoginStatus]) {
        [self accessPermissions];
    }
}

#pragma mark - UIScrollViewDelegate
- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    if (scrollView == self.tableView && [FMPlayerManager shareManager].player != nil) {
        FMAskCodeDetailVideoTabCell *cell = [self.tableView cellForRowAtIndexPath:[NSIndexPath indexPathForRow:2 inSection:0]];
        if (scrollView.contentOffset.y > cell.origin.y) {
            [self.view addSubview:self.playerManager.view];
        } else {
            [cell.liveBgImageV addSubview:self.playerManager.view];
        }
    }
}


#pragma mark - TableViewDelegate DataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (self.detailModel) {
        return 4;
    }
    
    return 0;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.row == 0) {
        FMAskCodeDetailAuditStatusCell *cell = [tableView dequeueReusableCellWithIdentifier:@"FMAskCodeDetailAuditStatusCell"];
        cell.model = self.detailModel;
        return cell;
    } else if (indexPath.row == 1) {
        FMAskCodeDetailHeaderCell *cell = [tableView dequeueReusableCellWithIdentifier:@"FMAskCodeDetailHeaderCell"];
        cell.model = self.detailModel;
        return cell;
    } else if (indexPath.row == 2) {
        if (self.detailModel.contentFileType.integerValue == 1) {
            FMAskCodeDetailVideoTabCell *playCell = [tableView reuseCellClass:[FMAskCodeDetailVideoTabCell class]];
            self.detailModel.playStatus = self.currentPalyStatus;
            playCell.model = self.detailModel;
            WEAKSELF
            playCell.playBrnClick = ^{
                [__weakSelf playBtnClick];
            };
            return playCell;
        } else {
            FMAskCodeDetailHeaderPlayCell *playCell = [tableView dequeueReusableCellWithIdentifier:@"FMAskCodeDetailHeaderPlayCell"];
            playCell.model = self.detailModel;
            return playCell;
        }
    } else if (indexPath.row == 3) {
        FMAskCodeDetailBottomCell *cell = [tableView dequeueReusableCellWithIdentifier:@"FMAskCodeDetailBottomCell"];
        cell.model = self.detailModel;
        return cell;
    }
    
    return [UITableViewCell new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    WEAKSELF;
    if (indexPath.row == 0) {
        if (self.detailModel.questionAnswerFlag.integerValue == 1 && self.detailModel.checkStatus.integerValue == -1) {
            return [tableView fd_heightForCellWithIdentifier:@"FMAskCodeDetailAuditStatusCell" configuration:^(FMAskCodeDetailAuditStatusCell *cell) {
                cell.model = __weakSelf.detailModel;
            }];
        } else {
            return 0;
        }
    } else if (indexPath.row == 1) {
        return [tableView fd_heightForCellWithIdentifier:@"FMAskCodeDetailHeaderCell" configuration:^(FMAskCodeDetailHeaderCell *cell) {
            cell.model = __weakSelf.detailModel;
        }];
    } else if (indexPath.row == 2) {
        if (self.detailModel.contentFileType.integerValue == 1) {
            return UI_SCREEN_WIDTH * (9 / 16.0);
        } else {
            return 45;
        }
    } else if (indexPath.row == 3) {
        CGFloat h = [tableView fd_heightForCellWithIdentifier:@"FMAskCodeDetailBottomCell" configuration:^(FMAskCodeDetailBottomCell *cell) {
            cell.model = __weakSelf.detailModel;
        }];
        return h;
    }
    
    return 0;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}


#pragma mark - HTTP
- (void)requestNoteDetail {
    if (self.detailModel.questionPerm.type.integerValue == 1) { // 在详情页登录后返回，数据没有请求到时，要把支付按钮点击事件屏蔽掉，以防用户重复支付
        FMAskCodeDetailHeaderPlayCell *cell = [self.tableView cellForRowAtIndexPath:[NSIndexPath indexPathForRow:1 inSection:0]];
        cell.userInteractionEnabled = NO;
    }
    
    WEAKSELF;
    [HttpRequestTool getQuestionDetailWithWithQuestionId:self.questionId start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD dismiss];
        [__weakSelf.view showReloadNetworkViewWithBlock:^{
            [__weakSelf requestNoteDetail];
        }];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD dismiss];
            
            __weakSelf.tableView.hidden = NO;
            __weakSelf.detailModel = [FMAskCodeModel modelWithDictionary:dic[@"data"]];
            
            __weakSelf.bottomView.collected = [[FMUserDataSyncManager sharedManager] isQuestionCollected:__weakSelf.questionId];
            
            __weakSelf.askPrice = __weakSelf.detailModel.bignameDto.answerPrice;
            [__weakSelf.view addSubview:__weakSelf.bottomView];
            [__weakSelf.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.bottom.right.equalTo(@0);
                make.height.equalTo(@(DetailBottomViewHeight));
            }];
            __weakSelf.bottomView.delegate = self;
            [__weakSelf.bottomView.commentBtn setTitle:[NSString stringWithFormat:@"%@金币提问", __weakSelf.askPrice] forState:UIControlStateNormal];
            
            [__weakSelf.tableView reloadData];
            if (__weakSelf.detailModel.contentFileType.integerValue == 1) { //视频问股
                [__weakSelf configPlayer];
            } else {
                FMAskCodeDetailHeaderPlayCell *cell = [__weakSelf.tableView cellForRowAtIndexPath:[NSIndexPath indexPathForRow:1 inSection:0]];
                cell.userInteractionEnabled = YES;
            }
             
            // 回答时间在三天内 显示评分
            if ([FMUserDefault getUserFlag] == 0 && ![self.detailModel.questionPerm.type isEqualToString:@"1"]) {
                if ([dic[@"systemTime"] integerValue] - self.detailModel.answerTime <= 3 * 24 * 60 * 60 * 1000) {
                    self.tableView.tableFooterView = self.footerView;
                }
            }
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            [__weakSelf.view showReloadNetworkViewWithBlock:^{
                [__weakSelf requestNoteDetail];
            }];
        }
    }];
}

// 问股意见反馈
- (void)subminAskCodeSubmit:(NSString *)content {
    if(content.length == 0) {
        [HttpRequestTool cancelHttpRequestWithUrlString:kAPI_Question_FeedBack];
    }
    [HttpRequestTool askCodeFeedBackWithQuestionId:self.detailModel.askCodeId.integerValue StarLevel:self.rateView.rate feedback:content start:^{
        if (content.length > 0) {
            [SVProgressHUD show];
        }
    } failure:^{
        if (content.length > 0) {
            [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        }
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            if (content.length > 0) {
                [SVProgressHUD showSuccessWithStatus:@"提交成功!"];
                dispatch_async(dispatch_get_main_queue(), ^{
                    [self.rateView.feedbackView dismiss];
                });
            }
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

#pragma mark - UIAlertView Delegate
- (void)alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex {
    if (buttonIndex == 1) {
        WEAKSELF;
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [HttpRequestTool questionAskWithAnswerId:__weakSelf.detailModel.bignameDto.userId content:__weakSelf.questionContent type:__weakSelf.commentView.type goodsId:__weakSelf.commentView.couponId  stockCode:__weakSelf.choosedStockModel.oldStockCode stockName:__weakSelf.choosedStockModel.name start:^{
                [SVProgressHUD show];
            } failure:^{
                [SVProgressHUD showErrorWithStatus:@"网络不给力"];
            } success:^(NSDictionary *dic) {
                if ([dic[@"status"] isEqualToString:@"1"]) {
                    [SVProgressHUD showSuccessWithStatus:@"您的提问已成功发送，请注意接收回复通知"];
                    __weakSelf.commentView.textView.text = @"";
                    __weakSelf.commentView = nil;
                    [[NSNotificationCenter defaultCenter] postNotificationName:kAskCodeSuccess object:nil];
                } else {
                    [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
                }
            }];
        });
    } else {
        self.questionContent = nil;
        self.choosedStockModel = nil;
    }
}

#pragma mark - FMDetailBottomView Delegate
- (void)detailBottomViewCommentBtnDidClicked:(UIView *)bottomView btn:(UIButton *)commentBtn {
    bottomView.userInteractionEnabled = NO;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        bottomView.userInteractionEnabled = YES;
    });
    
    if ([FMHelper checkLoginStatus]) {
        if ([self.detailModel.bignameDto.userId isEqualToString:[FMUserDefault getUserId]]) {
            [SVProgressHUD showInfoWithStatus:@"自己不能向自己提问"];
            return ;
        }
        
        if (self.bigCastModel.windowType == 2) {
            [FMHelper showVIPAlertWithType:VIPReadTypeAskCode needVip:self.bigCastModel.needVip authority:@"0" price:[self.bigCastModel.answerPrice floatValue] showAlert:YES clickSure:nil clickCancel:nil clickSureDismiss:YES];
        } else {
            [self gotoPay];
        }
    }
}

- (void)detailBottomViewCollectBtnDidClicked:(UIView *)bottomView btn:(UIButton *)collectBtn {
    if ([FMHelper checkLoginStatus]) {
        WEAKSELF;
        if ([[FMUserDataSyncManager sharedManager] isQuestionCollected:self.detailModel.askCodeId]) {
            [FMCommonHttp unCollectWithButton:collectBtn contentId:self.questionId type:@"1" unCollectSuccess:^{
                collectBtn.selected = NO;
                [[FMUserDataSyncManager sharedManager] uncollectQuestion:__weakSelf.questionId];
            }];
        } else {
            [FMCommonHttp collectBtnWithButton:collectBtn contentId:self.questionId type:@"1" collectSuccess:^{
                collectBtn.selected = YES;
                [[FMUserDataSyncManager sharedManager] collectQuestion:__weakSelf.questionId];
            }];
        }
    }
}

- (void)detailBottomViewShareBtnDidClicked:(UIView *)bottomView btn:(UIButton *)shareBtn {
    [FMShareHelper shareAskCodeDetailWithModel:self.detailModel];
}

#pragma mark - NSNotification
- (void)handleLoginStatusNotification {
    self.needsRefresh = YES;
}

- (void)networkChanged {
    if ([FMNetworkStatusMonitor sharedMonitor].currentStatus == NetworkStatusCellular) {
        if ([[FMHelper getCurrentVC] isEqual:self]) {
            [FMProgressHUD showTextOnlyInView:self.controlView withText:@"当前处于非wifi环境,请注意流量消耗"];
        }
    }
}

- (void)reloadDataPage {
    // 回答时间在三天内 显示评分
    if ([FMUserDefault getUserFlag] == 0 && [self.detailModel.questionPerm.type isEqualToString:@"2"]) {
        if ([CountDownShareInstance shareInstance].serverTime - self.detailModel.answerTime <= 3 * 24 * 60 * 60 * 1000) {
            self.tableView.tableFooterView = self.footerView;
        }
    }
    [self.tableView reloadData];
}

#pragma mark - Private
- (void)back {
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)gotoPay {
    WEAKSELF;
    // 判断余额
    // 判断确认书
    [[FMPayTool payTool] judgeConfirmOrderStatusWithDakaId:__weakSelf.detailModel.bignameDto.userId certCode:__weakSelf.detailModel.bignameDto.certCode clickView:__weakSelf.bottomView confirmOperation:^{
        UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
        __weakSelf.commentView.frame = keyWindow.bounds;
        [keyWindow addSubview:__weakSelf.commentView];
        __weakSelf.commentView.publisLb.textColor = UIColor.up_textPrimaryColor;
        __weakSelf.commentView.publisLb.text = [NSString stringWithFormat:@"向%@老师提问", __weakSelf.detailModel.bignameDto.userName];
        __weakSelf.commentView.placeholderText = @"输入您的问题。若已买入该股票，可告知“买入价格”和“仓位情况”，更便于老师根据您的实际情况给予建议。\n若超过预期时间未解答，提问费用或卡券将会自动退回。";
        __weakSelf.commentView.askPrice = __weakSelf.detailModel.bignameDto.answerPrice;
        __weakSelf.commentView.bignameId = __weakSelf.detailModel.bignameDto.userId;
        __weakSelf.commentView.consumeType = 2;
        [__weakSelf.commentView requestCouponList];
        __weakSelf.commentView.publishAskCodeBlock = ^(NSString *content, FMSearchStockModel *stockModel) {
            __weakSelf.questionContent = content;
            __weakSelf.choosedStockModel = stockModel;
            
            if (__weakSelf.commentView.couponId.length == 0) {
                // 没有选择优惠券 先校验余额
                [[FMPayTool payTool] compareCoinRemainderWithConsume:__weakSelf.detailModel.bignameDto.answerPrice clickView:nil completeBlock:^(NSString *coinRemainder) {
                    // 查询问股回答所需时长
                    [__weakSelf checkWaitTime];
                }];
            } else {
                // 查询问股回答所需时长
                [__weakSelf checkWaitTime];
            }
        };
    }];
}

/// 查询问股回答所需时长
- (void)checkWaitTime {
    [HttpRequestTool questionCheckWaitTimeWithAnswerId:self.detailModel.bignameDto.userId start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD dismiss];
            UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:nil
                                                                message:dic[@"data"]
                                                               delegate:self
                                                      cancelButtonTitle:@"取消"
                                                      otherButtonTitles:@"确定", nil];
            [alertView show];
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

#pragma mark - Getter/Setter
- (UITableView *)tableView {
    if (!_tableView) {
        UITableView *tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];
        _tableView = tableView;
        [_tableView registerClass:[FMAskCodeDetailBottomCell class] forCellReuseIdentifier:@"FMAskCodeDetailBottomCell"];
        [_tableView registerClass:[FMAskCodeDetailHeaderCell class] forCellReuseIdentifier:@"FMAskCodeDetailHeaderCell"];
        [_tableView registerClass:[FMAskCodeDetailHeaderPlayCell class] forCellReuseIdentifier:@"FMAskCodeDetailHeaderPlayCell"];
        [_tableView registerClass:[FMAskCodeDetailAuditStatusCell class] forCellReuseIdentifier:@"FMAskCodeDetailAuditStatusCell"];
        [_tableView registerCellClass:[FMAskCodeDetailVideoTabCell class]];
        _tableView.backgroundColor = UIColor.up_contentBgColor;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.bounces = NO;
        _tableView.delegate = self;
        _tableView.dataSource = self;
    }
    
    return _tableView;
}

- (FMDetailBottomView *)bottomView {
    if (!_bottomView) {
        _bottomView = [[FMDetailBottomView alloc] init];
        if (self.detailModel.isPublic.boolValue) {
            _bottomView.showType = FMDetailBottomShowTypeShare | FMDetailBottomShowTypeCollect;
        } else {
            _bottomView.showType = FMDetailBottomShowTypeCollect;
        }
        [_bottomView.commentBtn setTitle:@"金币提问" forState:UIControlStateNormal];
    }
    return _bottomView;
}

- (FMCommentView *)commentView {
    if (!_commentView) {
        _commentView = [[FMCommentView alloc] initWithCouponFrame:CGRectZero];
    }
    return _commentView;
}

- (FMAskCodePlayerControlView *)controlView {
    if (!_controlView) {
        _controlView = [[FMAskCodePlayerControlView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_WIDTH*9/16)];
        _controlView.activity.center = _controlView.center;
        _controlView.prepareShowLoading = YES;
        _controlView.prepareShowControlView = YES;
        _controlView.delegate = self;
    }
    return _controlView;
}

- (UIView *)footerView {
    if (!_footerView) {
        UIView *view = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 157)];
        FMRatingView *rateView = [[FMRatingView alloc] initWithFrame:CGRectMake(15, 2, UI_SCREEN_WIDTH - 30, 155)];
        rateView.rate = self.detailModel.starLevel;
        rateView.feedback = self.detailModel.feedback;
        WEAKSELF
        rateView.submitFeedBack = ^(NSString * _Nonnull content) {
            [__weakSelf subminAskCodeSubmit:content];
        };
        rateView.selectedRate = ^{
            [__weakSelf subminAskCodeSubmit:@""];
        };
        [view addSubview:rateView];
        self.rateView = rateView;
        _footerView = view;
    }
    return _footerView;
}

@end


