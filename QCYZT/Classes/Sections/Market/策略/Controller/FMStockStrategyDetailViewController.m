//
//  FMStockStrategyDetailViewController.m
//  QCYZT
//
//  Created by zeng on 2024/9/2.
//  Copyright © 2024 LZKJ. All rights reserved.
//

#import "FMStockStrategyDetailViewController.h"
#import "FMStockStrategyDetailInnerTableVC.h"
#import "FMStockStrategyDetailTopView.h"
#import "FMStockStrategyPayPopView.h"
#import "FMStockStrategyDetailModel.h"
#import "FMOuterTableView.h"
#import "EnablePayModel.h"
#import "PaymentView.h"
#import "FMPayTool.h"
#import "FMShareHelper.h"
#import "HttpRequestTool+Strategy.h"
#import "HttpRequestTool+Winner.h"
#import "HttpRequestTool+Pay.h"

@interface FMStockStrategyDetailViewController ()<SGPageTitleViewDelegate, UITableViewDelegate, UITableViewDataSource, FMStockStrategyDetailInnerTableVCDelegate>

@property (nonatomic, strong) UIView *topView;
@property (nonatomic, strong) FMOuterTableView *tableView;
@property (nonatomic, strong) FMStockStrategyDetailTopView *tableHeaderView;
@property (nonatomic, strong) SGPageTitleView *pageTitleView;
@property (nonatomic, strong) UITableViewCell *contentCell;
@property (nonatomic, strong) FMStockStrategyDetailInnerTableVC *showingVC;
@property (nonatomic, strong) UIView *bottomView;

@property (nonatomic, assign) NSInteger curveIndex; // 图表选择index
@property (nonatomic, strong) FMStockStrategyDetailModel *detailModel;
@property (nonatomic, strong) NSArray<StrategyTriggerPoolsModel *> *triggerPools;
@property (nonatomic, strong) NSArray<StrategyCurveModel *> *curveModels;
@property (nonatomic, strong) NSArray<FMStockStrategyPayModel *> *payInfoModels;

@property (nonatomic, assign) NSUInteger page;
@property (nonatomic, assign) NSUInteger currentPage;
@property (nonatomic, assign) NSUInteger pageSize;
@property (nonatomic, strong) NSMutableArray<FMCommentFrameModel *> *commentFrames;

@property (nonatomic, assign) BOOL pageTitleLoad;
@property (nonatomic, assign) BOOL canMonitorInnerTableScroll;


@end

@implementation FMStockStrategyDetailViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = UIColor.up_contentBgColor;
    // Do any additional setup after loading the view.
    [self.view addSubview:self.topView];
    [self.topView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(0);
        make.height.equalTo(UI_SAFEAREA_TOP_HEIGHT);
    }];
    
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(0);
        make.top.equalTo(self.topView.mas_bottom);
    }];
    
    [self.view addSubview:self.bottomView];
    [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(0);
        make.top.equalTo(self.tableView.mas_bottom);
        make.height.equalTo(UI_SAFEAREA_BOTTOM_HEIGHT);
        make.bottom.equalTo(0);
    }];
        
    self.curveIndex = 4;
    self.pageTitleLoad = NO;
    self.tableView.hidden = YES;
    self.bottomView.hidden = YES;
    
    self.page = 1;
    self.currentPage = self.page;
    self.pageSize = 20;
    
    [self requestData];
    [self requestBuyInfo:nil];
    
    [FMHelper addLoginAndLogoutNotificationWithObserver:self selector:@selector(loginStatusChange) monitorAuthLogin:NO];
}

- (void)loginStatusChange {
    [self requestData];
    [self requestBuyInfo:nil];
}

- (void)viewWillAppear:(BOOL)animated {
    self.selfNavigationBarHidden = YES;

    [super viewWillAppear:animated];
}

- (void)requestData {
    [SVProgressHUD show];
    
    dispatch_group_t group = dispatch_group_create();

    [self requestDetail:group];
    [self requestCurve:group];
    [self requestNotTradingDay:group];
    [self requestComments:group];
    
    dispatch_group_notify(group, dispatch_get_main_queue(), ^{
        if (self.detailModel) {
            self.detailModel.triggerPools = self.triggerPools;
            self.detailModel.curveModels = self.curveModels;
            self.showingVC.commentFrames = self.commentFrames;
            self.showingVC.model = self.detailModel;
            
            [self setupPageView];
            self.tableView.hidden = NO;
            [self.tableView reloadData];
            
            self.tableHeaderView.model = self.detailModel;
            if (self.detailModel.hasPermission) {
                self.bottomView.hidden = YES;
                [self.bottomView mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.height.equalTo(UI_SAFEAREA_BOTTOM_HEIGHT);
                }];
            } else {
                self.bottomView.hidden = NO;
                [self.bottomView mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.height.equalTo(UI_SAFEAREA_BOTTOM_HEIGHT + 70);
                }];
            }

            [SVProgressHUD dismiss];
        }
    });
}

- (void)requestDetail:(dispatch_group_t)group {
    [HttpRequestTool requestTemplateDetailWithTemplateId:self.templateId start:^{
        dispatch_group_enter(group);
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        dispatch_group_leave(group);
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            self.detailModel = [FMStockStrategyDetailModel modelWithDictionary:dic[@"data"]];
            
            // 处理日期显示问题
            [self.detailModel.tradePools enumerateObjectsUsingBlock:^(StrategyTradePoolsModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                obj.showDay = obj.day.copy;
            }];
            for (NSInteger i = 1; i < self.detailModel.tradePools.count; i++) {
                StrategyTradePoolsModel *currentModel = self.detailModel.tradePools[i];
                StrategyTradePoolsModel *previousModel = self.detailModel.tradePools[i - 1];
                
                if ([currentModel.day isEqualToString:previousModel.day]) {
                    currentModel.showDay = @"";
                }
            }
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
        
        dispatch_group_leave(group);
    }];
}

- (void)requestCurve:(dispatch_group_t)group {
    [HttpRequestTool requestTemplateDetailProfitCurveWithTemplateId:self.templateId type:self.curveIndex start:^{
        if (group) {
            dispatch_group_enter(group);
        }
    } failure:^{
        if (group) {
            dispatch_group_leave(group);
        }
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            NSArray<StrategyCurveModel *> *arr = [NSArray modelArrayWithClass:[StrategyCurveModel class] json:dic[@"data"]];
            self.curveModels = arr;
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
        
        if (group) {
            dispatch_group_leave(group);
        } else {
            self.detailModel.curveModels = self.curveModels;
            self.showingVC.model = self.detailModel;
        }
    }];
}

- (void)requestNotTradingDay:(dispatch_group_t)group {
    dispatch_group_enter(group);

    NSDate *currentDate = [NSDate dateWithTimeIntervalSince1970:[FMUserDefault getServerSystemTime]/1000];
    if ([FMUPDataTool marketTimePeriodFromTimestamp:[FMUPDataTool getServerSystemTime]] == StockMarketTimePeriodPostMarket) {
        [self requestTriggerPools:group day:[currentDate dateStringWithFormatString:@"yyyy-MM-dd"]];
    } else {
        NSString *year = [currentDate dateStringWithFormatString:@"yyyy"];
        [HttpRequestTool getStockNotTradingDayWithYear:year Start:^{
        } failure:^{
            [self requestTriggerPools:group day:[currentDate dateStringWithFormatString:@"yyyy-MM-dd"]];
        } success:^(NSDictionary *dic) {
            if ([dic[@"status"] isEqualToString:@"1"]) {
                if ([dic[@"data"] isKindOfClass:[NSArray class]]) {
                    NSArray *array = [NSArray arrayWithArray:dic[@"data"]];
                    if (array.count) {
                        NSDate *lastDate = [FMUPDataTool findPreviousTradingDay:currentDate nonTradingDays:array];
                        [self requestTriggerPools:group day:[lastDate dateStringWithFormatString:@"yyyy-MM-dd"]];
                    }
                }
            } else {
                [self requestTriggerPools:group day:[currentDate dateStringWithFormatString:@"yyyy-MM-dd"]];
            }
        }];
    }
}

- (void)requestTriggerPools:(dispatch_group_t)group day:(NSString *)day {
    [HttpRequestTool requestTemplateDetailTriggerPoolsWithTemplateId:self.templateId pageNo:1 pageSize:1000 day:day start:^{
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        dispatch_group_leave(group);
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            NSArray<StrategyTriggerPoolsModel *> *arr = [NSArray modelArrayWithClass:[StrategyTriggerPoolsModel class] json:dic[@"data"]];
            self.triggerPools = arr;
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
        dispatch_group_leave(group);
    }];
}

- (void)requestComments:(dispatch_group_t)group {
    [HttpRequestTool requestStrategyCommentListWithWithPage:self.page pageSize:self.pageSize strategyid:self.templateId start:^{
        if (group) {
            dispatch_group_enter(group);
        }
    } failure:^{
        if (group) {
            dispatch_group_leave(group);
        }
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [self endRefreshForSuccess];
            if (self.page == 1) {
                [self.showingVC.tableView.mj_footer resetNoMoreData];
                [self.commentFrames removeAllObjects];
            }
            
            NSArray *dataArr = [NSArray modelArrayWithClass:[FMCommentModel class] json:dic[@"data"]];
            if (dataArr.count < self.pageSize) {
                [self.showingVC.tableView.mj_footer endRefreshingWithNoMoreData];
                self.showingVC.tableView.tableFooterView = [FMStockDetaiDisclaimerTool tableFooterView2];
            } else {
                self.showingVC.tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, CGFLOAT_MIN)];
            }
            
            [self dealCommentFramesWithComments:dataArr];
            if (!self.commentFrames.count) {
                self.showingVC.tableView.mj_footer.hidden = YES;
            } else {
                self.showingVC.tableView.mj_footer.hidden = NO;
            }
        } else {
            [self endRefreshForFailure];
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
        
        if (group) {
            dispatch_group_leave(group);
        } else {
            self.showingVC.commentFrames = self.commentFrames;
            [self.showingVC.tableView reloadData];
        }
    }];
}

- (void)requestBuyInfo:(void(^)(void))block {
    [HttpRequestTool requestStrategyBuyInfoWithTemplateId:self.templateId start:^{
    } failure:^{
        if (block) {
            [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        }
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            self.payInfoModels = [NSArray modelArrayWithClass:[FMStockStrategyPayModel class] json:dic[@"data"]];
            if (block) {
                block();
            }
        } else {
            if (block) {
                [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            }
        }
    }];
}

- (void)requestPayWithModel:(FMStockStrategyPayModel *)model {
    if ([FMHelper checkLoginStatus]) {
        [[FMPayTool payTool] judgeConfirmOrderStatusWithDakaId:nil certCode:nil clickView:nil confirmOperation:^{
            FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
            EnablePayModel *enablePayModel = [[EnablePayModel alloc] init];
            enablePayModel.paydesc = [NSString stringWithFormat:@"您当前拥有%zd金币，确认支付", userModel.coin];
            enablePayModel.name = @"金币";
            enablePayModel.num = [NSString stringWithFormat:@"%zd", userModel.coin];
            enablePayModel.type = PaymentTypeCoin;
            enablePayModel.consumeType = model.consumeType;
            NSString *price = [NSString stringWithFormat:@"%.0f", model.price];
            NSString *title = model.templateName;
            [PaymentView showWithEnablePayModel:enablePayModel payPrice:price productName:title bottomReminder:@"注：已付费的产品和内容，不会重复扣费" payAction:^(EnablePayModel *selectedModel) {
                [HttpRequestTool payStrategyWithGoodsId:model.goodsId usePoints:selectedModel.usePoints start:^{
                    [SVProgressHUD show];
                } failure:^{
                    [SVProgressHUD showErrorWithStatus:@"网络不给力"];
                } success:^(NSDictionary *dic) {
                    if ([dic[@"status"] isEqualToString:@"1"]) {
                        [SVProgressHUD showSuccessWithStatus:@"支付成功"];
                        [HttpRequestTool queryDakaCoinsAndPointsStart:nil failure:nil success:nil];
                        
                        self.detailModel.hasPermission = YES;
                        self.showingVC.model = self.detailModel;
                        [self setupPageView];
                        
                        self.bottomView.hidden = YES;
                        [self.bottomView mas_updateConstraints:^(MASConstraintMaker *make) {
                            make.height.equalTo(UI_SAFEAREA_BOTTOM_HEIGHT);
                        }];
                        [self.tableView reloadData];
                    } else {
                        [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
                    }
                }];
            } dismissBlock:^{
            }];
        }];
    }
}

- (void)endRefreshForFailure {
    [self.showingVC.tableView.mj_footer endRefreshing];
    self.page = self.currentPage;
}

- (void)endRefreshForSuccess {
    [self.showingVC.tableView.mj_footer endRefreshing];
    self.currentPage = self.page;
}

/// 处理评论model
- (void)dealCommentFramesWithComments:(NSArray *)comments {
    NSMutableArray *tmpFrameArr = [NSMutableArray array];
    for (FMCommentModel *commentModel in comments) {
        FMCommentFrameModel *frameModel = [[FMCommentFrameModel alloc] init];
        frameModel.commentModel = commentModel;
        [tmpFrameArr addObject:frameModel];
    }
    
    [self removeRepeatDataWithArray:tmpFrameArr];
}

/// 移除重复数据
- (void)removeRepeatDataWithArray:(NSArray *)array {
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    for (FMCommentFrameModel *frameModel in array) {
        [dic setObject:frameModel forKey:frameModel.commentModel.commentId];
    }
    
    for (FMCommentFrameModel *frameModel in self.commentFrames.reverseObjectEnumerator) {
        if ([dic valueForKey:frameModel.commentModel.commentId]) {
            [self.commentFrames removeObject:frameModel];
        }
    }
    [self.commentFrames addObjectsFromArray:array];
}

#pragma mark - SGPageTitleViewDelegate
- (void)pageTitleView:(SGPageTitleView *)pageTitleView selectedIndex:(NSInteger)selectedIndex {
    if (self.pageTitleLoad) {
        self.canMonitorInnerTableScroll = NO;
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            self.canMonitorInnerTableScroll = YES;
        });
        
        [self.tableView scrollToRowAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:0] atScrollPosition:UITableViewScrollPositionTop animated:NO];
        if (self.showingVC.tableView.numberOfSections > selectedIndex) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [self.showingVC.tableView scrollToRowAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:selectedIndex] atScrollPosition:UITableViewScrollPositionTop animated:NO];
            });
        }
    }
    if (!self.pageTitleLoad) {
        self.canMonitorInnerTableScroll = YES;
        self.pageTitleLoad = YES;
    }
}


#pragma mark - TableView Delegate/Datasource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    return self.contentCell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return UI_SCREEN_HEIGHT-(UI_SAFEAREA_TOP_HEIGHT + UI_SegmentControl_Height + UI_SAFEAREA_BOTTOM_HEIGHT + (self.detailModel.hasPermission ? 0 : 70));
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return self.pageTitleView;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return UI_SegmentControl_Height;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}


#pragma mark - UIScrollViewDelegate
- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    CGFloat topCellOffset = [self.tableView rectForSection:0].origin.y;
    
    // 如果里层tableView的偏移量大于0，将外层tableView的偏移量定在tableTopViewHeight，保持悬停
    if (self.showingVC.tableView.contentOffset.y > 0) {
        self.tableView.contentOffset = CGPointMake(0, topCellOffset);
    }
    
    // 如果外层tableView偏移量小于tableTopViewHeight（也就是头部视图正在显示），发出通知让每个子tableView的偏移量变成0
    CGFloat offSetY = self.tableView.contentOffset.y;
    if (offSetY < topCellOffset) {
        for (FMStockStrategyDetailInnerTableVC *VC in self.childViewControllers) {
            VC.tableView.contentOffset = CGPointZero;
        }
    }
}

#pragma mark - FMStockStrategyDetailInnerTableVCDelegate
- (void)innerTableVCTableviewScroll:(UITableView *)innerTableview {
    CGFloat tableTopViewHeight = ([self.tableView rectForSection:0].origin.y);
    // 如果外层tableView偏移量小于tableTopViewHeight（也就是头部视图正在显示），将子tableView的偏移量变成0
    if (self.tableView.contentOffset.y < tableTopViewHeight) {
        innerTableview.contentOffset = CGPointZero;
        innerTableview.showsVerticalScrollIndicator = NO;
    } else {
        innerTableview.showsVerticalScrollIndicator = YES;
    }
}

- (void)innerTableVCScrollToSection:(NSInteger)section {
    if (self.canMonitorInnerTableScroll) {
        NSLog(@"section -- %zd", section);
        [self.pageTitleView setPageTitleViewWithProgress:1 originalIndex:self.pageTitleView.selectedIndex targetIndex:section];
    }
}

- (void)innerTableVCFooterAction:(UITableView *)tableView {
    self.page++;
    [self requestComments:nil];
}

#pragma mark - Private
- (void)setupPageView {
    NSArray *titleArr = self.detailModel.hasPermission ? @[@"策略思路", @"历史收益", @"调仓股票", @"持仓股票", @"买卖详情", @"策略评价"] : @[@"策略思路", @"历史收益", @"持仓股票", @"策略评价"];
    SGPageTitleViewConfigure *configure = [SGPageTitleViewConfigure pageTitleViewConfigure];
    configure.titleColor = UIColor.up_textSecondaryColor;
    configure.titleFont = FontWithSize(16);
    configure.titleSelectedColor = FMNavColor;
    configure.titleSelectedFont = BoldFontWithSize(16);
    configure.indicatorStyle = SGIndicatorStyleFixed;
    configure.indicatorColor = FMNavColor;
    configure.indicatorFixedWidth = 18;
    configure.indicatorHeight = 3;
    configure.indicatorCornerRadius = 1.5;
    configure.titleAdditionalWidth = 30;
    configure.equivalence = NO;
    configure.showBottomSeparator = YES;
    configure.bottomSeparatorColor = UIColor.fm_sepline_color;
    self.pageTitleView = [SGPageTitleView pageTitleViewWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SegmentControl_Height) delegate:self titleNames:titleArr configure:configure];
    self.pageTitleView.backgroundColor = UIColor.up_contentBgColor;
}

- (FMStockStrategyDetailInnerTableVC *)showingVC {
    if (!_showingVC) {
        _showingVC = [FMStockStrategyDetailInnerTableVC new];
        _showingVC.templateId = self.templateId;
        _showingVC.delegate = self;
        
        WEAKSELF
        _showingVC.incomeCellChooseBlock = ^(NSInteger index) {
            __weakSelf.curveIndex = index + 1;
            
            [__weakSelf requestCurve:nil];
        };
        _showingVC.buyStrategy = ^{
            [__weakSelf buyStrategy];
        };

    }
    
    return _showingVC;
}


- (void)backBtnClicked {
    [[FMHelper getCurrentVC].navigationController popViewControllerAnimated:YES];
}

- (void)buyStrategy {
    if (!self.payInfoModels.count) {
        [self requestBuyInfo:^{
            [self showPayPopView];
        }];
    } else {
        [self showPayPopView];
    }
}

- (void)showPayPopView {
    FMStockStrategyPayPopView *popView = [[FMStockStrategyPayPopView alloc] initWithDataArr:self.payInfoModels title:self.detailModel.templateName chooseBlock:^(FMStockStrategyPayModel * _Nonnull model) {
        [self requestPayWithModel:model];
    }];
    [popView show];
}

- (void)shareBtnClicked {
    [FMShareHelper shareStrategyDetailWithModel:self.detailModel];
}

#pragma mark - Getter/Setter
- (UIView *)topView {
    if (!_topView) {
        _topView = [UIView new];
        
        UIImageView *imgV = [[UIImageView alloc] initWithImage:FMImgInBundle(@"其它/策略详情顶部背景")];
        imgV.contentMode = UIViewContentModeScaleAspectFill;
        [_topView addSubview:imgV];
        [imgV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.top.equalTo(0);
            make.height.equalTo(143 + UI_SAFEAREA_TOP_HEIGHT);
        }];
        
        UIView *navView = [[UIView alloc] init];
        [_topView addSubview:navView];
        [navView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(0);
            make.top.equalTo(UI_STATUS_HEIGHT);
            make.height.equalTo(UI_NAVBAR_HEIGHT);
        }];
        navView.backgroundColor = FMClearColor;
        navView.clipsToBounds = YES;
        
        
        UIButton *backBtn = [[UIButton alloc] initWithFrame:CGRectZero font:nil normalTextColor:nil backgroundColor:FMClearColor title:nil image:FMImgInBundle(@"导航/亮白暗灰返回") target:self action:@selector(backBtnClicked)];
        [navView addSubview:backBtn];
        [backBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.bottom.top.equalTo(0);
            make.width.equalTo(UI_NAVBAR_HEIGHT);
        }];
        
        UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(18) textColor:UIColor.fm_market_nav_text_color backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        [navView addSubview:titleLabel];
        [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(0);
        }];
        titleLabel.text = @"策略详情";
        
        
        UIButton *shareBtn = [[UIButton alloc] initWithFrame:CGRectZero font:nil normalTextColor:nil backgroundColor:FMClearColor title:nil image:FMImgInBundle(@"导航/亮白暗灰分享") target:self action:@selector(shareBtnClicked)];
        [navView addSubview:shareBtn];
        [shareBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(0);
            make.right.equalTo(-15);
        }];
    }
    
    return _topView;
}

- (FMStockStrategyDetailTopView *)tableHeaderView {
    if (!_tableHeaderView) {
        _tableHeaderView = [[FMStockStrategyDetailTopView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 143)];
    }
    
    return _tableHeaderView;
}

- (FMOuterTableView *)tableView {
    if (!_tableView) {
        _tableView = [[FMOuterTableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.backgroundColor = FMClearColor;
        _tableView.showsVerticalScrollIndicator = NO;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.tableFooterView = [[UIView alloc] init];
        _tableView.tableHeaderView = self.tableHeaderView;
        _tableView.bounces = NO;
    }
    
    return _tableView;
}

- (UITableViewCell *)contentCell {
    if (!_contentCell) {
        UITableViewCell *cell = [[UITableViewCell alloc] init];
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
        _contentCell = cell;
        
        [_contentCell.contentView addSubview:self.showingVC.view];
        [self.showingVC.view mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(UIEdgeInsetsZero);
        }];
    }
    
    return _contentCell;
}


- (UIView *)bottomView {
    if (!_bottomView) {
        _bottomView = [UIView new];
        _bottomView.backgroundColor = UIColor.up_contentBgColor;
        
        UIButton *btn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(16) backgroundColor:FMNavColor target:self action:@selector(buyStrategy)];
        [_bottomView addSubview:btn];
        [btn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(15);
            make.right.equalTo(-15);
            make.top.equalTo(10);
            make.height.equalTo(45);
        }];
        UI_View_Radius(btn, 22.5);
        [btn setTitle:@"购买策略" forState:UIControlStateNormal];
    }
    
    return _bottomView;
}

- (NSMutableArray<FMCommentFrameModel *> *)commentFrames {
    if (!_commentFrames) {
        _commentFrames = [NSMutableArray array];
    }
    
    return _commentFrames;
}

@end
