//
//  FMBlockDetailConstituentStockListView.m
//  QCYZT
//
//  Created by zeng on 2024/7/4.
//  Copyright © 2024 LZKJ. All rights reserved.
//

#import "FMBlockDetailConstituentStockListView.h"
#import "FMBlockDetailConstituentStockCell.h"
#import "FMUPDataTool.h"

@interface FMBlockDetailConstituentStockListView()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UIView *topView;
@property (nonatomic, strong) UILabel *zdLabel;
@property (nonatomic, strong) UITableView *tableView;

// 行情数据相关
@property (nonatomic, strong) NSMutableArray<UPMarketCodeMatchInfo *> *stockArray;      // 所有相关股票数据
@property (nonatomic, strong) NSArray<UPHqStockHq *> *hqDatas;  // 排序后的数据
@property (nonatomic, strong) UPMarketMonitor *monitor;                          // 请求数据定时器
@property (nonatomic, assign) BOOL isShowing;                                    // 当前页面是否正在显示

@end

@implementation FMBlockDetailConstituentStockListView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    [self addSubview:self.topView];
    [self.topView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(0);
        make.height.equalTo(20);
    }];
    
    [self addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(20);
        make.left.right.bottom.equalTo(0);
    }];
}

- (void)viewWillAppear {
    [super viewWillAppear];
    self.isShowing = YES;
    [self requestStockHqWithArray:self.stockArray];
}

- (void)viewWillDisappear {
    [super viewWillDisappear];
    self.isShowing = NO;
    [self.monitor stopMonitorWithTag:FMBlockConstituentStocksTag + self.type];
}

#pragma mark - UITableViewDelegate/DataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.hqDatas.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    FMBlockDetailConstituentStockCell *cell = [tableView reuseCellClass:[FMBlockDetailConstituentStockCell class]];
    cell.type = self.type;
    cell.stockHq = self.hqDatas[indexPath.row];
    return cell;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}


- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
    
    UPHqStockHq *hq = self.hqDatas[indexPath.row];

    NSMutableString *paramSetCode = [NSMutableString stringWithCapacity:64];
    NSMutableString *paramCode = [NSMutableString stringWithCapacity:64];

    [self.hqDatas enumerateObjectsUsingBlock:^(UPHqStockHq *obj, NSUInteger idx, BOOL *stop) {
        if(paramSetCode.length > 0) {
            [paramSetCode appendString:@"_"];
        }
        
        if(paramCode.length > 0) {
            [paramCode appendString:@"_"];
        }

        [paramSetCode appendFormat:@"%d", obj.setCode];
        [paramCode appendFormat:@"%@", obj.code];
    }];
    
    UPRouterNavigate([@"upchina://market/stock" up_buildURLWithQueryParams:@{
        @"setcode" : paramSetCode,
        @"code" : paramCode,
        @"current" : [NSString stringWithFormat:@"%zd", indexPath.row],
    }]);
}

// 请求行情数据
- (void)requestStockHqWithArray:(NSArray<UPMarketCodeMatchInfo *> *)array {
    FMLog(@"%@想请求%zd条数据--%@", NSStringFromClass([self class]), array.count, [[array valueForKeyPath:@"name"] componentsJoinedByString:@","]);
    if (array.count) {
        // 将UPOptionalModel数组转换成UPHqStockUnique用于请求行情的数组
        NSMutableArray<UPHqStockUnique *> *stocksM = [NSMutableArray array];
        [array enumerateObjectsUsingBlock:^(UPMarketCodeMatchInfo *obj, NSUInteger idx, BOOL *_Nonnull stop) {
            UPHqStockUnique *stock = [UPHqStockUnique new];
            stock.setCode = obj.setCode;
            stock.code = obj.code;
            [stocksM addObject:stock];
        }];
        
        if (UPTAFNetworkReachable && self.isShowing) { // 防止切到别的页面后收到通知或者滚动事件未停止触发请求
            UPMarketOptStockHqReq *hqReq = [[UPMarketOptStockHqReq alloc] initWithStockArray:stocksM.copy];
            hqReq.simpleData = YES;
            WeakSelf(weakSelf);

            [self.monitor startMonitorOptStockHq:hqReq tag:FMBlockConstituentStocksTag + self.type completionHandler:^(UPMarketOptStockHqRsp *rsp, NSError *error) {
//                NSArray *tmpArr = [rsp.dataArray valueForKeyPath:@"name"];
//                FMLog(@"%@请求到%zd条数据--%@", NSStringFromClass([self class]), rsp.dataArray.count, [tmpArr componentsJoinedByString:@","]);
                
                if (IsValidateArray(rsp.dataArray)) {
                    NSArray *arr;
                    if (weakSelf.type ==  FMStockDetailConstituentStockTypeRise) {
                        arr = [FMHelper sortedDatas:rsp.dataArray withKey:@"changeRatio" ascending:NO];
                    } else if (weakSelf.type == FMStockDetailConstituentStockTypeFall) {
                        arr = [FMHelper sortedDatas:rsp.dataArray withKey:@"changeRatio" ascending:YES];
                    } else {
                        arr = [FMHelper sortedDatas:rsp.dataArray withKey:@"turnoverRate" ascending:NO];
                    }
                    weakSelf.hqDatas = arr;
                    
                    dispatch_async(dispatch_get_main_queue(), ^{
                        [weakSelf.tableView reloadData];
                    });
                }
            }];
        }
    } else {
        [self.monitor stopMonitorWithTag:FMBlockConstituentStocksTag + self.type];
    }
}

- (void)setDatas:(NSArray<FMBlockDetailConstituentStockModel *> *)datas {
    _datas = datas;
    
    [self.stockArray removeAllObjects];
    for (FMBlockDetailConstituentStockModel *model in datas) {
        UPMarketCodeMatchInfo *info = [FMUPDataTool matchInfoWithSetCodeAndCode:model.stockCode];
        [self.stockArray addObject:info];
    }
}

- (UPMarketMonitor *)monitor {
    if (!_monitor) {
        _monitor = [[UPMarketMonitor alloc] init];
    }
    
    return _monitor;
}

- (NSMutableArray<UPMarketCodeMatchInfo *> *)stockArray {
    if (!_stockArray) {
        _stockArray = [NSMutableArray array];
    }
    
    return _stockArray;
}

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain delegate:self dataSource:self viewController:nil];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        [_tableView registerCellClass:[FMBlockDetailConstituentStockCell class]];
        _tableView.bounces = NO;
        _tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, CGFLOAT_MIN)];
        _tableView.rowHeight = 60;
    }
    
    return _tableView;
}

- (UIScrollView *)baseScrollView {
    return self.tableView;
}

- (UIView *)topView {
    if (!_topView) {
        _topView = [UIView new];
        
        UILabel *label1 = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(13) textColor:UIColor.up_textSecondary1Color backgroundColor:FMClearColor numberOfLines:1];
        label1.text = @"股票名称/代码";
        [_topView addSubview:label1];
        [label1 mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(15);
            make.centerY.equalTo(0);
        }];
        UILabel *label2 = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(13) textColor:UIColor.up_textSecondary1Color backgroundColor:FMClearColor numberOfLines:1];
        label2.text = @"股价";
        [_topView addSubview:label2];
        [label2 mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_topView.mas_centerX);
            make.centerY.equalTo(0);
        }];
        UILabel *label3 = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(13) textColor:UIColor.up_textSecondary1Color backgroundColor:FMClearColor numberOfLines:1];
        label3.text = @"涨跌幅";
        [_topView addSubview:label3];
        [label3 mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(-15);
            make.centerY.equalTo(0);
        }];
        self.zdLabel = label3;
    }
    
    return _topView;
}

- (void)setType:(FMStockDetailConstituentStockType)type {
    _type = type;
    
    if (type == FMStockDetailConstituentStockTypeTurnover) {
        self.zdLabel.text = @"换手率";
    } else {
        self.zdLabel.text = @"涨跌幅";
    }
}

@end
