//
//  FMUnlockPositionViewController.m
//  QCYZT
//
//  Created by zeng on 2024/12/11.
//  Copyright © 2024 LZKJ. All rights reserved.
//

#import "FMUnlockPositionViewController.h"
#import "FMUnlockPositionSectionHeader.h"
#import "FMUnlockPositionCell.h"
#import "FMSelfStockGroupStockManagementModel.h"
#import "FMUnlockPositionPopView.h"
#import "FMUnlockPositionNoDataCell.h"
#import "HttpRequestTool+Search.h"
#import "FMNewSearchStockModel.h"
#import "CacheRequestManager.h"
#import "FMUnlockPositionGoodsModel.h"
#import "FMUnlockPositionStockDetailViewController.h"
#import "HttpRequestTool+Pay.h"
#import "EnablePayModel.h"
#import "PaymentView.h"
#import "FMPayTool.h"
#import "FMPaySuccessPopView.h"

@interface FMUnlockPositionViewController ()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UIImageView *topImgV;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UIView *tableHeaderView;
@property (nonatomic, strong) UIView *tableFooterView;

@property (nonatomic, strong) NSDictionary *stockhqCache; // stockHq缓存
@property (nonatomic, assign) NSInteger beginIndex;                              // 可见范围内第一个cell的index
@property (nonatomic, assign) NSInteger visibleCount;                            // 可见范围内cell数
@property (nonatomic, strong) UPMarketMonitor *monitor;                          // 请求数据定时器
@property (nonatomic, assign) BOOL isShowing;                                    // 当前页面是否正在显示
@property (nonatomic, assign) BOOL isDragEnd;                                    // 拖拽结束

@property (nonatomic, strong) NSArray<UPMarketCodeMatchInfo *> *optionStockAray;

@property (nonatomic, strong) FMUnlockPositionGoodsModel *goodsModel;

@end

@implementation FMUnlockPositionViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.view.backgroundColor = ColorWithHex(0xff734d);
    
    [self.view addSubview:self.topImgV];
    [self.topImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.equalTo(0);
        make.height.equalTo(UI_Relative_WidthValue(280));
    }];
    
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(0);
        make.top.equalTo(UI_SAFEAREA_TOP_HEIGHT + 10);
        make.bottom.equalTo(-UI_SAFEAREA_BOTTOM_HEIGHT);
    }];
    self.tableView.hidden = YES;
    
    [self requestSelfStocks];
    [self requestAllStocks];
}

- (void)viewWillAppear:(BOOL)animated {
    self.selfNavigationBarHidden = YES;
    [super viewWillAppear:animated];
    
    self.isShowing = YES;
    [self requestStockHqNeedLoadAll:NO];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    self.isShowing = NO;
    [self.monitor stopMonitor];
}

#pragma mark - UITableView
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return MAX(self.optionStockAray.count, 1);
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.optionStockAray.count) {
        FMUnlockPositionCell *cell = [tableView reuseCellClass:[FMUnlockPositionCell class]];
        [cell configModel:self.optionStockAray[indexPath.row] hqDic:self.stockhqCache];
        
        if (self.optionStockAray.count == 1) {
            cell.cellLocation = CellLocationOnlyOne;
        } else {
            if (indexPath.row == 0) {
                cell.cellLocation = CellLocationTop;
            } else if (indexPath.row == self.optionStockAray.count - 1) {
                cell.cellLocation = CellLocationBottom;
            } else {
                cell.cellLocation = CellLocationMiddle;
            }
        }
        WEAKSELF
        cell.unlockBlock = ^(UPHqStockHq * _Nonnull stockHq) {
            [__weakSelf requestPermissions:stockHq];
        };
        return cell;
    }
    
    FMUnlockPositionNoDataCell *cell = [tableView reuseCellClass:[FMUnlockPositionNoDataCell class]];
    return cell;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    FMUnlockPositionSectionHeader *header = [tableView reuseViewClass:[FMUnlockPositionSectionHeader class]];
    return header;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return 80;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [[UIView alloc] init];
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
    [self.monitor stopMonitorWithTag:0];

    if (scrollView.contentOffset.y + scrollView.frame.size.height <= scrollView.contentSize.height + 1) {
        self.isDragEnd = NO;
    }
}

- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
    self.isDragEnd = YES;
    [self updateVisibleCellInScrollView:scrollView];
}

- (void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate {
    if (!decelerate) {
        self.isDragEnd = YES;
    }
    
    [self updateVisibleCellInScrollView:scrollView];
}

- (void)scrollViewDidScrollToTop:(UIScrollView *)scrollView {
    [self updateVisibleCellInScrollView:scrollView];
}

#pragma mark - HTTP
- (void)requestSelfStocks {
    [HttpRequestTool stockListInSelfStockGroupWithGroupId:@"0" start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD dismiss];
            NSArray *array = [NSArray modelArrayWithClass:[FMSelfStockGroupStockManagementModel class] json:dic[@"data"]];
            NSMutableArray *arr = [NSMutableArray arrayWithCapacity:array.count];
            for (FMSelfStockGroupStockManagementModel *managementModel in array) {
                UPMarketCodeMatchInfo *info = [UPMarketManager queryStockWithSetCode:[managementModel.setCode integerValue] code:managementModel.code];
                
                if (!info) {
                    continue;
                }
                UPMarketCodeMatchInfo *model = [[UPMarketCodeMatchInfo alloc] init];
                model.code = info.code;
                model.setCode = info.setCode;
                model.category = info.category;
                model.name = info.name;
                [arr addObject:model];
            }
            self.optionStockAray = arr.copy;
            [self requestStockHqNeedLoadAll:YES];
            
            self.tableView.hidden = NO;
            [self.tableView reloadData];
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

- (void)requestAllStocks {
    [[CacheRequestManager shared] performRequest:@"allStocks"
                                  cacheInterval:6 * 3600
                                  requestBlock:^(void(^saveData)(id)) {
        [HttpRequestTool getAllStockWithStart:^{
        } failure:^{
        } success:^(NSDictionary *dic) {
            if ([dic[@"status"] isEqualToString:@"1"]) {
                [DBManager clearTableWithClass:[FMNewSearchStockModel class]];
                [[CacheRequestManager shared] clearCacheForKey:@"allStocks"];
                
                NSArray *array = [NSArray modelArrayWithClass:[FMNewSearchStockModel class] json:dic[@"data"]];
                [DBManager insertBatchUseTransactionWithModels:array finishBlock:^(_Bool issuccess) {
                    if (issuccess) {
                        saveData(@{}); // 随便存一条数据作为有数据的标记
                    }
                }];
            }
        }];
    } cacheDataBlock:^(id cachedData) {
    }];
}

- (void)requestPermissions:(UPHqStockHq *)stockHq {
    if (!self.goodsModel) {
        [HttpRequestTool requestUnlockPositionGetGoodsInfoWithStart:^{
            [SVProgressHUD show];
        } failure:^{
            [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        } success:^(NSDictionary *dic) {
            if (lz_HttpStatusCheck(dic)) {
                [SVProgressHUD dismiss];
                
                self.goodsModel = [FMUnlockPositionGoodsModel modelWithDictionary:dic[@"data"]];
                [self showPopView:stockHq];
            } else {
                [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            }
        }];
    } else {
        [self showPopView:stockHq];
    }
}

#pragma mark - Private
- (void)unlockBtnClick:(UIButton *)btn {
    [self requestPermissions:nil];
}

- (void)backArrowClicked {
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)showPopView:(UPHqStockHq *)stockHq {
    FMUnlockPositionPopView *popView = [FMUnlockPositionPopView new];
    if (stockHq) {
        popView.stockHq = stockHq;
    }
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    if (!userModel.vip) {
        popView.model = self.goodsModel;
    } else {
        popView.model = nil;
    }
    popView.dismissBlock = ^(UPMarketCodeMatchInfo *matchInfo, CGFloat tagetPrice) {
        [self jumpToDetail:matchInfo tagetPrice:tagetPrice];
    };
    popView.gotoPayBlock = ^(UPMarketCodeMatchInfo *matchInfo, CGFloat tagetPrice) {
        [self gotoPay:matchInfo tagetPrice:tagetPrice];
    };
    [popView show];
}

- (void)jumpToDetail:(UPMarketCodeMatchInfo *)matchInfo tagetPrice:(CGFloat)tagetPrice {
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.25 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        FMUnlockPositionStockDetailViewController *vc = [FMUnlockPositionStockDetailViewController new];
        vc.matchInfo = matchInfo;
        vc.price = tagetPrice;
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    });
}

- (void)gotoPay:(UPMarketCodeMatchInfo *)matchInfo tagetPrice:(CGFloat)tagetPrice {
    if ([FMHelper checkLoginStatus]) {
        [[FMPayTool payTool] judgeConfirmOrderStatusWithDakaId:self.goodsModel.consumeBignameid certCode:self.goodsModel.certCode clickView:nil confirmOperation:^{
            FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
            EnablePayModel *enablePayModel = [[EnablePayModel alloc] init];
            enablePayModel.paydesc = [NSString stringWithFormat:@"您当前拥有%zd金币，确认支付", userModel.coin];
            enablePayModel.name = @"金币";
            enablePayModel.num = [NSString stringWithFormat:@"%zd", userModel.coin];
            enablePayModel.type = PaymentTypeCoin;
            enablePayModel.consumeType = 26;
            NSString *price = [NSString stringWithFormat:@"%.0f", self.goodsModel.price];
            NSString *title = self.goodsModel.name;
            [PaymentView showWithEnablePayModel:enablePayModel payPrice:price productName:title bottomReminder:@"注：已付费的产品和内容，不会重复扣费" payAction:^(EnablePayModel *selectedModel) {
                [HttpRequestTool payUnlockPositionWithGoodsId:self.goodsModel.identifier usePoints:selectedModel.usePoints start:^{
                    [SVProgressHUD show];
                } failure:^{
                    [SVProgressHUD showErrorWithStatus:@"网络不给力"];
                } success:^(NSDictionary *dic) {
                    if ([dic[@"status"] isEqualToString:@"1"]) {
                        [SVProgressHUD dismiss];
                        FMPaySuccessPopView *popView = [[FMPaySuccessPopView alloc] init];
                        popView.jumpIndex = 5;
                        [popView show];
                        
                        [HttpRequestTool queryDakaCoinsAndPointsStart:nil failure:nil success:nil];
                        
                        self.goodsModel.buyFlag = YES;
                        [self jumpToDetail:matchInfo tagetPrice:tagetPrice];
                    } else {
                        [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
                    }
                }];
            } dismissBlock:^{
            }];
        }];
    }
}

#pragma mark - 相关股票行情数据相关
/// 处理相关股票数据，请求数据
- (void)requestStockHqNeedLoadAll:(BOOL)needLoadAll {
    if (!self.isShowing) {
        return;
    }
    
    // 请求数据
    if (needLoadAll) {
        [self requestStockHqWithArray:self.optionStockAray];
    } else {
        [self requestStockHqWithArray:[self getReqRangeArray]];
    }
}

// 获取需要请求的数据
- (NSArray *)getReqRangeArray {
    NSArray *updateDataArray = [NSArray array];
    if (_beginIndex > self.optionStockAray.count) {
        updateDataArray = [NSArray array];
    } else if (_beginIndex + _visibleCount > self.optionStockAray.count) {
        updateDataArray = [self.optionStockAray subarrayWithRange:NSMakeRange(_beginIndex, self.optionStockAray.count - _beginIndex)];
    } else if (_beginIndex >= 0 && _visibleCount > 0) {
        updateDataArray = [self.optionStockAray subarrayWithRange:NSMakeRange(_beginIndex, _visibleCount)];
    }
    
    return updateDataArray;
}

// 请求行情数据
- (void)requestStockHqWithArray:(NSArray<UPMarketCodeMatchInfo *> *)array {
    if (array.count) {
        NSMutableArray<UPHqStockUnique *> *stocksM = [NSMutableArray array];
        NSMutableDictionary *stockDic = self.stockhqCache.mutableCopy ?: [NSMutableDictionary new];
        [array enumerateObjectsUsingBlock:^(UPMarketCodeMatchInfo *obj, NSUInteger idx, BOOL *_Nonnull stop) {
            UPHqStockUnique *stock = [UPHqStockUnique new];
            stock.setCode = obj.setCode;
            stock.code = obj.code;
            [stocksM addObject:stock];
            
            NSString *key = [NSString stringWithFormat:@"%@_%@", @(stock.setCode), stock.code];
            if (!stockDic[key]) { // 可能存在拉不到行情的股票，先从从码表中获取基础信息
                UPMarketCodeMatchInfo *codeInfo = [UPMarketManager queryStockWithSetCode:stock.setCode code:stock.code];
                if (codeInfo) {
                    UPHqStockHq *stockHq = [UPHqStockHq new];
                    stockHq.setCode = codeInfo.setCode;
                    stockHq.code = codeInfo.code;
                    stockHq.name = codeInfo.name;
                    stockHq.category = codeInfo.category;
                    stockHq.origCategory = codeInfo.origCategory;
                    stockHq.precise = codeInfo.precise;
                    stockHq.tradeStatus = codeInfo.status;
                    stockDic[key] = stockHq;
                }
            }
        }];
        // 先触发一下set方法
        self.stockhqCache = stockDic.copy;
        
        if (UPTAFNetworkReachable && self.isShowing) { // 防止切到别的页面后收到通知或者滚动事件未停止触发请求
            UPMarketOptStockHqReq *hqReq = [[UPMarketOptStockHqReq alloc] initWithStockArray:stocksM.copy];
            hqReq.simpleData = YES;
            hqReq.wantNum = 30;
            WeakSelf(weakSelf);

            [self.monitor startMonitorOptStockHq:hqReq tag:0 completionHandler:^(UPMarketOptStockHqRsp *rsp, NSError *error) {
                if (IsValidateArray(rsp.dataArray)) {
                    NSMutableDictionary *cacheDic = weakSelf.stockhqCache.mutableCopy;
                    [rsp.dataArray enumerateObjectsUsingBlock:^(UPHqStockHq *_Nonnull obj,
                                                                NSUInteger idx,
                                                                BOOL *_Nonnull stop) {
                        NSString *key = [NSString stringWithFormat:@"%d_%@",obj.setCode, obj.code];
                        cacheDic[key] = obj;
                    }];
                    // 再次触发set方法
                    weakSelf.stockhqCache = cacheDic.copy;
                }
                                
                if (weakSelf.isShowing) {
                    // 请求到所有数据后更新一下需要请求股票
                    NSMutableArray *hqStockM = [NSMutableArray array];
                    [weakSelf updateFixColumnTableViewIndex];
                    NSArray *reqArray = [weakSelf getReqRangeArray];
                    [reqArray enumerateObjectsUsingBlock:^(UPMarketCodeMatchInfo *obj, NSUInteger idx, BOOL *_Nonnull stop) {
                        UPHqStockUnique *stock = [UPHqStockUnique new];
                        stock.setCode = obj.setCode;
                        stock.code = obj.code;
                        [hqStockM addObject:stock];
                    }];
                    UPMarketOptStockHqReq *stockHqReq = [[UPMarketOptStockHqReq alloc] initWithStockArray:hqStockM.copy];
                    stockHqReq.isHKDelay = YES;
                    [weakSelf.monitor modifyMonitorWithTag:0 request:stockHqReq];
                }
            }];
        }
    } else {
        [self.monitor stopMonitor];
    }
}

- (void)updateFixColumnTableViewIndex {
    NSArray *visibleCells = self.tableView.visibleCells;
    UITableViewCell *tableViewCell = [visibleCells firstObject];
    NSIndexPath *indexPath = [self.tableView indexPathForCell:tableViewCell];
    NSInteger beginIndex = indexPath.row;
    NSInteger visibleCount = visibleCells.count + 1;

    self.visibleCount = visibleCount;
    self.beginIndex = beginIndex;
}

- (void)updateVisibleCellInScrollView:(UIScrollView *)scrollView {
    if ((!self.tableView.dragging || self.isDragEnd) && scrollView.contentOffset.y >= 0) {   // 底部上拉或者 顶部下拉 不同步自选数据 避免在滚动过程中更新数据造成页面跳动
        NSLog(@"解套宝更新---");
        if (scrollView.contentOffset.y + scrollView.frame.size.height <= scrollView.contentSize.height + 1) {
            NSArray *visibleCells = self.tableView.visibleCells;
            UITableViewCell *tableViewCell = [visibleCells firstObject];
            NSIndexPath *indexPath = [self.tableView indexPathForCell:tableViewCell];
            self.beginIndex = indexPath.row;
            self.visibleCount = visibleCells.count+1;
            
            [self requestStockHqNeedLoadAll:NO];
        }
    }
}

#pragma mark - Getter/Setter
- (UIImageView *)topImgV {
    if (!_topImgV) {
        _topImgV = [[UIImageView alloc] initWithImage:ImageWithName(@"unlock_topBg")];
        _topImgV.userInteractionEnabled = YES;
        
        UIButton *backArrow = [[UIButton alloc] initWithFrame:CGRectZero font:nil normalTextColor:nil backgroundColor:FMClearColor title:nil image:ImageWithName(@"return") target:self action:@selector(backArrowClicked)];
        [_topImgV addSubview:backArrow];
        [backArrow mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(@(UI_STATUS_HEIGHT));
            make.height.width.equalTo(UI_NAVBAR_HEIGHT);
            make.left.equalTo(@0);
        }];
    }
    
    return _topImgV;
}

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped delegate:self dataSource:self viewController:self];
        [_tableView registerViewClass:[FMUnlockPositionSectionHeader class]];
        [_tableView registerCellClass:[FMUnlockPositionCell class]];
        [_tableView registerCellClass:[FMUnlockPositionNoDataCell class]];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.backgroundColor = FMClearColor;
        _tableView.tableHeaderView = self.tableHeaderView;
        _tableView.tableFooterView = self.tableFooterView;
        _tableView.bounces = NO;
        _tableView.estimatedRowHeight = 63;
        _tableView.rowHeight = UITableViewAutomaticDimension;
    }
    
    return _tableView;
}

- (UIView *)tableHeaderView {
    if (!_tableHeaderView) {
        _tableHeaderView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 10 + 88 + 30 + 150 + 15)];
        
        UIImageView *imgV = [[UIImageView alloc] initWithImage:ImageWithName(@"unlock_topText")];
        [_tableHeaderView addSubview:imgV];
        [imgV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(0);
            make.top.equalTo(10);
        }];
        
        UIImageView *imgV2 = [[UIImageView alloc] initWithImage:ImageWithName(@"unlock_topGuide")];
        [_tableHeaderView addSubview:imgV2];
        [imgV2 mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(0);
            make.top.equalTo(imgV.mas_bottom).offset(30);
            make.left.equalTo(15);
        }];
        imgV2.userInteractionEnabled = YES;
        
        UIButton *btn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(17) normalTextColor:FMWhiteColor backgroundColor:FMClearColor title:@"立即解套" image:nil target:self action:@selector(unlockBtnClick:)];
        [imgV2 addSubview:btn];
        [btn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(0);
            make.bottom.equalTo(-18);
            make.left.equalTo(15);
            make.height.equalTo(45);
        }];
        UI_View_Radius(btn, 5);
        btn.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xfc5219), ColorWithHex(0xfc0002)] withFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH - 30, 45) direction:GradientDirectionLeftToRight];
    }
    
    return _tableHeaderView;
}

- (UIView *)tableFooterView {
    UIView *tableFooterView = [UIView new];
    
    UILabel *label = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:ColorWithHex(0xFED5CA) backgroundColor:FMClearColor numberOfLines:0 textAlignment:NSTextAlignmentLeft];
    NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:@"风险提示：投资有风险，入市需谨慎！操作建议仅供当日参考，如有操作，严格控制仓位，自负盈亏。免责声明：所述观点、建议，仅供参考和学习，投资者不能将其作为投资决策的唯一依据，并应自主决策，独立承担风险，我公司不会作出任何保本或收益承诺。投资有风险，入市需谨慎！四川大决策证券投资顾问有限公司，经营证券期货业务许可证号：915101067130530143"];
    attrStr.yy_lineSpacing = 5.0;
    attrStr.yy_font = FontWithSize(12);
    label.attributedText = attrStr;
    
    CGFloat height = [attrStr boundingRectWithSize:CGSizeMake(UI_SCREEN_WIDTH - 30, CGFLOAT_MAX) options:NSStringDrawingUsesLineFragmentOrigin context:nil].size.height;
    [tableFooterView addSubview:label];
    [label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.equalTo(15);
        make.right.equalTo(-15);
    }];
    tableFooterView.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, height + 30);
    
    return tableFooterView;
}

- (UPMarketMonitor *)monitor {
    if (!_monitor) {
        _monitor = [[UPMarketMonitor alloc] init];
    }
    
    return _monitor;
}

- (void)setStockhqCache:(NSDictionary *)stockhqCache {
    _stockhqCache = stockhqCache;
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.tableView reloadData];
    });
}

@end
