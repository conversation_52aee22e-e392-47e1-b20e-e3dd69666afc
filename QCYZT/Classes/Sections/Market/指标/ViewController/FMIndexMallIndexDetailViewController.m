//
//  FMIndexMallIndexDetailViewController.m
//  QCYZT
//
//  Created by zeng on 2024/5/9.
//  Copyright © 2024 LZKJ. All rights reserved.
//

#import "FMIndexMallIndexDetailViewController.h"
#import "FMIndexMallIndexDetailTopCell.h"
#import "FMIndexMallIndexDetailImageCell.h"
#import "HttpRequestTool+Stock.h"
#import "HttpRequestTool+Pay.h"
#import "FMIndicatorImportUtil.h"
#import "EnablePayModel.h"
#import "PaymentView.h"
#import "FMPaySuccessPopView.h"
#import "FMPayTool.h"

@interface FMIndexMallIndexDetailViewController ()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableView;

@property (nonatomic, strong) UIView *bottomView;
@property (nonatomic, strong) UIButton *payBtn;
@property (nonatomic, strong) UILabel *priceLabel;


@end

@implementation FMIndexMallIndexDetailViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.title = @"指标详情";
    
    self.view.backgroundColor = FMWhiteColor;
    // Do any additional setup after loading the view.
    
    UIStackView *sv = [[UIStackView alloc] initWithAxis:UILayoutConstraintAxisVertical alignment:UIStackViewAlignmentFill distribution:UIStackViewDistributionFill spacing:0 arrangedSubviews:@[self.tableView, self.bottomView]];
    [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(70);
    }];
    
    [self.view addSubview:sv];
    [sv mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsMake(0, 0, UI_SAFEAREA_BOTTOM_HEIGHT, 0));
    }];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self configNavWhiteColorWithCloseSEL:@selector(backArrowClicked)];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [self configNavRedColor];
}

- (void)backArrowClicked {
    [self.navigationController popViewControllerAnimated:YES];
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return 1 + self.model.detailImgModels.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.row == 0) {
        FMIndexMallIndexDetailTopCell *cell = [tableView reuseCellClass:[FMIndexMallIndexDetailTopCell class]];
        cell.model = self.model;
        WEAKSELF
        cell.payBlock = ^(UIButton * _Nonnull btn) {
            [__weakSelf payBtnClick:btn];
        };
        return cell;
    } else {
        FMIndexMallIndexDetailImageCell *cell = [tableView reuseCellClass:[FMIndexMallIndexDetailImageCell class]];
        cell.model = self.model.detailImgModels[indexPath.row - 1];
        cell.refreshBlock = ^{
            [self.tableView reloadData];
        };
        return cell;
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.row == 0) {
        return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMIndexMallIndexDetailTopCell class]) configuration:^(FMIndexMallIndexDetailTopCell *cell) {
            cell.model = self.model;
        }];
    } else {
        return self.model.detailImgModels[indexPath.row - 1].imageHeight;
    }
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}


- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (void)payBtnClick:(UIButton *)btn {
    if ([FMHelper checkLoginStatus]) {
        [[FMPayTool payTool] judgeConfirmOrderStatusWithDakaId:self.model.consumeBignameid certCode:nil clickView:nil confirmOperation:^{
            FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
            EnablePayModel *enablePayModel = [[EnablePayModel alloc] init];
            enablePayModel.paydesc = [NSString stringWithFormat:@"您当前拥有%zd金币，确认支付", userModel.coin];
            enablePayModel.name = @"金币";
            enablePayModel.num = [NSString stringWithFormat:@"%zd", userModel.coin];
            enablePayModel.type = PaymentTypeCoin;
            NSString *price = [NSString stringWithFormat:@"%ld", self.model.price];
            NSString *title = self.model.indexName;
            [PaymentView showWithEnablePayModel:enablePayModel payPrice:price productName:title bottomReminder:@"注：已付费的产品和内容，不会重复扣费" payAction:^(EnablePayModel *selectedModel) {
                // 3.支付
                [HttpRequestTool payIndexWithGoodsId:self.model.goodsId usePoints:selectedModel.usePoints start:^{
                    [SVProgressHUD show];
                } failure:^{
                    [SVProgressHUD showErrorWithStatus:@"网络不给力"];
                } success:^(NSDictionary *dic) {
                    if ([dic[@"status"] isEqualToString:@"1"]) {
                        [SVProgressHUD dismiss];
                        
                        [HttpRequestTool queryDakaCoinsAndPointsStart:nil failure:nil success:nil];
                        
                        FMPaySuccessPopView *popView = [[FMPaySuccessPopView alloc] init];
                        popView.jumpIndex = 5;
                        [popView show];
                        
                        [HttpRequestTool requestIndexAndTemplateList];
                    } else {
                        [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
                    }
                }];
            } dismissBlock:^{
            }];
        }];
    }
}


- (void)requestData {
    [HttpRequestTool requestIndexDetailWithFormulaId:self.formulaId start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        WEAKSELF
        [self.view showReloadNetworkViewWithBlock:^{
            [__weakSelf requestData];
        }];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            FMIndexMallListModel *model = [FMIndexMallListModel modelWithDictionary:dic[@"data"]];
            
            if (model.detailImg.length) {
                model.detailImgModels = [NSArray modelArrayWithClass:[FMIndexMallListDetailImgModel class] json:[JsonTool dicOrArrFromJsonString:model.detailImg]];
            }
            
            [HttpRequestTool requestIndexPayInfoWithFormulaId:self.formulaId start:^{
            } failure:^{
            } success:^(NSDictionary *dic) {
                if ([dic[@"status"] isEqualToString:@"1"]) {
                    model.price = [dic[@"data"][@"price"] integerValue];
                    model.shelfLife = dic[@"data"][@"shelfLife"];
                    model.consumeBignameid = dic[@"data"][@"consumeBignameid"];
                    self.model = model;
                }
            }];
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            WEAKSELF
            [self.view showReloadNetworkViewWithBlock:^{
                [__weakSelf requestData];
            }];
        }
    }];
    

}

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped delegate:self dataSource:self viewController:self];
        [_tableView registerCellClass:[FMIndexMallIndexDetailTopCell class]];
        [_tableView registerCellClass:[FMIndexMallIndexDetailImageCell class]];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, CGFLOAT_MIN)];
        _tableView.bounces = NO;
    }
    
    return _tableView;
}

- (UIView *)bottomView {
    if (!_bottomView) {
        _bottomView = [UIView new];
        _bottomView.backgroundColor = FMWhiteColor;
        
        [_bottomView addSubview:self.payBtn];
        [self.payBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(15);
            make.right.equalTo(-15);
            make.top.equalTo(10);
            make.height.equalTo(45);
        }];
        UI_View_Radius(self.payBtn, 22.5);
        
        UIStackView *sv = [[UIStackView alloc] initWithAxis:UILayoutConstraintAxisHorizontal alignment:UIStackViewAlignmentCenter distribution:UIStackViewDistributionEqualSpacing spacing:15 arrangedSubviews:nil];
        [self.payBtn addSubview:sv];
        [sv mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(0);
        }];
        sv.userInteractionEnabled = NO;
        
        UILabel *payLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(16) textColor:ColorWithHex(0x5c2c17) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
        payLabel.text = @"立即购买";
        [sv addArrangedSubview:payLabel];
        [sv addArrangedSubview:self.priceLabel];
    }
    
    return _bottomView;
}

- (UIButton *)payBtn {
    if (!_payBtn) {
        _payBtn = [[UIButton alloc] initWithFrame:CGRectZero font:nil normalTextColor:nil backgroundColor:ColorWithHex(0xf5ce62) title:nil image:nil target:self action:@selector(payBtnClick:)];
    }
    
    return _payBtn;
}

- (UILabel *)priceLabel {
    if (!_priceLabel) {
        _priceLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:ColorWithHex(0x5c2c17) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    }
    
    return _priceLabel;
}

- (void)setFormulaId:(NSInteger)formulaId {
    _formulaId = formulaId;

    [self requestData];
}


- (void)setModel:(FMIndexMallListModel *)model {
    _model = model;
    
//    if (model.hasPermission && (model.expiryDate == 0 || model.expiryDate >= [FMUPDataTool getServerSystemTime])) {
//        self.bottomView.hidden = YES;
//    } else {
//        self.bottomView.hidden = NO;
        
        if (model.price > 0) {
            self.priceLabel.hidden = NO;
            if (model.shelfLife.length) {
                self.priceLabel.text = [NSString stringWithFormat:@"%zd金币/%@", model.price, model.shelfLife];
            } else {
                self.priceLabel.text = [NSString stringWithFormat:@"%zd金币", model.price];
            }
        } else {
            self.priceLabel.hidden = YES;
        }
//    }
    
    [self.tableView reloadData];
}

@end
