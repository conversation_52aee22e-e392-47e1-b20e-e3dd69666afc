//
//  FMMarketForcastViewController.m
//  QCYZT
//
//  Created by shumi on 2022/5/24.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMMarketForcastViewController.h"
#import "FMOuterTableView.h"
#import "FMMarketForcastHeaderView.h"
#import "MarketForecastModel.h"
#import "NSObject+FBKVOController.h"
#import "MarketForecastGuessView.h"
#import "UPMarketCommonHeader.h"
#import "MarketForecastRuleView.h"
#import "FMMarketForcastCommentViewController.h"
#import "FMCommentView.h"
#import "FMDetailBottomView.h"
#import "HttpRequestTool+MarketForecast.h"

@interface FMMarketForcastViewController ()<UITableViewDelegate, UITableViewDataSource,SGPageTitleViewDelegate, SGPageContentCollectionViewDelegate,FMDetailBottomViewDelegate,FMInnerTableVCDelegate, FMDetailBottomViewDelegate>
/// 表视图
@property (nonatomic, strong) FMOuterTableView *tableView;
/// 评论区cell容器
@property (nonatomic, strong) UITableViewCell *contentCell;
/// 评论区
@property (nonatomic, strong) SGPageContentCollectionView *cellInnerScrollView;
/// 评论标题选项卡
@property (nonatomic, strong) SGPageTitleView *pageTitleView;
/// 当前显示的评论控制器
@property (nonatomic,   weak) FMMarketForcastCommentViewController *showingVC;
/// 竞猜头试图
@property (nonatomic, strong) FMMarketForcastHeaderView *headerView;
/// 竞猜数据模型
@property (nonatomic, strong) MarketForecastModel *marketMode;
/// 预测下注的弹窗
@property (nonatomic, strong) MarketForecastGuessView *forecastGuessView;
/// 预测规则弹窗
@property (nonatomic, strong) MarketForecastRuleView *forecastRuleView;
/// 股票行情请求工具
@property (nonatomic, strong) UPMarketMonitor *monitor;
/// 底部发送评论UI
@property (nonatomic, strong) FMDetailBottomView *bottomView;
/// 评论输入框
@property (nonatomic, strong) FMCommentView *commentView;
/// 当前大盘id
@property (nonatomic, copy) NSString *currentForecastId;

@property (nonatomic, assign) BOOL needsRefresh;

@end

@implementation FMMarketForcastViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = ColorWithHex(0xFFAE67);
    
    self.navigationItem.title = @"大盘预测";
    
    [self.view addSubview:self.bottomView];
    self.bottomView.delegate = self;
    [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(@0);
        make.height.equalTo(@(DetailBottomViewHeight));
    }];
    
    [self.view addSubview:self.tableView];
    self.tableView.backgroundColor = FMClearColor;
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.right.equalTo(@(0));
        make.bottom.equalTo(self.bottomView.mas_top);
    }];
    
    // 获取其他竞猜列表数据
    [self getMarketForecastList];
    
    [FMHelper addLoginAndLogoutNotificationWithObserver:self selector:@selector(handleLoginStatusNotification) monitorAuthLogin:NO];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    [self configNavWhiteColorWithCloseSEL:@selector(closeBtnClick)];

    if (self.needsRefresh) {
        self.needsRefresh = NO;
        [self updatePageData];
    }
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [self.forecastGuessView removeFromSuperview];
    [self.forecastRuleView removeFromSuperview];
    
    [self configNavRedColor];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    if (self.forecastGuessView) {
        [[FMAppDelegate shareApp].main.currentNav.view addSubview:self.forecastGuessView];
    }
    if (self.forecastRuleView) {
        [[FMAppDelegate shareApp].main.currentNav.view addSubview:self.forecastRuleView];
    }
}


#pragma mark - NSNotification
- (void)handleLoginStatusNotification {
    self.needsRefresh = YES;
}

- (void)closeBtnClick {
    [self.navigationController popViewControllerAnimated:YES];
}

// 配置评论
- (void)configPageTitle {
   
    [self.cellInnerScrollView removeFromSuperview];
    [self.pageTitleView removeFromSuperview];
    self.pageTitleView = nil;
    self.cellInnerScrollView = nil;
    SGPageTitleViewConfigure *configure = [SGPageTitleViewConfigure pageTitleViewConfigure];
    configure.titleColor = ColorWithHex(0x666666);
    configure.titleFont = FontWithSize(17.0);
    configure.titleSelectedColor = ColorWithHex(0x333333);
    configure.titleSelectedFont = BoldFontWithSize(17.0);
    configure.indicatorColor = FMNavColor;
    configure.indicatorStyle = SGIndicatorStyleFixed;
    configure.bottomSeparatorColor = FMSepLineColor;
    configure.indicatorFixedWidth = 18;
    configure.indicatorHeight = 3;
    configure.indicatorCornerRadius = 1.5;
    configure.titleAdditionalWidth = 30;
    configure.equivalence = YES; //是否均匀分布
    SGPageTitleView *pageTitleView = [SGPageTitleView pageTitleViewWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SegmentControl_Height) delegate:self titleNames:@[@"最新评论",@"精彩评论"] configure:configure];
    pageTitleView.backgroundColor = FMWhiteColor;
    [pageTitleView layerAndBezierPathWithRect:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SegmentControl_Height) cornerRadii:CGSizeMake(20, 20) byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight];
    self.pageTitleView = pageTitleView;
    [pageTitleView layerAndBezierPathWithRect:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SegmentControl_Height) cornerRadii:CGSizeMake(15, 15) byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight];
    SGPageContentCollectionView *cellInnerScrollView = [[SGPageContentCollectionView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT - UI_SAFEAREA_TOP_HEIGHT - UI_TABBAR_HEIGHT - UI_SAFEAREA_BOTTOM_HEIGHT - UI_SegmentControl_Height) parentVC:self childVCs:[self getChildVCs]];
    self.cellInnerScrollView = cellInnerScrollView;
    self.cellInnerScrollView.delegatePageContentCollectionView = self;
    [self.contentCell.contentView addSubview:self.cellInnerScrollView];
}

- (NSArray *)getChildVCs {
    for (UIViewController *subVc in self.childViewControllers) {
        [subVc.view removeFromSuperview];
        [subVc removeFromParentViewController];
    }
    NSArray *arr = @[@"最新评论",@"精彩评论"];
    for (NSInteger i=0; i < arr.count; i++) {
        ForcastCommentType type = 0;
        switch (i) {
            case 0:{
                type = ForcastCommentTypeRecent;
            }
                break;
            case 1: {
                type = ForcastCommentTypeNice;
            }
                break;
            default:
                break;
        }
        FMMarketForcastCommentViewController *vc = [FMMarketForcastCommentViewController instanceWithCommentType:type];
        vc.delegate = self;
        vc.forecastId = self.currentForecastId;
        WEAKSELF
        vc.listDataCount = ^(NSInteger count) {
            __weakSelf.bottomView.commentNum = count;
        };
        [vc view]; // 不使用懒加载了，提前进入viewDidLoad
        [self addChildViewController:vc];
        
        vc.commentReplyBlock = ^(FMCommentModel *commentModel) {
            if ([FMHelper checkLoginStatus]) {
                UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
                __weakSelf.commentView.frame = keyWindow.bounds;
                [keyWindow addSubview:__weakSelf.commentView];
                __weakSelf.commentView.publisLb.text = [NSString stringWithFormat:@"回复:%@", commentModel.commenterName];
                __weakSelf.commentView.publishBlock = ^(NSString *content) {
                    [HttpRequestTool marketForecastAddCommentLevel:@"2" parentId:commentModel.commentId content:content forecastId:self.currentForecastId start:^{
                        [SVProgressHUD show];
                    } failure:^{
                        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
                    } success:^(NSDictionary *dic) {
                        if ([dic[@"status"] isEqualToString:@"1"]) {
                            [SVProgressHUD showSuccessWithStatus:@"发布成功！审核通过后展示"];
                            __weakSelf.commentView.textView.text = @"";
                        } else {
                            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
                        }
                    }];
                };
            }
        };
        [self addChildViewController:vc];
    }
    return self.childViewControllers;
}

// 竞猜弹窗
- (void)showGuessViewWithType:(MarketForecastGuessType)type
                      upBonus:(NSInteger)upBonus
                    downBonus:(NSInteger)downBonus
                   minBetting:(int)minBetting
                   maxBetting:(int)maxBetting {
    WEAKSELF
    if (self.forecastGuessView) {
        [self.forecastGuessView removeFromSuperview];
        self.forecastGuessView = nil;
    }
    self.forecastGuessView = [MarketForecastGuessView showWithGuessType:type
                                                                upBonus:upBonus
                                                              downBonus:downBonus
                                                             minBetting:minBetting
                                                             maxBetting:maxBetting
                                                             closeBlock:^{
                                                                 __weakSelf.forecastGuessView = nil;
                                                             } guessBlock:^(NSInteger guessBonus) {
                                                                 [__weakSelf marketForecastWithType:type betting:guessBonus];
                                                             }];
    self.forecastGuessView.uptions = self.marketMode.uptions;
}

/// 设置头部视图
- (void)setupHeaderView {
    self.headerView.model = self.marketMode;
    [self getStockInfo];
    self.tableView.tableHeaderView = self.headerView;
}

- (void)updatePageData {
    [self getMarketForecastInformation:nil];
}

#pragma mark - request
/// 下拉刷新
- (void)refreshData {
    [self getMarketForecastList];
    if (self.childViewControllers.count > 0 ) {
        FMMarketForcastCommentViewController *vc = self.childViewControllers[self.pageTitleView.selectedIndex];
        WEAKSELF
        [vc refreshWithEndBlock:^ {
            [__weakSelf.tableView.mj_header endRefreshing];
        }];
        vc.listDataCount = ^(NSInteger count) {
            __weakSelf.bottomView.commentNum = count;
        };
    }
}

/// 大盘预测信息  model有值 表示是切换大盘  为空表示刷新当前大盘
- (void)getMarketForecastInformation:(MarketForecastModel *)model {
    WEAKSELF
    [HttpRequestTool getMarketForecastInformationWithforecastId:self.currentForecastId start:^{
    } failure:^{
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            if (model) {
                MarketForecastModel *currentModel = [__weakSelf.marketMode mutableCopy];
                [__weakSelf.headerView.listArr insertObject:currentModel atIndex:0];
                [__weakSelf.headerView.listArr removeObject:model];
                __weakSelf.headerView.listArr = [NSMutableArray arrayWithArray:__weakSelf.headerView.listArr];
            }
            __weakSelf.marketMode = [MarketForecastModel modelWithDictionary:dic[@"data"]];
            [__weakSelf setupHeaderView];
            [__weakSelf.tableView reloadData];
        }
    }];
}

/// 其他预测列表
- (void)getMarketForecastList {
//    [HttpRequestTool getMarketForecastListWithstart:^{
//    } failure:^{
//        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
//        [self getMarketForecastList];
//    } success:^(NSDictionary *dic) {
//        [SVProgressHUD dismiss];
//        if ([dic[@"status"] isEqualToString:@"1"]) {
//            [self.tableView.mj_header endRefreshing];
//            NSArray *arr = [NSArray modelArrayWithClass:[MarketForecastModel class] json:dic[@"data"]];
//            if (arr.count > 0) {
//                [self.tableView dismissNoDataView];
//                NSMutableArray *headerListArr = [NSMutableArray arrayWithArray:arr];
//                if (self.marketMode) {
//                    for (MarketForecastModel *model in arr) {
//                        if ([model.iD isEqualToString:self.marketMode.iD]) {
//                            self.marketMode = model;
//                            [headerListArr removeObject:model];
//                            break;
//                        }
//                    }
//                } else {
//                    self.marketMode = arr.firstObject;
//                    [headerListArr removeObjectAtIndex:0];
//                }
//                self.currentForecastId = self.marketMode.iD;
//                [self setupHeaderView];
//                self.headerView.listArr = headerListArr;
//                if (!self.pageTitleView) {
//                    // 不存在评论页 配置评论页
//                    [self configPageTitle];
//                } else {
//                    //存在评论页 刷新评论页数据
//                    [self refreshCommentSubVCData];
//                }
//                self.bottomView.hidden = NO;
//                self.view.backgroundColor = ColorWithHex(0xFFAE67);
//            } else {
//                self.headerView.listArr = [NSMutableArray array];
//                [self.tableView showNoDataViewWithImage:ImageWithName(@"common_nodata") string:@"暂无竞猜" attributes:nil offsetY:160];
//                self.bottomView.hidden = YES;
//                self.view.backgroundColor = FMWhiteColor;
//            }
//            [self.tableView reloadData];
//        } else {
//            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
//        }
//    }];
}

/// 刷新评论页数据
- (void)refreshCommentSubVCData {
    WEAKSELF
    for (id subVC in self.childViewControllers) {
        if ([subVC isKindOfClass:[FMMarketForcastCommentViewController class]]) {
            FMMarketForcastCommentViewController *vc = (FMMarketForcastCommentViewController *)subVC;
            vc.forecastId = __weakSelf.currentForecastId;
            [vc refreshWithEndBlock:^ {
            }];
            vc.listDataCount = ^(NSInteger count) {
                __weakSelf.bottomView.commentNum = count;
            };
        }
    }
}

/// 获取上证指数
- (void)getStockInfo {
    [self.monitor stopMonitorWithTag:FMQuizhqTag];
    UPMarketCodeMatchInfo *info = [FMUPDataTool matchInfoWithSetCodeAndCode:self.marketMode.stock];
    NSMutableArray *stocksM = [NSMutableArray array];
    UPHqStockUnique *stock1 = [UPHqStockUnique new];
    stock1.setCode = info.setCode;
    stock1.code = info.code;
    [stocksM addObject:stock1];
    UPMarketStockHqReq *req = [[UPMarketStockHqReq alloc] initWithStockArray:stocksM];
    req.simpleData = YES;
    WeakSelf(weakSelf);
    [self.monitor startMonitorStockHq:req tag:FMQuizhqTag completionHandler:^(UPMarketStockHqRsp *rsp, NSError *error) {
        if (!error) {
            if (rsp.dataArray.count > 0) {
                UPHqStockHq *hq = rsp.dataArray.firstObject;
                weakSelf.headerView.indexHq = hq;
            }
        }
    }];
}

// 看涨 || 看跌
- (void)marketForecastWithType:(NSInteger)type betting:(NSInteger)betting {
    WEAKSELF
    __weakSelf.forecastGuessView.allowBetting = NO;
    [SVProgressHUD show];
    [HttpRequestTool markerForecastWithforecastId:self.currentForecastId Type:type betting:betting start:^{
        
    } failure:^{
        __weakSelf.forecastGuessView.allowBetting = YES;
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        __weakSelf.forecastGuessView.allowBetting = YES;
        if ([dic[@"status"] isEqualToString:@"1"]) {
            //1、金币支付，更新本地的金币余额
            [HttpRequestTool queryDakaCoinsAndPointsStart:nil failure:nil success:nil];

            //2、关闭下注弹窗
            [__weakSelf.forecastGuessView viewClose];
            __weakSelf.forecastGuessView = nil;
            //3、手动刷新UI
            MarketForecastModel *model = __weakSelf.marketMode;
            model.myForecastStatus = 1;
            __weakSelf.marketMode = model;
            //4、提示下注成功
            [SVProgressHUD showSuccessWithStatus:dic[@"errmessage"]];
            
            [FMHelper delayTime:0.5f queue:dispatch_get_main_queue() action:^{
                //5、重新请求数据，刷新UI
                [__weakSelf getMarketForecastInformation:nil];
                //6、拉起评论
                [__weakSelf detailBottomViewCommentBtnDidClicked:__weakSelf.bottomView btn:nil];
            }];
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

#pragma mark - bottomviewDelegate
- (void)detailBottomViewCommentNumBtnDidClicked:(UIView *)bottomView btn:(UIButton *)commentNumBtn {
    CGFloat topCellOffset = ceil([self.tableView rectForSection:0].origin.y);
    [self.tableView setContentOffset:CGPointMake(0, topCellOffset) animated:YES];
}

#pragma mark - UIScrollView Delegate
-(void)scrollViewDidScroll:(UIScrollView *)scrollView{
    if (scrollView == self.tableView) {
        CGFloat topCellOffset = ceil([self.tableView rectForSection:0].origin.y);
        // 如果里层tableView的偏移量大于0，将外层tableView的偏移量定在tableTopViewHeight，保持悬停
        if (self.showingVC.tableView.contentOffset.y > 0 ||
            self.tableView.contentOffset.y >= topCellOffset) {
            self.tableView.contentOffset = CGPointMake(0, topCellOffset);
            [self.pageTitleView layerAndBezierPathWithRect:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SegmentControl_Height) cornerRadii:CGSizeMake(0, 0) byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight];
        }
        //如果外层tableView偏移量小于tableTopViewHeight（也就是头部视图正在显示），发出通知让每个子tableView的偏移量变成0
        CGFloat offSetY = self.tableView.contentOffset.y;
        if (offSetY < topCellOffset) {
            for (FMInnerTableViewController *VC in self.childViewControllers) {
                VC.tableView.contentOffset = CGPointZero;
            }
            [self.pageTitleView layerAndBezierPathWithRect:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SegmentControl_Height) cornerRadii:CGSizeMake(20, 20) byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight];
        }
    }
}


#pragma mark - innertabvcdelegate
- (void)innerTableVCTableviewScroll:(UITableView *)tableView {
    CGFloat tableTopViewHeight = ceil([self.tableView rectForSection:0].origin.y);
    // 如果外层tableView偏移量小于tableTopViewHeight（也就是头部视图正在显示），将子tableView的偏移量变成0
    if (self.tableView.contentOffset.y < tableTopViewHeight) {
        tableView.contentOffset = CGPointZero;
        tableView.showsVerticalScrollIndicator = NO;
    } else {
        tableView.showsVerticalScrollIndicator = YES;
    }
}


#pragma mark - FMDetailBottomView Delegate
- (void)detailBottomViewCommentBtnDidClicked:(UIView *)bottomView btn:(UIButton *)commentBtn {
    bottomView.userInteractionEnabled = NO;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        bottomView.userInteractionEnabled = YES;
    });

    if ([FMHelper checkLoginStatus]) {
        UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
        self.commentView.frame = keyWindow.bounds;
        [keyWindow addSubview:self.commentView];
        self.commentView.publisLb.text = @"发表评论";
        
        WEAKSELF;
        
        self.commentView.publishBlock = ^(NSString *content){
            [HttpRequestTool marketForecastAddCommentLevel:@"1" parentId:nil content:content forecastId:__weakSelf.currentForecastId start:^{
                [SVProgressHUD show];
            } failure:^{
                [SVProgressHUD showErrorWithStatus:@"网络不给力"];
            } success:^(NSDictionary *dic) {
                if ([dic[@"status"] isEqualToString:@"1"]) {
                    [SVProgressHUD showSuccessWithStatus:@"发布成功！审核通过后展示"];
                    __weakSelf.commentView.textView.text = @"";
                    
//                    FMCommentModel *model = [[FMCommentModel alloc] init];
//                    model.commentContent = content;
//                    model.praisedNum = 0;
//                    model.commenterIco = [FMUserDefault getUserFace];
//                    model.commenterName = [FMUserDefault getNickName];
//                    model.commentTime = [[NSDate date] timeIntervalSince1970] * 1000;
//                    model.commentId = [NSString stringWithFormat:@"%@", dic[@"data"]];
//                    model.isPraised = NO;
//                    model.forecastType = __weakSelf.marketMode.myForecast.forecastType;
//                    model.uptions = __weakSelf.marketMode.uptions;
//                    model.lastComments = @[];
//                    FMCommentFrameModel *frameModel = [[FMCommentFrameModel alloc] init];
//                    frameModel.commentModel = model;
//                    for (FMMarketForcastCommentViewController *subVC in __weakSelf.childViewControllers) {
//                        [subVC commentSuccssWithComentFrameModel:frameModel];
//                    }
//                    __weakSelf.bottomView.commentNum += 1;
                } else {
                    [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
                }
            }];
        };
    };
}

#pragma mark - tableViewDelegate
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    return self.contentCell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return UI_SCREEN_HEIGHT - UI_SegmentControl_Height  - UI_SAFEAREA_TOP_HEIGHT - UI_SAFEAREA_BOTTOM_HEIGHT;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return self.marketMode ? self.pageTitleView : [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return self.marketMode ? UI_SegmentControl_Height : CGFLOAT_MIN;
}

#pragma mark - SGPageTitleViewDelegate
- (void)pageTitleView:(SGPageTitleView *)pageTitleView selectedIndex:(NSInteger)selectedIndex{
    FMMarketForcastCommentViewController *subVC = self.childViewControllers[selectedIndex];
    _showingVC = subVC;
    [self.cellInnerScrollView setPageContentCollectionViewCurrentIndex:selectedIndex];
}

#pragma mark - SGPageContentCollectionViewDelegate
- (void)pageContentCollectionView:(SGPageContentCollectionView *)pageContentCollectionView progress:(CGFloat)progress originalIndex:(NSInteger)originalIndex targetIndex:(NSInteger)targetIndex {
    FMMarketForcastCommentViewController *subVC = self.childViewControllers[targetIndex];
    _showingVC = subVC;
    [self.pageTitleView setPageTitleViewWithProgress:progress originalIndex:originalIndex targetIndex:targetIndex];
}

- (FMOuterTableView *)tableView {
    if (!_tableView) {
        _tableView = [[FMOuterTableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain delegate:self dataSource:self viewController:self headerTarget:self headerAction:@selector(refreshData)];
    }
    return _tableView;
}

- (FMMarketForcastHeaderView *)headerView {
    if (!_headerView) {
        _headerView = [[FMMarketForcastHeaderView alloc] init];
        WEAKSELF;
        // 猜涨
        _headerView.middleView.guessUpBlock = ^{
            if ([FMHelper checkLoginStatus]) {
                [__weakSelf showGuessViewWithType:MarketForecastGuessTypeUp
                                          upBonus:__weakSelf.marketMode.bonusPool.upBonus
                                        downBonus:__weakSelf.marketMode.bonusPool.downBonus
                                       minBetting:__weakSelf.marketMode.configuration.minBetting
                                       maxBetting:__weakSelf.marketMode.configuration.maxBetting];
            }
        };
        // 猜跌
        _headerView.middleView.guessDownBlock = ^{
            if ([FMHelper checkLoginStatus]) {
                [__weakSelf showGuessViewWithType:MarketForecastGuessTypeDown
                                          upBonus:__weakSelf.marketMode.bonusPool.upBonus
                                        downBonus:__weakSelf.marketMode.bonusPool.downBonus
                                       minBetting:__weakSelf.marketMode.configuration.minBetting
                                       maxBetting:__weakSelf.marketMode.configuration.maxBetting];
            }
        };
        // 更新状态
        _headerView.middleView.updateStatusBlock = ^(MarketForecastModel *model) {
            [__weakSelf getMarketForecastInformation:nil];
        };
        // 查看规则
        _headerView.guesssRulesBlock = ^{
            if (__weakSelf.marketMode.configuration.rules.length > 0) {
                __weakSelf.forecastRuleView = [MarketForecastRuleView showWithRulesString:__weakSelf.marketMode.configuration.rules closeBlock:^{
                    __weakSelf.forecastRuleView = nil;
                }];
            }
        };
        // 切换当前展示大盘
        _headerView.otherInfo.listviewClick = ^(MarketForecastModel *model) {
            __weakSelf.currentForecastId = model.iD;
            // 切换时 停止行情请求
            [__weakSelf.monitor stopMonitorWithTag:FMQuizhqTag];
            [__weakSelf getMarketForecastInformation:model];
            // 更新评论数据
            [__weakSelf refreshCommentSubVCData];
        };
    }
    return _headerView;
}

- (UITableViewCell *)contentCell {
    if (_contentCell == nil) {
        _contentCell = [[UITableViewCell alloc] init];
        _contentCell.contentView.backgroundColor = FMWhiteColor;
        _contentCell.selectionStyle = UITableViewCellSelectionStyleNone;
    }
    return _contentCell;
}

- (UPMarketMonitor *)monitor {
    if (!_monitor) {
        _monitor = [[UPMarketMonitor alloc] init];
    }
    return _monitor;
}

- (FMDetailBottomView *)bottomView {
    if (!_bottomView) {
        _bottomView = [[FMDetailBottomView alloc] init];
        _bottomView.showType = FMDetailBottomShowTypeCommentNum;
        _bottomView.delegate = self;
        [_bottomView.commentBtn setTitle:@"分享您的见解..." forState:UIControlStateNormal];
    }
    return _bottomView;
}

- (FMCommentView *)commentView {
    if (!_commentView) {
        _commentView = [[FMCommentView alloc] init];
        _commentView.publisLb.text = @"发表评论";
        _commentView.placeholderText = @"分享您的见解...";
    }
    return _commentView;
}

@end
