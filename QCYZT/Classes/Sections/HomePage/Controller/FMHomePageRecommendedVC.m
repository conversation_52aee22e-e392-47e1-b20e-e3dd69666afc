//
//  FMHomePageRecommendedVC.m
//  QCYZT
//
//  Created by zeng on 2023/5/18.
//  Copyright © 2023 LZKJ. All rights reserved.
//

#import "FMHomePageRecommendedVC.h"
#import "FMHomePageTodayTopicTabCell.h"
#import "FMHomePageNewsTabCell.h"
#import "FMHomePageHotLiveTabCell.h"
#import "FMHomePageRecommendAskCodeVideoCell.h"
#import "FMHomePageRecommendAskCodeCell.h"
#import "FMNoteCell.h"
#import "FMHomeTopicModel.h"
#import "HttpRequestTool+Home.h"
#import "HttpRequestTool+Slice.h"
#import "HttpRequestTool+MarketForecast.h"
#import "MarketForecastModel.h"
#import "FMHomeMarketForcastCell.h"
#import "FMAllNoteListModel.h"
#import "FMHomePageCourseTabCell.h"
#import "FMHomePageRecommendSurveryCell.h"
#import "CourseDetailViewController.h"
#import "FMHomePageRecommendInsuranceCell.h"


@interface FMHomePageRecommendedVC ()

@property (nonatomic, assign) NSUInteger page;
@property (nonatomic, assign) NSUInteger currentPage;
@property (nonatomic, assign) NSUInteger pageSize;
@property (nonatomic, assign) BOOL isRequesting;

@property (nonatomic, strong) NSArray<NewsFlashModel *> *newsArr; // 快讯
@property (nonatomic, strong) NSArray<MarketForecastModel *> *marketForecastArr; // 大盘预测数据
@property (nonatomic, strong) NSArray<FMHomePageSurveryModel *> *surveryArr; // 调研数据
@property (nonatomic, strong) NSMutableArray *recommendedNotes;  // 笔记，中间会插入快讯、大盘预测和调研

@property (nonatomic, strong) NSMutableArray *dataArr; // 总数据，二维数组

@property (nonatomic, assign) NSInteger requestFlag; // 需要发起的请求数
@property (nonatomic, assign) NSInteger requestSuccessNum;  // 返回了成功结果的请求数
@property (nonatomic, assign) NSInteger requestFailNum;  // 返回了失败结果的请求数

// 行情数据相关
@property (nonatomic, strong) NSMutableArray<UPMarketCodeMatchInfo *> *stockArray;      // 所有相关股票数据
@property (nonatomic, strong) NSDictionary *stockhqCache;                        // stockHq缓存
@property (nonatomic, strong) UPMarketMonitor *monitor;                          // 请求数据定时器
@property (nonatomic, assign) BOOL isShowing;                                    // 当前页面是否正在显示


@end

static NSString *const HomePageRecommendedDataCache =  @"HomePageRecommendedDataCache"; // 缓存数据key，不含笔记列表
static NSString *const HomePageRecommendedNoteListCache =  @"HomePageRecommendedNoteListCache"; // 缓存的笔记列表key
static NSString *const HomePageRecommendedCacheTime =  @"HomePageRecommendedCacheTime"; // 缓存时间


@implementation FMHomePageRecommendedVC

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.view.backgroundColor = UIColor.up_contentBgColor;
    [self configTableView];
    
    [self loadCacheData];
    
    self.currentPage = self.page;
    self.pageSize = 20;
//    [self headerAction];  这里不请求，在父vc初始化时headerAction里会调用推荐控制器的headerAction
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(updateFocusStatus:) name:kFocusNumChanged object:nil];
    // 问股付费更新
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(questionPaySuccess:) name:kQuestionPaySuccess object:nil];
    // 问股点赞状态
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(askCodePraisedNotification:) name:kAskCodePriasedNotification object:nil];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    self.isShowing = YES;
    [self requestStockHq];
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    self.isShowing = NO;
    [self.monitor stopMonitorWithTag:FMNewsRelatedStockhqTag];
}

#pragma mark - UITableView  DataSoure
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.dataArr.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    NSArray *arr = self.dataArr[section];
    if (section != 3) {
        return arr.count ? 1 : 0;
    }
    return arr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    NSArray *arr = self.dataArr[indexPath.section];
    if (indexPath.section == 0) { // 直播
        FMHomePageHotLiveTabCell *cell = [tableView reuseCellClass:[FMHomePageHotLiveTabCell class]];
        cell.arr = arr;
        return cell;
    } else if (indexPath.section == 1) { // 保险
        if (arr.count) {
            FMHomePageRecommendInsuranceCell *cell = [tableView reuseCellClass:[FMHomePageRecommendInsuranceCell class]];
            NSDictionary *dic = arr.firstObject;
            cell.imageUrl = dic[@"image"];
            return cell;
        }
        return [UITableViewCell new];
    } else if (indexPath.section == 2) { // 话题
        NSArray *hotsOneItemArr = [arr firstObject];
        NSArray *hotsTwoItemArr = [arr lastObject];
        if ([hotsOneItemArr count] > 0 || hotsTwoItemArr.count > 0) {
            FMHomePageTodayTopicTabCell *cell = [tableView reuseCellClass:[FMHomePageTodayTopicTabCell class]];
            cell.dataArr = arr ;
            return cell;
        }
        return [UITableViewCell new];
    }
    
    if (indexPath.row < arr.count) {
        FMAllNoteListModel *model = arr[indexPath.row];
        // 快讯
        if ([model isKindOfClass:[NewsFlashModel class]]) {
            FMHomePageNewsTabCell *cell = [tableView reuseCellClass:[FMHomePageNewsTabCell class]];
            [cell configNewModel:(NewsFlashModel *)model hqDic:self.stockhqCache];
            WEAKSELF
            cell.reloadCellHeight = ^{
                [__weakSelf.tableView reloadData];
            };
            cell.showPhotoBrowser = ^{
                [__weakSelf.monitor stopMonitorWithTag:FMNewsRelatedStockhqTag];
            };
            cell.dismissPhotoBrowser = ^{
                [__weakSelf requestStockHq];
            };
            return cell;
        } 
        // 调研
        if ([model isKindOfClass:[FMHomePageSurveryModel class]]) {
            FMHomePageRecommendSurveryCell *cell = [tableView reuseCellClass:[FMHomePageRecommendSurveryCell class]];
            cell.model = (FMHomePageSurveryModel *)model;
            return cell;
        } 
        if ([model isKindOfClass:[MarketForecastModel class]]) {
            FMHomeMarketForcastCell *cell = [tableView reuseCellClass:[FMHomeMarketForcastCell class]];
            cell.model = (MarketForecastModel *)model;
            return cell;
        }
        
        if (model.noteApiDto) { // 笔记
            FMNoteCell *cell = [tableView reuseCellClass:[FMNoteCell class]];
            cell.model = model.noteApiDto;
            cell.isLastCell = (indexPath.row == self.recommendedNotes.count - 1);
            WEAKSELF
            cell.deleteBlock = ^{
                [__weakSelf.recommendedNotes removeObjectAtIndex:indexPath.row];
                [tableView reloadData];
            };
            return cell;
        } else if (model.questionInterfaceApiDto) { // 问股
            if (model.questionInterfaceApiDto.contentFileType.integerValue == 1) {
                // 视频问股
                FMHomePageRecommendAskCodeVideoCell *cell = [tableView reuseCellClass:[FMHomePageRecommendAskCodeVideoCell class]];
                cell.askModel = model.questionInterfaceApiDto;
                return cell;
            } else {
                // 语音问股
                FMHomePageRecommendAskCodeCell *cell = [tableView reuseCellClass:[FMHomePageRecommendAskCodeCell class]];
                cell.askModel = model.questionInterfaceApiDto;
                return cell;
            }
        } else if (model.courseSingleApiDto) {
            FMAllNoteListModel *model = arr[indexPath.row];
            // 课程
            FMHomePageCourseTabCell *cell = [tableView reuseCellClass:[FMHomePageCourseTabCell class]];
            cell.model = model.courseSingleApiDto;
//            cell.userIcon.userInteractionEnabled = NO;
//            cell.isLastCell = indexPath.row == arr.count - 1;
            return cell;
        }
    }
    
    return [UITableViewCell new];
}


- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    NSArray *arr = self.dataArr[indexPath.section];
    if (indexPath.section == 0) { // 直播
        if (arr.count == 1) {
            CGFloat width = UI_SCREEN_WIDTH - 30 - 55 - 15;
            return 9.0/16.0 * width + 13 + 13 + 24 + 15;
        } else if (arr.count > 1) {
            return 9.0/16.0 * 231.0 + 13 + 13 + 24 + 15;
        } else {
            return CGFLOAT_MIN;
        }
    } else if (indexPath.section == 1) { // 保险
        return arr.count ? 60 : CGFLOAT_MIN;
    } else if (indexPath.section == 2) { // 话题
        NSArray *oneItemArr = [arr firstObject];
        NSArray *twoItemArr = [arr lastObject];
        if (oneItemArr.count == 0 && twoItemArr.count == 0) return CGFLOAT_MIN;
        if (oneItemArr.count > 0 || twoItemArr.count > 0) {
            NSInteger num = oneItemArr.count;
            CGFloat cellHeight = 7 + 24 + 13 + 15 + 8;
            for (NSInteger i = 0; i < num; i ++) {
                FMHomeTopicModel *model = oneItemArr[i];
                if (model.profile.length == 0) {
                    cellHeight += 51;
                } else {
                    cellHeight += 78;
                }
            }
            cellHeight += 54 * (twoItemArr.count % 2 + twoItemArr.count / 2);
            return cellHeight;
        }
    }
    
    // 快讯
    if (indexPath.row < arr.count) {
        FMAllNoteListModel *model = arr[indexPath.row];
        if ([model isKindOfClass:[NewsFlashModel class]]) {
            return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMHomePageNewsTabCell class]) configuration:^(FMHomePageNewsTabCell *cell) {
                [cell configNewModel:(NewsFlashModel *)model hqDic:self.stockhqCache];
            }];
        }
        if ([model isKindOfClass:[FMHomePageSurveryModel class]]) {
            return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMHomePageRecommendSurveryCell class]) configuration:^(FMHomePageRecommendSurveryCell *cell) {
                cell.model = (FMHomePageSurveryModel *)model;
            }];
        }
        if([model isKindOfClass:[MarketForecastModel class]]) {
            return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMHomeMarketForcastCell class]) configuration:^(FMHomeMarketForcastCell *cell) {
                cell.model = (MarketForecastModel *)model;
            }];
        }
    }

    
    // 取缓存后的数据，fd计算的笔记高度不准，换成自动高度
    return UITableViewAutomaticDimension;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:NO];

    NSArray *arr = self.dataArr[indexPath.section];
    if(indexPath.section == 1) {
        if (arr.count) {
            NSDictionary *dic = arr.firstObject;
            [ProtocolJump jumpWithUrl:dic[@"action"]];
        }
    } else if (indexPath.section == 3) {
        FMAllNoteListModel *model = arr[indexPath.row];
        if ([model isKindOfClass:[NewsFlashModel class]]) {
            return;
        }
        if ([model isKindOfClass:[MarketForecastModel class]]) {
            [ProtocolJump jumpWithUrl:@"qcyzt://stockpredict"];
            return;
        }
        if ([model isKindOfClass:[FMHomePageSurveryModel class]]) {
            FMHomePageSurveryModel *tmpModel = (FMHomePageSurveryModel *)model;
            [ProtocolJump jumpWithUrl:tmpModel.action];
            return;
        }
        
        if (model.questionInterfaceApiDto) {
            [ProtocolJump jumpWithUrl:model.questionInterfaceApiDto.contentAction];
        } else if (model.noteApiDto) {
            [ProtocolJump jumpWithUrl:model.noteApiDto.contentAction];
        } else if (model.courseSingleApiDto) {
            CourseDetailViewController *detailVC = [[CourseDetailViewController alloc] init];
            FMAllNoteListModel *model = arr[indexPath.row];
            detailVC.courseId = model.courseSingleApiDto.courseId;
            [self.navigationController pushViewController:detailVC animated:YES];
        }
    }
}

#pragma mark - Private
- (void)configTableView {
    self.tableView.backgroundColor = UIColor.up_contentBgColor;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    [self.tableView registerCellClass:[FMHomePageHotLiveTabCell class]];
    [self.tableView registerCellClass:[FMHomePageTodayTopicTabCell class]];
    [self.tableView registerCellClass:[FMNoteCell class]];
    [self.tableView registerCellClass:[FMHomePageNewsTabCell class]];
    [self.tableView registerCellClass:[FMHomeMarketForcastCell class]];
    [self.tableView registerCellClass:[FMHomePageRecommendAskCodeCell class]];
    [self.tableView registerCellClass:[FMHomePageRecommendAskCodeVideoCell class]];
    [self.tableView registerCellClass:[FMHomePageRecommendSurveryCell class]];
    [self.tableView registerCellClass:[FMHomePageCourseTabCell class]];
    [self.tableView registerCellClass:[FMHomePageRecommendInsuranceCell class]];
    self.tableView.estimatedRowHeight = 300.0f;
    
    WEAKSELF;
    self.tableView.mj_footer = [MJRefreshAutoNormalFooter footerWithRefreshingBlock:^{
        [__weakSelf footerAction];
    }];
//    self.tableView.mj_footer.hidden = YES;
}

- (void)headerAction {
    if (self.isRequesting) {
        return;
    }
    
    self.page = 1;
    self.requestFlag = 6;
    self.requestSuccessNum = 0;
    self.requestFailNum = 0;
    
    [self requestData];
}

- (void)footerAction {
    if (self.isRequesting) {
        return;
    }
    
    self.page++;
    self.requestFlag = 1;
    self.requestSuccessNum = 0;
    self.requestFailNum = 0;
    
    [self requestData];
}

- (void)endRefreshForFailure {
    [self.tableView.mj_footer endRefreshing];
    [self.tableView.mj_header endRefreshing];
    self.page = self.currentPage;
}

- (void)endRefreshForSuccess {
    [self.tableView.mj_footer endRefreshing];
    [self.tableView.mj_header endRefreshing];
    self.currentPage = self.page;
}

- (void)loadCacheData {
    NSDate *dateNow = [NSDate date];
    NSDate *cacheDate = [FMUserDefault getUnArchiverDataForKey:HomePageRecommendedCacheTime];
    if ([dateNow timeIntervalSinceDate:cacheDate] < 60 * 60 * 24 * 7) {
        NSArray *cacheArr = [FMUserDefault getUnArchiverDataForKey:HomePageRecommendedDataCache];
        NSArray *noteListArr = [FMUserDefault getUnArchiverDataForKey:HomePageRecommendedNoteListCache];
        if ([self hasDataWithArr:cacheArr] && noteListArr.count) {
            [self.dataArr replaceObjectAtIndex:0 withObject:cacheArr[0]];
            [self.dataArr replaceObjectAtIndex:2 withObject:cacheArr[1]];
            [self.recommendedNotes removeAllObjects];
            [self dealNoteListDataWithArr:noteListArr];
            [self.tableView reloadData];
        }
    } else {
        [FMUserDefault setArchiverData:@[@[], @[@[], @[]]] forKey:HomePageRecommendedDataCache];
        [FMUserDefault setArchiverData:@[] forKey:HomePageRecommendedNoteListCache];
    }
}

#pragma mark - NSNotification
// 笔记点赞
- (void)praisedNotification:(NSNotification *)noti {
    NSDictionary *userInfo = noti.userInfo;
    NSString *noteId = userInfo[@"noteId"];
    for (NSInteger i = 0; i < self.recommendedNotes.count; i++) {
        FMAllNoteListModel *model = self.recommendedNotes[i];
        if ([model isKindOfClass:[FMAllNoteListModel class]] && model.noteApiDto.noteId.integerValue == [noteId integerValue]) {
            [[FMUserDataSyncManager sharedManager] likeNote:model.noteApiDto.noteId];
            model.noteApiDto.satisfiedNums = [NSString stringWithFormat:@"%ld",model.noteApiDto.satisfiedNums.integerValue + 1];
            [self.tableView reloadData];
            
            break;
        }
    }
}

- (void)updateFocusStatus:(NSNotification *)noti {
    [self.tableView reloadData];
}

// 问股点赞
- (void)askCodePraisedNotification:(NSNotification *)noti {
    NSDictionary *userInfo = noti.userInfo;
    NSString *questionId = userInfo[@"questionId"];
    for (NSInteger i = 0; i < self.recommendedNotes.count; i ++) {
        FMAllNoteListModel *model = self.recommendedNotes[i];
        if ([model isKindOfClass:[FMAllNoteListModel class]] && model.questionInterfaceApiDto) {
            if ([model.questionInterfaceApiDto.askCodeId isEqualToString:questionId]) {
                NSInteger satisfiedNums = model.questionInterfaceApiDto.virtualSatisfiedNums.integerValue;
                model.questionInterfaceApiDto.virtualSatisfiedNums = [NSString stringWithFormat:@"%ld",satisfiedNums + 1];
                [self.tableView reloadData];
            }
        }
    }
}

// 问股支付成功
- (void)questionPaySuccess:(NSNotification *)noti {
    NSDictionary *userInfo = noti.userInfo;
    NSString *questionId = userInfo[@"questionId"];
    for (NSInteger i = 0; i < self.recommendedNotes.count; i ++) {
        FMAllNoteListModel *model = self.recommendedNotes[i];
        if ([model isKindOfClass:[FMAllNoteListModel class]] && [model.questionInterfaceApiDto.askCodeId isEqualToString:questionId]) {
            model.questionInterfaceApiDto.questionPerm.type = @"3";
            [self.tableView reloadData];
        }
    }
}

#pragma mark - HTTP
- (void)requestData {
    self.isRequesting = YES;
    if (self.page == 1) {
        if (![self hasDataWithArr:self.dataArr]) {
            [self.view showActivityIndicatorView:ShowPositionTop];
        }
        // FMLog(@"首页请求开始 -- %zd", self.page);
        [self requestLiveList];
        [self requestTodayTopics];
        [self requestNews];
        [self requestNoteList];
        [self requestMarketForcast];
        [self requestSurvery];
    } else {
        // FMLog(@"首页请求开始 -- %zd", self.page);
        [self requestNoteList];
    }
}

// 直播
- (void)requestLiveList {
    [HttpRequestTool getHomeHotLiveroomListWithstart:^{
    } failure:^{
        self.requestFailNum += 1;
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            // FMLog(@"首页直播数据来了");
            NSArray *array = [NSArray modelArrayWithClass:[FMNoteLiveInfoModel class] json:dic[@"data"]];
            array = array ? array : @[];
            NSMutableArray *mutableArr = [NSMutableArray arrayWithArray:array];
            [mutableArr enumerateObjectsWithOptions:NSEnumerationReverse usingBlock:^(FMNoteLiveInfoModel  *obj, NSUInteger idx, BOOL * _Nonnull stop) {
                if ([obj isKindOfClass:[NSNull class]] ||
                    obj.liveId.length == 0) {
                    [mutableArr removeObject:obj];
                }
            }];
            [self.dataArr replaceObjectAtIndex:0 withObject:mutableArr];
            self.requestSuccessNum += 1;
        } else {
            self.requestFailNum += 1;
        }
    }];
}

// 话题
- (void)requestTodayTopics {
    [HttpRequestTool getCommunityHotTopicContentWithStart:^{
    } failure:^{
        self.requestFailNum += 1;
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            // FMLog(@"首页话题数据来了");
            NSDictionary * dict = [FMHelper processDictionaryIsNSNull:dic[@"data"]];
            NSArray *oneItemArr = [NSArray modelArrayWithClass:[FMHomeTopicModel class] json:dict[@"oneItem"]];
            NSArray *twoItemArr = [NSArray modelArrayWithClass:[FMHomeTopicModel class] json:dict[@"twoItem"]];
            NSMutableArray *mutableArr = [NSMutableArray arrayWithArray:self.dataArr[2]];
            [mutableArr replaceObjectAtIndex:0 withObject:oneItemArr];
            [mutableArr replaceObjectAtIndex:1 withObject:twoItemArr];
            [self.dataArr replaceObjectAtIndex:2 withObject:mutableArr];
            self.requestSuccessNum += 1;
        } else {
            self.requestFailNum += 1;
        }
    }];
}

// 新闻
- (void)requestNews {
    [HttpRequestTool getSliceListRequestWithPageType:0 signId:[NSNumber numberWithInteger:1] lastTime:0.0 pageSize:4 requestStart:^{
    } failure:^{
        self.requestFailNum += 1;
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            // FMLog(@"首页新闻数据来了");
            if ([dic[@"data"] isKindOfClass:[NSArray class]]) {
                self.newsArr = [NSArray modelArrayWithClass:[NewsFlashModel class] json:dic[@"data"]];
                [self configRelatedStocks];
            }
            self.requestSuccessNum += 1;
        } else {
            self.requestFailNum += 1;
        }
    }];
}

/// 大盘预测
- (void)requestMarketForcast {
    // FMLog(@"首页大盘预测数据来了");
    self.requestSuccessNum += 1;
}

/// 调研
- (void)requestSurvery {
    [HttpRequestTool requestHomepageSurveyWithStart:^{
    } failure:^{
        self.requestFailNum += 1;
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            // FMLog(@"首页调研数据来了");
            if ([dic[@"data"] isKindOfClass:[NSArray class]]) {
                self.surveryArr = [NSArray modelArrayWithClass:[FMHomePageSurveryModel class] json:dic[@"data"]];
            }
            self.requestSuccessNum += 1;
        } else {
            self.requestFailNum += 1;
        }
    }];
}

- (void)requestNoteList {
    [HttpRequestTool getCommunityChoiceContentWithPage:self.page pageSize:self.pageSize start:^{
        
    } failure:^{
        [self endRefreshForFailure];
        self.requestFailNum += 1;
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            // FMLog(@"首页笔记数据来了");
            [self endRefreshForSuccess];

            if (self.page == 1) {
                [self.tableView.mj_footer resetNoMoreData];
                [self.recommendedNotes removeAllObjects];
                [FMUserDefault setArchiverData:dic[@"data"] forKey:HomePageRecommendedNoteListCache];
            }
            NSInteger pageNum = (([dic[@"total"] integerValue] + self.pageSize - 1) / self.pageSize);
            if (self.page >= pageNum) {
                [self.tableView.mj_footer endRefreshingWithNoMoreData];
            }
            
            [self dealNoteListDataWithArr:dic[@"data"]];
            self.requestSuccessNum += 1;
        } else {
            [self endRefreshForFailure];
            self.requestFailNum += 1;
        }
    }];
}

- (void)dealNoteListDataWithArr:(NSArray *)arr {
    NSArray *dataArr = [NSArray modelArrayWithClass:[FMAllNoteListModel class] json:arr];
    NSMutableArray *mutableArr = [NSMutableArray arrayWithArray:dataArr];
    [mutableArr enumerateObjectsWithOptions:NSEnumerationReverse usingBlock:^(FMAllNoteListModel  *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj isKindOfClass:[NSNull class]]) {
            [mutableArr removeObject:obj];
        }
    }];
    [self removeRepeatDataWithArray:mutableArr];
}

- (void)removeRepeatDataWithArray:(NSArray *)array {
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    for (FMAllNoteListModel *model in array) {
        FMAllNoteListModel *noteModel = (FMAllNoteListModel *)model;
        if (noteModel.noteApiDto.noteId.length > 0) {
            noteModel.noteApiDto.rtShowType = [self judgeRTShowTypeWithNoteModel:noteModel];
            [dic setObject:noteModel.noteApiDto forKey:[NSString stringWithFormat:@"note_%@", noteModel.noteApiDto.noteId]];
        } else if (noteModel.questionInterfaceApiDto.askCodeId) {
            [dic setObject:noteModel.questionInterfaceApiDto forKey:[NSString stringWithFormat:@"ask_%@", noteModel.questionInterfaceApiDto.askCodeId]];
        } else if (noteModel.liveroomApiDto.liveId.length > 0) {
            noteModel.noteApiDto.rtShowType = [self judgeRTShowTypeWithNoteModel:noteModel];
            [dic setObject:noteModel.liveroomApiDto forKey:[NSString stringWithFormat:@"live_%@", noteModel.liveroomApiDto.liveId]];
        } else if (noteModel.courseSingleApiDto.courseId.length > 0) {
            [dic setObject:noteModel.courseSingleApiDto forKey:[NSString stringWithFormat:@"course_%@", noteModel.courseSingleApiDto.courseId]];
        }
    }
    
    for (FMAllNoteListModel *model in self.recommendedNotes.reverseObjectEnumerator) {
        if ([model isKindOfClass:[FMAllNoteListModel class]]) {
            if ([dic valueForKey:[NSString stringWithFormat:@"ask_%@", model.questionInterfaceApiDto.askCodeId]]) {
                [self.recommendedNotes removeObject:model];
            } else if ([dic valueForKey:[NSString stringWithFormat:@"ask_%@", model.questionInterfaceApiDto.askCodeId]]) {
                [self.recommendedNotes removeObject:model];
            } else if ([dic valueForKey:[NSString stringWithFormat:@"live_%@", model.liveroomApiDto.liveId]]) {
                [self.recommendedNotes removeObject:model];
            } else if ([dic valueForKey:[NSString stringWithFormat:@"course_%@", model.courseSingleApiDto.courseId]]) {
                [self.recommendedNotes removeObject:model];
            }
        }
    }
    
    [self.recommendedNotes addObjectsFromArray:array];
}

/// 判断笔记Cell右上角显示类型
- (NoteListCellRightTopShowType)judgeRTShowTypeWithNoteModel:(FMAllNoteListModel *)model {
    if (model.type == 2 || model.type == 1) { // 直播或课程
        if ([[FMUserDefault getUserId] integerValue] == model.noteApiDto.bignameDto.userId.integerValue) {
            return NoteListCellRightTopShowTypeNone;
        } else {
            return NoteListCellRightTopShowTypeFocusBtn;
        }
    } else { // 笔记
        if (model.noteApiDto.deleteFlag.integerValue < 0) { // 已删除
            if ([[FMUserDefault getUserId] integerValue] == model.noteApiDto.bignameDto.userId.integerValue) {
                if (model.noteApiDto.deleteFlag.integerValue == -1) {
                    return NoteListCellRightTopShowTypeMoreBtn;
                } else {
                    return NoteListCellRightTopShowTypeNone;
                }
            } else {
                return NoteListCellRightTopShowTypeNone;
            }
        } else {
            return NoteListCellRightTopShowTypeMoreBtn;
        }
    }
}

#pragma mark - 资讯相关股票行情数据相关
/// 处理相关股票数据，请求数据
- (void)requestStockHq {
    if (!self.isShowing) {
        return;
    }
    
//    // FMLog(@"想请求%zd条数据--%@", self.stockArray.count, [[self.stockArray valueForKeyPath:@"name"] componentsJoinedByString:@","]);
    if (self.stockArray.count) {
        // 将UPOptionalModel数组转换成UPHqStockUnique用于请求行情的数组
        NSMutableArray<UPHqStockUnique *> *stocksM = [NSMutableArray array];
        NSMutableDictionary *stockDic = self.stockhqCache.mutableCopy ?: [NSMutableDictionary new];
        [self.stockArray enumerateObjectsUsingBlock:^(UPMarketCodeMatchInfo *obj, NSUInteger idx, BOOL *_Nonnull stop) {
            UPHqStockUnique *stock = [UPHqStockUnique new];
            stock.setCode = obj.setCode;
            stock.code = obj.code;
            [stocksM addObject:stock];
            
            NSString *key = [FMUPDataTool jointWithSetCode:obj.setCode code:obj.code];
            if (!stockDic[key]) { // 可能存在拉不到行情的股票，先从从码表中获取基础信息
                UPMarketCodeMatchInfo *codeInfo = [UPMarketManager queryStockWithSetCode:stock.setCode code:stock.code];
                if (codeInfo) {
                    UPHqStockHq *stockHq = [UPHqStockHq new];
                    stockHq.setCode = codeInfo.setCode;
                    stockHq.code = codeInfo.code;
                    stockHq.name = codeInfo.name;
                    stockHq.category = codeInfo.category;
                    stockHq.origCategory = codeInfo.origCategory;
                    stockHq.precise = codeInfo.precise;
                    stockHq.tradeStatus = codeInfo.status;
                    stockDic[key] = stockHq;
                }
            }
        }];
        // 先触发一下set方法
        self.stockhqCache = stockDic.copy;
        
        if (UPTAFNetworkReachable && self.isShowing) { // 防止切到别的页面后收到通知或者滚动事件未停止触发请求
            UPMarketOptStockHqReq *hqReq = [[UPMarketOptStockHqReq alloc] initWithStockArray:stocksM.copy];
            hqReq.simpleData = YES;
            WeakSelf(weakSelf);

            [self.monitor startMonitorOptStockHq:hqReq tag:FMNewsRelatedStockhqTag completionHandler:^(UPMarketOptStockHqRsp *rsp, NSError *error) {
                NSArray *tmpArr = [rsp.dataArray valueForKeyPath:@"name"];
//                // FMLog(@"请求到%zd条数据--%@", rsp.dataArray.count, [tmpArr componentsJoinedByString:@","]);
                
                if (IsValidateArray(rsp.dataArray)) {
                    NSMutableDictionary *cacheDic = weakSelf.stockhqCache.mutableCopy;
                    [rsp.dataArray enumerateObjectsUsingBlock:^(UPHqStockHq *_Nonnull obj,
                                                                NSUInteger idx,
                                                                BOOL *_Nonnull stop) {
                        NSString *key = [FMUPDataTool jointWithSetCode:obj.setCode code:obj.code];
                        cacheDic[key] = obj;
                    }];
                    // 再次触发set方法
                    weakSelf.stockhqCache = cacheDic.copy;
                }
            }];
        }
    } else {
        [self.monitor stopMonitorWithTag:FMNewsRelatedStockhqTag];
    }
}


- (void)configRelatedStocks {
    [self.stockArray removeAllObjects];
    
    for (NewsFlashModel *model in self.newsArr) {
        if (model.upStockModels.count) {
            [self.stockArray addObjectsFromArray:model.upStockModels];
        }
    }
    [self requestStockHq];
}

#pragma mark - Getter/Setter
- (NSMutableArray *)dataArr {
    if (!_dataArr) {
        _dataArr = [NSMutableArray arrayWithArray:@[@[], @[], @[@[], @[]], self.recommendedNotes]]; // 第一个数组是热门直播，第二个数组是保险，第三个数组是推荐话题, 第四个数组是推荐笔记
    }
    
    return _dataArr;
}

- (NSMutableArray *)recommendedNotes {
    if (!_recommendedNotes) {
        _recommendedNotes = [NSMutableArray array];
    }
    
    return _recommendedNotes;
}

- (void)setRequestSuccessNum:(NSInteger)requestSuccessNum {
    _requestSuccessNum = requestSuccessNum;
    
    [self judgeRequestStatus];
}

- (void)setRequestFailNum:(NSInteger)requestFailNum {
    _requestFailNum = requestFailNum;
    
    [self judgeRequestStatus];
}

- (BOOL)hasDataWithArr:(NSArray *)array {
    BOOL hasData = NO;
    for (NSArray *arr in array) {
        if (arr.count) {
            hasData = YES;
            break;
        }
    }
    return hasData;
}

- (void)judgeRequestStatus {
    // FMLog(@"首页请求状态 -- 成功%zd 失败%zd", _requestSuccessNum, _requestFailNum);
    WEAKSELF
    if (_requestFailNum >= _requestFlag) { // 全部失败
        [self.view dismissIndicatorView];
        _requestFailNum = 0;
        _requestSuccessNum = 0;
        _isRequesting = NO;
        [self.view showReloadNetworkViewWithBlock:^{
            [__weakSelf headerAction];
        }];
        [self.tableView reloadData];
        
        // FMLog(@"首页所有接口失败");
    } else if (_requestFailNum + _requestSuccessNum >= _requestFlag) { // 全部返回
        [self.view dismissNoNetWorkView];
        [self.view dismissIndicatorView];
        
        // FMLog(@"首页所有接口成功");
        _requestFailNum = 0;
        _requestSuccessNum = 0;
        _isRequesting = NO;
        if ([self hasDataWithArr:self.dataArr]) {
            [self.tableView dismissNoDataView];
        } else {
            [self.tableView showNoDataViewWithImage:UPTImgInMarket2Module(@"个股/common_nodata") string:@"暂无内容" attributes:nil offsetY:120];
        }
        
        NSDictionary *initDic = [[NSUserDefaults standardUserDefaults] objectForKey:AppInit_Key];
        if ([initDic[@"insuranceIndex"] length]) {
            NSDictionary *dic = [JsonTool dicOrArrFromJsonString:initDic[@"insuranceIndex"]];
            [self.dataArr replaceObjectAtIndex:1 withObject:@[dic]];
        }
        
        self.tableView.mj_footer.hidden = !self.recommendedNotes.count;
        [self dealData];
        [self.tableView reloadData];
        
        [FMUserDefault setArchiverData:@[self.dataArr[0], self.dataArr[2]] forKey:HomePageRecommendedDataCache]; // 由于NSAttributedString里的图片无法归档，这里不缓存笔记列表数据
        [FMUserDefault setArchiverData:[NSDate date] forKey:HomePageRecommendedCacheTime];
    }
}

- (void)dealData {
    if (self.page == 1 && self.newsArr.count) {
        /// 插入快讯数据, 笔记第3,7,11,15条位置放置快讯
        NSArray *insertPositions = @[@2, @6, @10, @14];
        for (NSInteger i = 0; i < insertPositions.count; i++) {
            if (self.newsArr.count > i) {
                id insertObjcet = self.newsArr[i];
                NSInteger insertPosition = [insertPositions[i] integerValue];
                if (self.recommendedNotes.count > insertPosition) {
                    [self.recommendedNotes insertObject:insertObjcet atIndex:insertPosition];
                } else {
                    [self.recommendedNotes addObject:insertObjcet];
                }
            } else {
                break;
            }
        }
        
        // 插入调研和大盘预测
        NSMutableArray *arr = [NSMutableArray array];
        if (self.surveryArr.count) {
            [arr addObject:self.surveryArr.firstObject];
        }
        if (self.marketForecastArr.count) {
            [arr addObject:self.marketForecastArr.firstObject];
        }
        if (arr.count) {
            if (self.recommendedNotes.count > 2) {
                [self.recommendedNotes insertObjects:arr atIndexes:[NSIndexSet indexSetWithIndexesInRange:NSMakeRange(2, arr.count)]];
            } else {
                [self.recommendedNotes addObjectsFromArray:arr];
            }
        }
    }
    
    // FMLog(@"------------   首页处理数据结束 ---------------");
}

- (UPMarketMonitor *)monitor {
    if (!_monitor) {
        _monitor = [[UPMarketMonitor alloc] init];
    }
    
    return _monitor;
}

- (NSMutableArray<UPMarketCodeMatchInfo *> *)stockArray {
    if (!_stockArray) {
        _stockArray = [NSMutableArray array];
    }
    
    return _stockArray;
}

- (void)setStockhqCache:(NSDictionary *)stockhqCache {
    _stockhqCache = stockhqCache;
    
    [self.tableView reloadData];
}


@end
