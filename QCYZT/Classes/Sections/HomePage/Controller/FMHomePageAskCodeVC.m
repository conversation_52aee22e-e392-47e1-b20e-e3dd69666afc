//
//  FMHomePageAskCodeVC.m
//  QCYZT
//
//  Created by zeng on 2023/5/17.
//  Copyright © 2023 LZKJ. All rights reserved.
//

#import "FMHomePageAskCodeVC.h"
#import "FMAskCodeModel.h"
#import "FMHomePageAskCodeTopCell.h"
#import "FMAskCodeVideoListCell.h"
#import "FMAskCodeListCell.h"

@interface FMHomePageAskCodeVC ()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, assign) NSUInteger page;
@property (nonatomic, assign) NSUInteger currentPage;
@property (nonatomic, assign) NSUInteger pageSize;
@property (nonatomic, assign) NSInteger myQuestionOrAskMeNum; // 我的问股(问我的)数
@property (nonatomic, assign) NSInteger selfOptinalOrNotAnswerNum; // 自选问股(未回答)数
@property (nonatomic, strong) NSArray *hotQuestionArr; // 热门问股
@property (nonatomic, strong) NSMutableArray *commonQuestionArr; // 普通问股

@property (nonatomic, assign) NSInteger requestFlag; // 需要发起的请求数
@property (nonatomic, assign) NSInteger requestSuccessNum;  // 返回了成功结果的请求数
@property (nonatomic, assign) NSInteger requestFailNum;  // 返回了失败结果的请求数

@property (nonatomic, assign) BOOL needsRefresh;

@end

@implementation FMHomePageAskCodeVC

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.view.backgroundColor = UIColor.up_contentBgColor;
    [self configTableView];
    
    self.currentPage = self.page;
    self.pageSize = 20;
    [self headerAction];

    [FMHelper addLoginAndLogoutNotificationWithObserver:self  selector:@selector(handleLoginStatusNotification) monitorAuthLogin:YES];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(headerAction) name:kFMUpdateSelfStockDatabaseNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(questionPaySuccess:) name:kQuestionPaySuccess object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(praisedNotification:) name:kAskCodePriasedNotification object:nil];
}


- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    if (self.needsRefresh) {
        NSLog(@"首页问股准备刷新");
        self.needsRefresh = NO;
        [self headerAction];
    }
}

#pragma mark - UITableView  DataSoure
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.commonQuestionArr.count + 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == 0) {
        if (self.hotQuestionArr.count) {
            FMHomePageAskCodeTopCell *cell = [tableView reuseCellClass:[FMHomePageAskCodeTopCell class]];
            cell.myQuestionNum = self.myQuestionOrAskMeNum;
            cell.selfOptionalQuestionNum = self.selfOptinalOrNotAnswerNum;
            cell.recommendDatas = self.hotQuestionArr;
            return cell;
        } else {
            return [UITableViewCell new];
        }
    }
    
    FMAskCodeModel *model = self.commonQuestionArr[indexPath.section - 1];
    if (model.contentFileType.integerValue == 1) {
        // 视频问股
        FMAskCodeVideoListCell *cell = [tableView reuseCellClass:[FMAskCodeVideoListCell class]];
        cell.askModel = model;
        cell.isLastCell = YES;
        return cell;
    } else {
        // 语音问股
        FMAskCodeListCell *cell = [tableView reuseCellClass:[FMAskCodeListCell class]];
        cell.askModel = model;
        cell.isLastCell = YES;
        return cell;
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    if (indexPath.section == 0) {
        if (self.hotQuestionArr.count) {
            return 233.0f;
        } else {
            return 0;
        }
    }
    
    FMAskCodeModel *model = self.commonQuestionArr[indexPath.section - 1];
    if (model.contentFileType.integerValue == 1) { //视频问股
        return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMAskCodeVideoListCell class]) cacheByKey:model.askCodeId configuration:^(FMAskCodeVideoListCell *cell) {
            cell.askModel = model;
        }];
    } else { //语音问股
        return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMAskCodeListCell class]) cacheByKey:model.askCodeId configuration:^(FMAskCodeListCell *cell) {
            cell.askModel = model;
        }];
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
    
    if (indexPath.section == 0) {
        return;
    }
    FMAskCodeModel *model = self.commonQuestionArr[indexPath.section - 1];
    if ([model isKindOfClass:[FMAskCodeModel class]]) {
        [ProtocolJump jumpWithUrl:model.contentAction];
    }
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    UIView *footer = [UIView new];
    footer.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    return footer;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return 7.5;
}

#pragma mark - NSNotification
// 问股点赞
- (void)praisedNotification:(NSNotification *)noti {
    NSDictionary *userInfo = noti.userInfo;
    NSString *questionId = userInfo[@"questionId"];
    for (NSInteger i = 0; i < self.commonQuestionArr.count; i ++) {
        FMAskCodeModel *model = self.commonQuestionArr[i];
        if ([model.askCodeId isEqualToString:questionId]) {
            NSInteger satisfiedNums = model.virtualSatisfiedNums.integerValue;
            model.virtualSatisfiedNums = [NSString stringWithFormat:@"%ld",satisfiedNums + 1];
            [self.tableView reloadData];
        }
    }
}

// 问股支付成功
- (void)questionPaySuccess:(NSNotification *)noti {
    NSDictionary *userInfo = noti.userInfo;
    NSString *questionId = userInfo[@"questionId"];
    for (NSInteger i = 0; i < self.commonQuestionArr.count; i ++) {
        FMAskCodeModel *model = self.commonQuestionArr[i];
        if ([model.askCodeId isEqualToString:questionId]) {
            model.questionPerm.type = @"3";
            [self.tableView reloadData];
        }
    }
}

#pragma mark - NSNotification
- (void)handleLoginStatusNotification {
    self.needsRefresh = YES;
}

#pragma mark - Private
- (void)configTableView {
    self.tableView.backgroundColor = UIColor.up_contentBgColor;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    [self.tableView registerCellClass:[FMHomePageAskCodeTopCell class]];
    [self.tableView registerCellClass:[FMAskCodeListCell class]];
    [self.tableView registerCellClass:[FMAskCodeVideoListCell class]];
    
    WEAKSELF;
    self.tableView.mj_footer = [MJRefreshAutoNormalFooter footerWithRefreshingBlock:^{
        [__weakSelf footerAction];
    }];
    self.tableView.mj_footer.hidden = YES;
}

- (void)headerAction {
    self.page = 1;
    if ([FMHelper isLogined]) {
        self.requestFlag = 4;
    } else {
        self.requestFlag = 2;
    }
    self.requestSuccessNum = 0;
    self.requestFailNum = 0;
    
    [self requestData];
}

- (void)footerAction {
    self.page++;
    self.requestFlag = 1;
    self.requestSuccessNum = 0;
    self.requestFailNum = 0;
    
    [self requestData];
}

- (void)endRefreshForFailure {
    [self.tableView.mj_footer endRefreshing];
    [self.tableView.mj_header endRefreshing];
    self.page = self.currentPage;
}

- (void)endRefreshForSuccess {
    [self.tableView.mj_footer endRefreshing];
    [self.tableView.mj_header endRefreshing];
    self.currentPage = self.page;
}

#pragma mark - HTTP
- (void)requestData {
    if (self.page == 1) {
        if (![self hasData]) {
            [self.view showActivityIndicatorView:ShowPositionTop];
        }
        if ([FMHelper isLogined]) {
            if ([FMUserDefault getUserFlag]) {
                [self requestAskMeNum];
                [self requestWaitingAnswerNum];
            } else {
                [self requestMyQuestionNum];
                [self requestSelfOptinalQuestionNum];
            }
        } else {
            self.myQuestionOrAskMeNum = 0;
            self.selfOptinalOrNotAnswerNum = 0;
        }
        [self requestHotQuestion];
        [self requestCommonQuestion];
    } else {
        [self requestCommonQuestion];
    }
}

// 问我的
- (void)requestAskMeNum {
    self.myQuestionOrAskMeNum = 0;
    self.requestSuccessNum += 1;
    [HttpRequestTool getAskMeCountWithStart:^{
    } failure:^{
        self.requestFailNum += 1;
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            self.myQuestionOrAskMeNum = [[NSString stringWithFormat:@"%@", dic[@"data"]] integerValue];
            self.requestSuccessNum += 1;
        } else {
            self.requestFailNum += 1;
        }
    }];
}

// 待回复问股数
- (void)requestWaitingAnswerNum {
    [HttpRequestTool getNotAnswerCountWithStart:^{
    } failure:^{
        self.requestFailNum += 1;
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            self.selfOptinalOrNotAnswerNum = [[NSString stringWithFormat:@"%@", dic[@"data"]] integerValue];
            self.requestSuccessNum += 1;
        } else {
            self.requestFailNum += 1;
        }
    }];
}

// 我的提问数
- (void)requestMyQuestionNum {
    [HttpRequestTool getMyQuestionCountWithStart:^{
    } failure:^{
        self.requestFailNum += 1;
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            self.myQuestionOrAskMeNum = [[NSString stringWithFormat:@"%@", dic[@"data"]] integerValue];
            self.requestSuccessNum += 1;
        } else {
            self.requestFailNum += 1;
        }
    }];
}

// 自选股相关问股数
- (void)requestSelfOptinalQuestionNum {
    [HttpRequestTool getSelfStocksQuestionCountWithKeyWord:[FMUPDataTool selfStockDataFormaterCodeColonName] start:^{
    } failure:^{
        self.requestFailNum += 1;
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            self.selfOptinalOrNotAnswerNum = [[NSString stringWithFormat:@"%@", dic[@"data"]] integerValue];
            self.requestSuccessNum += 1;
        } else {
            self.requestFailNum += 1;
        }
    }];
}

// 热门问股
- (void)requestHotQuestion {
    [HttpRequestTool getAskCodeRecommonDataWithstart:nil failure:^{
        self.requestFailNum += 1;
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            self.hotQuestionArr = [NSArray modelArrayWithClass:[FMAskCodeModel class] json:dic[@"data"]];
            self.requestSuccessNum += 1;
        } else {
            self.requestFailNum += 1;
        }
    }];
}

- (void)requestCommonQuestion {
    [HttpRequestTool getAskCodeListWithPage:self.page pageSize:self.pageSize listType:2 keyWords:nil start:^{
    } failure:^{
        [self endRefreshForFailure];
        self.requestFailNum += 1;
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [self endRefreshForSuccess];

            if (self.page == 1) {
                [self.tableView.mj_footer resetNoMoreData];
                [self.commonQuestionArr removeAllObjects];
            }
            NSInteger pageNum = (([dic[@"total"] integerValue] + self.pageSize - 1) / self.pageSize);
            if (self.page >= pageNum) {
                [self.tableView.mj_footer endRefreshingWithNoMoreData];
            }
            
            NSArray *arr = [NSArray modelArrayWithClass:[FMAskCodeModel class] json:dic[@"data"]];
            [self removeRepeatDataWithArray:arr];
            self.requestSuccessNum += 1;
        } else {
            [self endRefreshForFailure];
            self.requestFailNum += 1;
        }
    }];
}


- (void)removeRepeatDataWithArray:(NSArray *)array {
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    for (FMAskCodeModel *model in array) {
        [dic setObject:model forKey:model.askCodeId];
    }
    
    for (FMAskCodeModel *model in self.commonQuestionArr.reverseObjectEnumerator) {
        if ([dic valueForKey:model.askCodeId]) {
            [self.commonQuestionArr removeObject:model];
        }
    }
    
    [self.commonQuestionArr addObjectsFromArray:array];
}


#pragma mark - Getter/Setter
- (NSMutableArray *)commonQuestionArr {
    if (!_commonQuestionArr) {
        _commonQuestionArr = [NSMutableArray array];
    }
    return _commonQuestionArr;
}

- (void)setRequestSuccessNum:(NSInteger)requestSuccessNum {
    _requestSuccessNum = requestSuccessNum;
    
    [self judgeRequestStatus];
}

- (void)setRequestFailNum:(NSInteger)requestFailNum {
    _requestFailNum = requestFailNum;
    
    [self judgeRequestStatus];
}

- (BOOL)hasData {
    return self.commonQuestionArr.count + self.hotQuestionArr.count;
}

- (void)judgeRequestStatus {
    WEAKSELF
    if (_requestFailNum == _requestFlag) { // 全部失败
        [self.view dismissIndicatorView];
        _requestFailNum = 0;
        _requestSuccessNum = 0;
        [self.view showReloadNetworkViewWithBlock:^{
            [__weakSelf headerAction];
        }];
        [self.tableView reloadData];
    } else if (_requestFailNum + _requestSuccessNum == _requestFlag) { // 全部返回
        [self.view dismissNoNetWorkView];
        [self.view dismissIndicatorView];
        
        _requestFailNum = 0;
        _requestSuccessNum = 0;
        if (self.hasData) {
            [self.tableView dismissNoDataView];
        } else {
            [self.tableView showNoDataViewWithImage:UPTImgInMarket2Module(@"个股/common_nodata") string:@"暂无内容" attributes:nil offsetY:120];
        }
        self.tableView.mj_footer.hidden = !self.commonQuestionArr.count;
        [self.tableView reloadData];
    }
}

@end
