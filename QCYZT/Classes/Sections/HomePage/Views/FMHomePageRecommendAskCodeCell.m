//
//  FMHomePageRecommendAskCodeCell.m
//  QCYZT
//
//  Created by zeng on 2021/12/5.
//  Copyright © 2021 LZKJ. All rights reserved.
//

#import "FMHomePageRecommendAskCodeCell.h"
#import "UIImage+stocking.h"
#import "FMAskCodeModel.h"
@interface FMHomePageRecommendAskCodeCell()
/// 投顾信息UI
@property (nonatomic, strong) DakaInfoNewView *infoView;
/// 标题
@property (nonatomic, strong) UILabel *titleLabel;
/// 语音
@property (nonatomic, strong) UIImageView *voiceImgV;
/// 收听价格
@property (nonatomic, strong) UILabel *listionPriceLabel;
/// 时长
@property (nonatomic, strong) UILabel *durationLabel;
/// 收听人数
@property (nonatomic, strong) UILabel *watchListenLabel;
/// 点赞按钮
@property (nonatomic, strong) UIButton *praiseBtn;
/// 底部分割线
@property (nonatomic, strong) UIView *bottomLine;
/// 关注按钮
@property (nonatomic, strong) FocusButton *focusBtn;

@end

@implementation FMHomePageRecommendAskCodeCell

- (id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setUp];
    }
    return  self;
}

- (void)setUp {
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    self.backgroundColor = self.contentView.backgroundColor = UIColor.up_contentBgColor;
    // 投顾UI
    DakaInfoNewView *infoView = [[DakaInfoNewView alloc] init];
    [self.contentView addSubview:infoView];
    infoView.tagView.hidden = YES;
    [infoView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.top.equalTo(@15);
        if([FMHelper isBigFont]) {
            make.height.equalTo(@50);
            make.right.mas_equalTo(self.contentView.mas_right).offset(-5-15-75);
        } else {
            make.height.equalTo(@40);
            make.right.mas_equalTo(self.contentView.mas_right).offset(-5-15-65);
        }
    }];
    self.infoView = infoView;

    
    // 关注按钮
    FocusButton *focusBtn = [FocusButton buttonWithType:UIButtonTypeCustom];
    CGSize focuseBtnSize = [FMHelper isBigFont] ? CGSizeMake(75, 30) : CGSizeMake(65, 30);
    focusBtn.size = focuseBtnSize;
    focusBtn.text = @"关注";
    focusBtn.focusText = @"已关注";
    focusBtn.textColor = FMNavColor;
    focusBtn.focusTextColor = UIColor.fm_stock_calendar_textDisabledColor;
    focusBtn.image = [UIImage imageWithTintColor:FMNavColor blendMode:kCGBlendModeDestinationIn WithImageObject:ImageWithName(@"add_focus")];
    focusBtn.focusImage = ImageWithName(@"");
    focusBtn.boardColor = FMNavColor;
    focusBtn.focusBoardColor = UIColor.fm_stock_calendar_textDisabledColor;
    focusBtn.backColor = UIColor.up_contentBgColor;
    focusBtn.focusBackColor = UIColor.up_contentBgColor;
    focusBtn.titleLabel.font = [FMHelper scaleFont:14];
    WEAKSELF
    focusBtn.compeletionClock = ^(NSString * _Nonnull dakaId, BOOL isfocus) {
        if ([__weakSelf.askModel.bignameDto.userId isEqualToString:dakaId]) {
            __weakSelf.askModel = __weakSelf.askModel;
        }
    };
    [self.contentView addSubview:focusBtn];
    
    [focusBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.mas_right).offset(-15);
        make.centerY.equalTo(infoView);
        make.size.equalTo(@(focuseBtnSize));
    }];
    self.focusBtn = focusBtn;

    // 标题
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:16] textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:0 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:titleLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.right.equalTo(@-15);
        make.top.equalTo(infoView.mas_bottom).offset(15);
    }];
    self.titleLabel = titleLabel;
    
    
    // 语音image
    UIImageView *voiceImgV = [[UIImageView alloc] init];
    [self.contentView addSubview:voiceImgV];
    [voiceImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(titleLabel.mas_bottom).offset(14);
        make.left.equalTo(infoView);
        make.width.equalTo(@(UI_Relative_WidthValue(195)));
        make.height.equalTo(@(UI_Relative_WidthValue(35)));
    }];
    self.voiceImgV = voiceImgV;
    
    UIView *whiteView = [UIView new];
    whiteView.backgroundColor = FMWhiteColor;
    [self.contentView insertSubview:whiteView belowSubview:voiceImgV];
    [whiteView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(voiceImgV.mas_left).offset(15);
        make.width.equalTo(40);
        make.top.bottom.equalTo(voiceImgV);
    }];
    
    
    // 收听价格
    UILabel *listionPriceLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:13] textColor:FMWhiteColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [voiceImgV addSubview:listionPriceLabel];
    [listionPriceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@(UI_Relative_WidthValue(40)));
        make.centerY.equalTo(@0);
    }];
    self.listionPriceLabel = listionPriceLabel;
    
    // 语音时长
    UILabel *durationLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:13] textColor:FMWhiteColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentRight];
    [voiceImgV addSubview:durationLabel];
    [durationLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@-10);
        make.centerY.equalTo(@0);
    }];
    self.durationLabel = durationLabel;
    
    //  手势view  拦截点击事件
    UIView *gesView = [[UIView alloc] init];
    [self.contentView addSubview:gesView];
    [gesView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(titleLabel);
        make.top.equalTo(voiceImgV.mas_bottom).offset(15);
        make.height.equalTo(@18);
        make.bottom.equalTo(@-15);
    }];
    gesView.userInteractionEnabled = YES;
    [gesView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithActionBlock:^(id  _Nonnull sender) {
        // 屏蔽点击跳转到详情
    }]];
    
    // 点赞按钮
    UIButton *praiseBtn = [[UIButton alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:12] normalTextColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor title:@"点赞" image:FMImgInBundle(@"笔记/点赞数") target:self action:@selector(praiseBtnClicked)];
    [gesView addSubview:praiseBtn];
    [praiseBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(@0);
        make.centerY.equalTo(@0);
    }];
    [praiseBtn setTitleColor:FMNavColor forState:UIControlStateDisabled];
    [praiseBtn setImage:ImageWithName(@"new_praised") forState:UIControlStateDisabled];
    praiseBtn.titleLabel.lineBreakMode = NSLineBreakByClipping;
    self.praiseBtn = praiseBtn;
    
    // 收听人数
    UILabel *watchListenLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:12] textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:watchListenLabel];
    [watchListenLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(gesView);
        make.left.equalTo(titleLabel);
    }];
    self.watchListenLabel = watchListenLabel;

    // 底部分割线
    self.bottomLine = [self.contentView addSepLineWithBlock:^(MASConstraintMaker * _Nonnull make) {
        make.left.right.bottom.equalTo(@0);
        make.height.equalTo(@7.5);
    }];
    self.bottomLine.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
}

- (void)setIsLastCell:(BOOL)isLastCell {
    _isLastCell = isLastCell;
    self.bottomLine.hidden = isLastCell;
}

- (void)setAskModel:(FMAskCodeModel *)askModel {
    _askModel = askModel;
    self.focusBtn.dakaId = askModel.bignameDto.userId;
    self.focusBtn.isFocus = [[FMUserDataSyncManager sharedManager] isDakaNoticed:askModel.bignameDto.userId];
    [self configWatchCell:askModel];
}

- (void)configWatchCell:(FMAskCodeModel *)model {
    NSMutableAttributedString *titleAttrStr = [[NSMutableAttributedString alloc] initWithString:model.questionContent];
    NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
    style.lineSpacing = 5.0f;
    [titleAttrStr addAttributes:@{NSParagraphStyleAttributeName : style} range:NSMakeRange(0, titleAttrStr.length)];
    self.titleLabel.attributedText = titleAttrStr;
    
    [self.infoView.iconImgV.iconImg sd_setImageWithURL:[NSURL URLWithString:model.bignameDto.userIco] placeholderImage:ImageWithName(@"userCenter_dltx")];
    self.infoView.nameLabel.text = model.bignameDto.userName;
    self.infoView.userId = model.bignameDto.userId;
    self.infoView.islive = model.bignameDto.isLive;
    /**
      2022/3/6 语音问股 视频问股 不显示投顾风格
     */
    self.infoView.tagView.hidden = YES;
    self.infoView.attestationType = model.bignameDto.attestationType;

    
    NSDate *nowDate = [NSDate dateWithTimeIntervalSince1970:model.answerTime/1000];
    NSString *timeStr = nil;
    if ([nowDate isToday]) {
        timeStr = [NSString stringFromDate:nowDate format:@"HH:mm 回答"];
    } else {
        if ([nowDate isThisYear]) {
            timeStr = [NSString stringFromDate:nowDate format:@"MM-dd HH:mm 回答"];
        } else {
            timeStr = [NSString stringFromDate:nowDate format:@"yyyy-MM-dd 回答"];
        }
    }
    self.infoView.timeLabel.text = timeStr;
    
    if (model.virtualListenerNums.integerValue > 0) {
        NSString *readNumStr = nil;
        if (model.virtualListenerNums.integerValue < 10000) {
            readNumStr = [NSString stringWithFormat:@"%@人听过",model.virtualListenerNums];
        } else {
            double wan = model.virtualListenerNums.integerValue / 10000.0;
            readNumStr = [NSString stringWithFormat:@"%.1f万人听过", wan];
        }
        self.watchListenLabel.text = readNumStr;
    } else {
        self.watchListenLabel.text = @"";
    }
    
    if (model.virtualSatisfiedNums.integerValue > 0) {
        NSString *praiseNumStr = nil;
        if (model.virtualSatisfiedNums.integerValue < 10000) {
            praiseNumStr = [NSString stringWithFormat:@"%@", model.virtualSatisfiedNums];
        } else {
            double wan = model.virtualSatisfiedNums.integerValue / 10000.0;
            praiseNumStr = [NSString stringWithFormat:@"%.1f万", wan];
        }
        [self.praiseBtn setTitle:praiseNumStr forState:UIControlStateNormal];
    } else {
        [self.praiseBtn setTitle:@"点赞" forState:UIControlStateNormal];
    }
    [self.praiseBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageLeft imageTitleSpacing:5];
    self.praiseBtn.enabled = ![[FMUserDataSyncManager sharedManager] isQuestionLiked:model.askCodeId];
    
    self.listionPriceLabel.text = @"";
    if ([model.listenPriceStr floatValue] == 0) {
        if (model.questionPerm.type.intValue == 5) {
            self.voiceImgV.image = [UIImage imageWithTintColor:ColorWithHex(0x23BF91) blendMode:kCGBlendModeDestinationIn WithImageObject:ImageWithName(@"askcode_list_blueBg")];
            self.listionPriceLabel.text = @"已回答";
        } else {
            self.voiceImgV.image = ImageWithName(@"askcode_list_blueBg");
        }
    } else {
        if (model.questionPerm.type.integerValue == 1) { // 没有权限
            self.voiceImgV.image = ImageWithName(@"askcode_list_blueBg");
            self.listionPriceLabel.text = [NSString stringWithFormat:@"%.0f金币付费偷听",  [model.listenPriceStr floatValue]];
        } else if (model.questionPerm.type.intValue == 2) { // 免费看
            self.voiceImgV.image = ImageWithName(@"askcode_list_blueBg");
        } else if (model.questionPerm.type.intValue == 3) { // 已看过
            self.voiceImgV.image = [UIImage imageWithTintColor:ColorWithHex(0xF85943) blendMode:kCGBlendModeDestinationIn WithImageObject:ImageWithName(@"askcode_list_blueBg")];
            self.listionPriceLabel.text = @"已付费";
        }  else if (model.questionPerm.type.intValue == 4) { // 我问的
            self.voiceImgV.image = [UIImage imageWithTintColor:ColorWithHex(0xF85943) blendMode:kCGBlendModeDestinationIn WithImageObject:ImageWithName(@"askcode_list_blueBg")];
            self.listionPriceLabel.text = @"已付费";
        } else if (model.questionPerm.type.intValue == 5) { // 已回答
            self.voiceImgV.image = [UIImage imageWithTintColor:ColorWithHex(0x23BF91) blendMode:kCGBlendModeDestinationIn WithImageObject:ImageWithName(@"askcode_list_blueBg")];
            self.listionPriceLabel.text = @"已回答";
        }
    }
    
    self.durationLabel.text = [NSString stringWithFormat:@"%@\"", model.questionAnswerLength];
}

- (void)praiseBtnClicked {
    NSString *questionId = self.askModel.askCodeId;
    if ([FMHelper checkLoginStatus]) {
        [HttpRequestTool questionPraiseWithQuestionId:questionId start:^{
            self.praiseBtn.userInteractionEnabled = NO;
        } failure:^{
            self.praiseBtn.userInteractionEnabled = YES;
            [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        } success:^(NSDictionary *dic) {
            self.praiseBtn.userInteractionEnabled = YES;
            if ([dic[@"status"] isEqualToString:@"1"]) {                
                [[FMUserDataSyncManager sharedManager] likeQuestion:questionId];
                self.askModel.virtualSatisfiedNums = [NSString stringWithFormat:@"%ld",self.askModel.virtualSatisfiedNums.integerValue  + 1];
                
                NSString *praiseNumStr = nil;
                if (self.askModel.virtualSatisfiedNums.integerValue < 10000) {
                    praiseNumStr = [NSString stringWithFormat:@"%@", self.askModel.virtualSatisfiedNums];
                } else {
                    double wan = self.askModel.virtualSatisfiedNums.integerValue / 10000.0;
                    praiseNumStr = [NSString stringWithFormat:@"%.1f万", wan];
                }
                
                [self.praiseBtn setTitle:praiseNumStr forState:UIControlStateNormal];
                [self.praiseBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageLeft imageTitleSpacing:5.0f];
                
                self.praiseBtn.enabled = NO;
            } else {
                [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            }
        }];
    }
}

@end
