//
//  FMHomePageRecommendAskCodeVideoCell.m
//  QCYZT
//
//  Created by zeng on 2021/12/5.
//  Copyright © 2021 LZKJ. All rights reserved.
//

#import "FMHomePageRecommendAskCodeVideoCell.h"
#import "FMAskCodeModel.h"
@interface FMHomePageRecommendAskCodeVideoCell()

@property (nonatomic, strong) DakaInfoNewView *infoView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) ZLTagLabel *timeLB;

@property (nonatomic, strong) UILabel *watchListenLabel;
@property (nonatomic, strong) UIButton *praiseBtn;
@property (nonatomic, strong) UIView *bottomLine;
@property (nonatomic, strong) FocusButton *focusBtn;

@end

@implementation FMHomePageRecommendAskCodeVideoCell

- (id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setUp];
    }
    return  self;
}

- (void)setUp {
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    self.backgroundColor = self.contentView.backgroundColor = UIColor.up_contentBgColor;

    DakaInfoNewView *infoView = [[DakaInfoNewView alloc] init];
    [self.contentView addSubview:infoView];

    self.infoView = infoView;
    
    
    FocusButton *focusBtn = [FocusButton buttonWithType:UIButtonTypeCustom];
    CGSize focuseBtnSize = [FMHelper isBigFont] ? CGSizeMake(75, 30) : CGSizeMake(65, 30);
    focusBtn.size = focuseBtnSize;
    focusBtn.text = @"关注";
    focusBtn.focusText = @"已关注";
    focusBtn.textColor = FMNavColor;
    focusBtn.focusTextColor = UIColor.fm_stock_calendar_textDisabledColor;
    focusBtn.image = [UIImage imageWithTintColor:FMNavColor blendMode:kCGBlendModeDestinationIn WithImageObject:ImageWithName(@"add_focus")];
    focusBtn.focusImage = ImageWithName(@"");
    focusBtn.boardColor = FMNavColor;
    focusBtn.focusBoardColor = UIColor.fm_stock_calendar_textDisabledColor;
    focusBtn.backColor = UIColor.up_contentBgColor;
    focusBtn.focusBackColor = UIColor.up_contentBgColor;
    focusBtn.titleLabel.font = [FMHelper scaleFont:14];
    WEAKSELF
    focusBtn.compeletionClock = ^(NSString * _Nonnull dakaId, BOOL isfocus) {
        if ([__weakSelf.askModel.bignameDto.userId isEqualToString:dakaId]) {
            __weakSelf.askModel = __weakSelf.askModel;
        }
    };
    [self.contentView addSubview:focusBtn];
    [focusBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.mas_right).offset(-15);
        make.centerY.equalTo(infoView);
        make.size.equalTo(@(focuseBtnSize));
    }];
    [infoView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.top.equalTo(@15);
        make.height.equalTo([FMHelper isBigFont] ? 50 : 40);
        make.right.mas_equalTo(focusBtn.mas_left).offset(-5);
    }];
    self.focusBtn = focusBtn;

    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:16] textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:0 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:titleLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.right.equalTo(@-15);
        make.top.equalTo(infoView.mas_bottom).offset(15);
    }];
    self.titleLabel = titleLabel;
        
    UIImageView *videoImg = [[UIImageView alloc] init];
    videoImg.userInteractionEnabled = YES;
    [self.contentView addSubview:videoImg];
    [videoImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(titleLabel);
        make.top.equalTo(titleLabel.mas_bottom).offset(14);
        make.height.equalTo(@((UI_SCREEN_WIDTH - 30) * 194.0 / 345));
    }];
    videoImg.contentMode = UIViewContentModeScaleAspectFill;
    videoImg.clipsToBounds = YES;
    self.videoImg = videoImg;
    
    UIImageView *playImg = [[UIImageView alloc] init];
    playImg.image = [UIImage imageNamed:@"askcode_list_video"];
    [videoImg addSubview:playImg];
    [playImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.centerY.equalTo(videoImg);
        make.size.equalTo(@(CGSizeMake(45, 45)));
    }];
    
    ZLTagLabel *timeLB = [[ZLTagLabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:12] textColor:FMWhiteColor backgroundColor:ColorWithHexAlpha(0x000000, 0.6) numberOfLines:1];
    timeLB.textAlignment = NSTextAlignmentCenter;
    timeLB.widthPadding = 12.0;
    UI_View_Radius(timeLB, 10);
    [videoImg addSubview:timeLB];
    [timeLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@(-10));
        make.bottom.equalTo(@(-10));
        make.height.equalTo(@(20));
    }];
    self.timeLB = timeLB;
    
    UIView *gesView = [[UIView alloc] init];
    [self.contentView addSubview:gesView];
    [gesView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(titleLabel);
        make.top.equalTo(videoImg.mas_bottom).offset(15);
        make.height.equalTo(@18);
        make.bottom.equalTo(@-15);
    }];
    gesView.userInteractionEnabled = YES;
    [gesView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithActionBlock:^(id  _Nonnull sender) {
        // 屏蔽点击跳转到详情
    }]];
    
    UIButton *praiseBtn = [[UIButton alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:12] normalTextColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor title:@"点赞" image:FMImgInBundle(@"笔记/点赞数") target:self action:@selector(praiseBtnClicked)];
    [gesView addSubview:praiseBtn];
    [praiseBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(@0);
        make.centerY.equalTo(@0);
    }];
    [praiseBtn setTitleColor:FMNavColor forState:UIControlStateDisabled];
    [praiseBtn setImage:ImageWithName(@"new_praised") forState:UIControlStateDisabled];
    self.praiseBtn = praiseBtn;
    
    UILabel *watchListenLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:12] textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:watchListenLabel];
    [watchListenLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(gesView);
        make.left.equalTo(titleLabel);
    }];
    self.watchListenLabel = watchListenLabel;
    
    self.bottomLine = [self.contentView addSepLineWithBlock:^(MASConstraintMaker * _Nonnull make) {
        make.left.right.equalTo(@0);
        make.bottom.equalTo(@0);
        make.height.equalTo(@7.5);
    }];
    self.bottomLine.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
}

- (void)praiseBtnClicked {
    if ([FMHelper checkLoginStatus]) {
        NSString *contentid = self.askModel.askCodeId;
        [HttpRequestTool questionPraiseWithQuestionId:contentid start:^{
            self.praiseBtn.userInteractionEnabled = NO;
        } failure:^{
            self.praiseBtn.userInteractionEnabled = YES;
            [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        } success:^(NSDictionary *dic) {
            self.praiseBtn.userInteractionEnabled = YES;
            if ([dic[@"status"] isEqualToString:@"1"]) {
                [[FMUserDataSyncManager sharedManager] likeQuestion:contentid];
                self.askModel.virtualSatisfiedNums = [NSString stringWithFormat:@"%ld",self.askModel.virtualSatisfiedNums.integerValue + 1];
                
                NSString *praiseNumStr = nil;
                if (self.askModel.virtualSatisfiedNums.integerValue < 10000) {
                    praiseNumStr = [NSString stringWithFormat:@"%@", self.askModel.virtualSatisfiedNums];
                } else {
                    double wan = self.askModel.virtualSatisfiedNums.integerValue / 10000.0;
                    praiseNumStr = [NSString stringWithFormat:@"%.1f万", wan];
                }
                
                [self.praiseBtn setTitle:praiseNumStr forState:UIControlStateNormal];
                [self.praiseBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageLeft imageTitleSpacing:5.0f];
                
                self.praiseBtn.enabled = NO;
            } else {
                [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            }
        }];
    }
}

- (void)setAskModel:(FMAskCodeModel *)askModel {
    _askModel = askModel;
    
    NSMutableAttributedString *titleAttrStr = [[NSMutableAttributedString alloc] initWithString:askModel.questionContent];
    NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
    style.lineSpacing = 5.0f;
    [titleAttrStr addAttributes:@{NSParagraphStyleAttributeName : style} range:NSMakeRange(0, titleAttrStr.length)];
    self.titleLabel.attributedText = titleAttrStr;
    
    [self.infoView.iconImgV.iconImg sd_setImageWithURL:[NSURL URLWithString:askModel.bignameDto.userIco] placeholderImage:ImageWithName(@"userCenter_dltx")];
    /**
      2022/3/6 语音问股 视频问股 不显示投顾风格
     */
    self.infoView.tagView.hidden = YES;
    self.infoView.islive = askModel.bignameDto.isLive;
    self.infoView.nameLabel.text = askModel.bignameDto.userName;
    self.infoView.userId = askModel.bignameDto.userId;
    self.infoView.attestationType = askModel.bignameDto.attestationType;

    self.focusBtn.dakaId = askModel.bignameDto.userId;
    self.focusBtn.isFocus = [[FMUserDataSyncManager sharedManager] isDakaNoticed:askModel.bignameDto.userId];
    
    NSDate *nowDate = [NSDate dateWithTimeIntervalSince1970:askModel.answerTime/1000];
    NSString *timeStr = nil;
    if ([nowDate isToday]) {
        timeStr = [NSString stringFromDate:nowDate format:@"HH:mm 回答"];
    } else {
        if ([nowDate isThisYear]) {
            timeStr = [NSString stringFromDate:nowDate format:@"MM-dd HH:mm 回答"];
        } else {
            timeStr = [NSString stringFromDate:nowDate format:@"yyyy-MM-dd 回答"];
        }
    }
    self.infoView.timeLabel.text = timeStr;
    self.timeLB.text = [NSString stringWithFormat:@"%@",askModel.answerVideoTime];
    [self.videoImg sd_setImageWithURL:[NSURL URLWithString:askModel.questionImg] placeholderImage:[UIImage imageNamed:@"home_shortCut_bigPlaceholder"]];
    
    if (askModel.virtualListenerNums.integerValue > 0) {
        NSString *readNumStr = nil;
        if (askModel.virtualListenerNums.integerValue < 10000) {
            readNumStr = [NSString stringWithFormat:@"%@人看过",askModel.virtualListenerNums];
        } else {
            double wan = askModel.virtualListenerNums.integerValue / 10000.0;
            readNumStr = [NSString stringWithFormat:@"%.1f万人看过", wan];
        }
        self.watchListenLabel.text = readNumStr;
    } else {
        self.watchListenLabel.text = @"";
    }
    
    if (askModel.virtualSatisfiedNums.integerValue > 0) {
        NSString *praiseNumStr = nil;
        if (askModel.virtualSatisfiedNums.integerValue < 10000) {
            praiseNumStr = [NSString stringWithFormat:@"%@", askModel.virtualSatisfiedNums];
        } else {
            double wan = askModel.virtualSatisfiedNums.integerValue / 10000.0;
            praiseNumStr = [NSString stringWithFormat:@"%.1f万", wan];
        }
        [self.praiseBtn setTitle:praiseNumStr forState:UIControlStateNormal];
    } else {
        [self.praiseBtn setTitle:@"点赞" forState:UIControlStateNormal];
    }
    [self.praiseBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageLeft imageTitleSpacing:5];
    self.praiseBtn.enabled = ![[FMUserDataSyncManager sharedManager] isQuestionLiked:askModel.askCodeId];
}



- (void)setIsLastCell:(BOOL)isLastCell {
    _isLastCell = isLastCell;
    
    self.bottomLine.hidden = isLastCell;
}
@end
