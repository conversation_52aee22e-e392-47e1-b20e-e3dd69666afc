//
//  UPMarket2SDIBaseChartView.m
//  UPMarket2
//
//  Created by sammy<PERSON> on 2020/2/9.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2SDIBaseChartView.h"
#import "UPMarket2IndexUtil.h"
#import "UPMarket2SDISelectIndexModel.h"
#import "UPMarket2SDIStockCancelStockView.h"
#import "UPMarket2StockSetting.h"
#import <UPMarketIndex/UPMarketIndexManager.h>
#import <UPMarketIndex/UPMarketIndexGroupModel.h>
#import <UPMarketIndex/NSObject+func.h>
#import <UPMarket2/UPMarketUIKlineMajorLayer+UPMarketUIKlineMajorLayerHelper.h>
#import <UPMarketUISDK/UPMarketUIIndexUtil.h>
#import "UPMarket2StockSettingController.h"
#import "UPMarket2StockSettingManager.h"
#import "FMUPDataTool.h"
#import "FMIndicatorImportUtil.h"
#import "UPMarketChooseTemplatePopView.h"

static NSString* const kMajorIndexKey = @"kMajorIndexKey";
static NSString* const kMinorIndexKey = @"kMinorIndexKey";
static uint64_t kCrossLineTimeoutMS = 10 * 1000;

@interface UPMarket2SDIBaseChartView () <UPMarketUIBaseStockViewDelegate, UPTAFHandlerDelegate, UITextFieldDelegate> {
    CGFloat _ketboradHeight;
}

@property (nonatomic, strong) UIStackView *chartContainerView;

// 指标
@property (nonatomic, strong) UIView *indicatorView;
@property (nonatomic, strong) UIScrollView *indicatorButtonContentView;
@property (nonatomic, strong) UILabel *templateLabel;

// 指标类型的字典
@property (nonatomic, strong) NSMutableDictionary *indexIDDic;

@property (nonatomic, strong) UITapGestureRecognizer *tapGesture;

@property (nonatomic, strong) UILongPressGestureRecognizer *longPressGesture;

@property (nonatomic, strong) UPTAFHandler *handler;

@property (nonatomic, strong) UPMarket2SDIStockCancelStockView *cancelMaskView;

@property (nonatomic, strong) NSArray<UPMarketIndexGroupModel*> * selindexGroupArray;

@property (nonatomic, strong) NSMutableArray<UIButton*> * indexGroupBtnArray;

@property (nonatomic, strong) UPMarketIndexDataModel *indexDataModel;
@property (nonatomic, strong) UPMarketIndexDataModel *overlayIndexDataModel;

@property (nonatomic, strong) NSArray<UPMarketIndexGroupModel*> * templateIndexGroupArray;
@property (nonatomic, strong) NSArray<UPMarketIndexGroupModel*> * strategyIndexGroupArray;

@property (nonatomic, strong) NSMutableArray<NSString *> *showTemplateCategoryNames; // 模板分类名称数组，主要包含“模板”和“策略”两大类， 每个分类下包含不同的模板；
@property (nonatomic, copy) NSString *chooseTemeplateCategoryName; // 当前选择的模板分类名称



@end

@implementation UPMarket2SDIBaseChartView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = UIColor.up_contentBgColor;
        self.layoutMargins = UIEdgeInsetsMake(0, 5, 0, 5);
        
        self.indexHost = [[UPMarket2SDIStockIndexHost alloc] init];
        self.indexHost.delegate = self;
        
        self.indexIDDic = [NSMutableDictionary dictionary];
        
        _handler = [UPTAFHandler mainHandlerWithDelegate:self];
        
        [self addSubview:self.chartContainerView];
        [self.chartContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.mas_leftMargin);
            make.right.equalTo(self.mas_rightMargin);
            make.top.bottom.equalTo(self);
        }];
        
        
        [self initMajorAndMinorView:self.isKline];
        _isAppeared = NO;

        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(requestIndexAndTemplateListResult)
                                                     name:@"indexAndTemplateListUpdate"
                                                   object:nil];
    }
    return self;
}

#pragma mark - 指标模板新增
- (void)requestIndexAndTemplateListResult {
    [self viewDidAppear];
    
    if (self.chooseTemeplateCategoryName.length) {
        self.chooseTemeplateCategoryName = self.chooseTemeplateCategoryName;
    } else {
        NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
        NSString *chooseTemeplateCategoryName = [defaults objectForKey:@"chooseTemeplateCategoryNameCache"];
        if (!chooseTemeplateCategoryName.length) {
            chooseTemeplateCategoryName = @"模板";
        }
        self.chooseTemeplateCategoryName = chooseTemeplateCategoryName;
    }
    
    BOOL showIndicatorView = NO;
    if ([FMUPDataTool isLogin]) { // 登录
        if (self.stockModel.stockCategory == UPMarketStockCategory_SZ_A || self.stockModel.stockCategory == UPMarketStockCategory_SZ_B || self.stockModel.stockCategory ==   UPMarketStockCategory_SH_A || self.stockModel.stockCategory ==  UPMarketStockCategory_SH_B || self.stockModel.stockCategory == UPMarketStockCategory_INDEX) { // 沪深或指数
            if (!self.isLandscape && self.isKline) { // 竖屏，k线
                NSInteger klineType = [[self valueForKeyPath:@"klineType"] integerValue];
                if (klineType == UPMarketUIKlineDataTypeDaily) { // 日K
                    if (self.selindexGroupArray.count) {
                        showIndicatorView = YES;
                    }
                }
            }
        }
    }
    if (showIndicatorView) {
        self.indicatorView.hidden = NO;
        [self.chartContainerView addArrangedSubview:self.indicatorView];
        [self.indicatorView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self.chartContainerView);
            make.height.equalTo(@40);
        }];
    } else {
        self.indicatorView.hidden = YES;
    }
}

- (NSMutableArray<UIButton *> *)indexGroupBtnArray{
    if (!_indexGroupBtnArray) {
        _indexGroupBtnArray = [NSMutableArray new];
    }
    return _indexGroupBtnArray;
}

- (void)setSelindexGroupArray:(NSArray<UPMarketIndexGroupModel *> *)selindexGroupArray {
    _selindexGroupArray = selindexGroupArray;

    [self.indexGroupBtnArray removeAllObjects];
    [self.indicatorButtonContentView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    CGFloat startX = 10;
    NSInteger index = 0;
    UIButton * lastBtn = nil;
    for (UPMarketIndexGroupModel * model in selindexGroupArray) {
        UIButton * btn = [self createBtnWithTitle:model index:index];
        [self.indicatorButtonContentView addSubview:btn];
        [btn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.indicatorButtonContentView);
            if (lastBtn) {
                make.left.equalTo(lastBtn.mas_right).offset(UPWidth(16));
            }else{
                make.left.equalTo(self.indicatorButtonContentView).offset(UPWidth(startX));
            }
            
            if (index == selindexGroupArray.count - 1) {
                make.right.equalTo(self.indicatorButtonContentView).offset(-10);
            }
        }];
        lastBtn = btn;
        index++;
        [self.indexGroupBtnArray addObject:btn];
        
        if (index != selindexGroupArray.count) {
            UIView *sepline = [UIView new];
            sepline.backgroundColor = UIColor.fm_market_IndexTemplate_seplineColor;
            [self.indicatorButtonContentView addSubview:sepline];
            [sepline mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(lastBtn.mas_right).offset(UPWidth(7));
                make.centerY.equalTo(@0);
                make.width.equalTo(@1);
                make.height.equalTo(@14);
            }];
            sepline.layer.cornerRadius = 0.5;
            sepline.layer.masksToBounds = YES;
        }
    }
}

- (UIButton*)createBtnWithTitle:(UPMarketIndexGroupModel *)model index:(NSInteger)index{
    UIButton * btn = [UIButton buttonWithType:UIButtonTypeCustom];
    btn.semanticContentAttribute = UISemanticContentAttributeForceRightToLeft;

    btn.tag = index;
    [btn addTarget:self action:@selector(indicateItemClick:) forControlEvents:UIControlEventTouchUpInside];
    [btn setTitle:model.name forState:UIControlStateNormal];
    btn.titleLabel.font = [UIFont up_fontOfSize:14];
    
    [btn setTitleColor:UIColor.up_textPrimaryColor forState:UIControlStateNormal];
    [btn setTitleColor:UIColor.up_riseColor forState:UIControlStateSelected];
    
    NSArray<FMIndexMallTemplateGroupModel *> *arr = [FMIndicatorImportUtil importedTemplateIndicators];
    [arr enumerateObjectsUsingBlock:^(FMIndexMallTemplateGroupModel * _Nonnull groupModel, NSUInteger idx, BOOL * _Nonnull stop) {
        [groupModel.indextemplateList enumerateObjectsUsingBlock:^(FMIndexMallTemplateListModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if (obj.formulaTemplateId == -model.identify) {
                [self setupVIPBadgeForButton:btn withType:obj.vipType];
                
                *stop = YES;
            }
        }];
    }];
    
    return btn;
}

- (void)setupVIPBadgeForButton:(UIButton *)btn withType:(NSInteger)vipType {
    // 先移除可能已存在的标识视图
    for (UIView *subview in btn.subviews) {
        if ([subview.accessibilityIdentifier isEqualToString:@"vipBadge"]) {
            [subview removeFromSuperview];
            break;
        }
    }
    
    // 如果vipType不在1-6范围内，不添加标识
    if (vipType < 1 || vipType > 6) {
        return;
    }
    
    // 根据vipType选择不同的图标
    NSString *imageName = nil;
    
    if (vipType >= 1 && vipType <= 4) {
        imageName = @"个股/index_goldVIP";
    } else if (vipType == 5) {
        imageName = @"个股/index_redVIP";
    } else if (vipType == 6) {
        imageName = @"个股/index_orangeVIP";
    }
    
    if (imageName) {
        UIImageView *badgeImgView = [[UIImageView alloc] init];
        badgeImgView.image = UPTImg(imageName);
        badgeImgView.accessibilityIdentifier = @"vipBadge";
        [btn addSubview:badgeImgView];
        
        [badgeImgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.size.equalTo(@(CGSizeMake(18, 10)));
            make.top.equalTo(btn).offset(-3);
            make.right.equalTo(btn).offset(6);
        }];
    }
}

- (UILabel*)createLabelWithTitle:(NSString*)title {
    UILabel * label = [UILabel new];
    label.text = title;
    label.textAlignment = NSTextAlignmentLeft;
    label.font = [UIFont up_fontOfSize:15];
    label.textColor = UIColor.up_textPrimaryColor;
    return label;
}

- (UILabel *)templateLabel {
    if (!_templateLabel) {
        _templateLabel = [UILabel new];
        _templateLabel.font = [UIFont up_boldFontOfSize:15];
        _templateLabel.textAlignment = NSTextAlignmentCenter;
        _templateLabel.textColor = UIColor.up_textPrimaryColor;
    }
    return _templateLabel;
}

- (UIView *)indicatorView {
    if (!_indicatorView) {
        _indicatorView = [[UIView alloc] init];
        _indicatorView.backgroundColor = UIColor.fm_market_IndexTemplate_bgColor;
        
        UIStackView *vv = [[UIStackView alloc] init];
        vv.userInteractionEnabled = YES;
        [vv addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(chooseTemplate:)]];
        vv.axis = UILayoutConstraintAxisHorizontal;
        vv.distribution = UIStackViewDistributionEqualSpacing;
        vv.alignment = UIStackViewAlignmentCenter;
        vv.spacing = 5;
        [_indicatorView addSubview:vv];
        [vv mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.bottom.equalTo(_indicatorView);
            make.left.equalTo(@15);
        }];
        [vv addArrangedSubview:self.templateLabel];
        
        
        UIImageView *image = [[UIImageView alloc] initWithImage:UPTImg(@"指标设置/指标模板选择")];
        
        [vv addArrangedSubview:image];
        
        [image mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(vv);
            make.right.mas_equalTo(vv.mas_right);
        }];
        
        
        [_indicatorView addSubview:self.indicatorButtonContentView];
        
        [self.indicatorButtonContentView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(vv.mas_right).offset(10);
            make.right.equalTo(_indicatorView.mas_right).offset(-5);
            make.top.bottom.equalTo(_indicatorView);
        }];
        
    }
    
    return _indicatorView;
}

- (void)getTemplatesArr {
    NSUInteger chartTabIndex = [[[UPMarket2Preference sharedManager].tradeViewTabIndex valueForKey:@"chartTabIndexWillSet"] intValue];
    NSUInteger chartSubTabIndex = [[[UPMarket2Preference sharedManager].tradeViewTabIndex valueForKey:@"chartSubTabIndexWillSet"] intValue];
    
    NSArray *templateArray = [UPMarketIndexManager.share getIndexGroupWithPeriod:self.isKline? getKlinePeriodByIndex(chartTabIndex, chartSubTabIndex):UPMarketIndexLocalPeriodRTMin stockModel:self.stockModel needCheckPermission:NO];
    if (templateArray.count) {
        NSLog(@"templateArray有数据");
    }
    
    NSArray *sortArray = [[UPMarket2StockSettingManager sharedInstance].indexCache getSortedTemplateArrayIsKline:self.isKline];
    
    //按顺序取出数据
    NSMutableArray<UPMarketIndexGroupModel *> *datas = [NSMutableArray array];
    for (NSNumber * templateId in sortArray) {
        UPMarketIndexGroupModel *group = templateArray.up_first(^BOOL(NSUInteger idx, UPMarketIndexGroupModel*  _Nonnull obj) {
            return obj.identify == templateId.integerValue;
        });
        
        if (group) {
            [datas addObject:group];
        }
    }
    self.templateIndexGroupArray = datas.copy;
}

- (void)getStrategyIndexArr {
    NSUInteger chartTabIndex = [[[UPMarket2Preference sharedManager].tradeViewTabIndex valueForKey:@"chartTabIndexWillSet"] intValue];
    NSUInteger chartSubTabIndex = [[[UPMarket2Preference sharedManager].tradeViewTabIndex valueForKey:@"chartSubTabIndexWillSet"] intValue];

   self.strategyIndexGroupArray = [UPMarketIndexManager.share getStrategyIndexWithPeriod:getKlinePeriodByIndex(chartTabIndex, chartSubTabIndex) stockModel:self.stockModel needCheckPermission:NO];
}


// 更新按钮状态
- (void)autoSetTemplateButtonStatus{
    if (self.selindexGroupArray.count == 0) {
        return;
    }
    
    for (NSInteger index = 0; index < self.indexGroupBtnArray.count; index++) {
        UPMarketIndexGroupModel * groupModel = self.selindexGroupArray[index];
        UIButton * btn = self.indexGroupBtnArray[index];
        btn.selected = groupModel.isCurrentSelect;
    }
}

// 更新选中模板
- (void)refreshSelectGroupModel{
    NSMutableArray * indexIdArray = [NSMutableArray new];
    for (UPMarket2StockView *view in self.stockViews) {
        [indexIdArray addObject:@(view.indexID)];
    }
    
    NSMutableSet * currentIndexIdSet = [NSMutableSet setWithArray:indexIdArray];
    WeakSelf(weakSelf)
    [self.selindexGroupArray enumerateObjectsUsingBlock:^(UPMarketIndexGroupModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        BOOL selected = NO;
        if (obj.specialIndexId) {
            if ([currentIndexIdSet containsObject:@(obj.specialIndexId)]) {
                selected = YES;
            }
        }else{
            NSSet *allIndexIdSet = [NSSet setWithArray:obj.allIndexIdArray];
            if ([currentIndexIdSet isEqualToSet:allIndexIdSet]) {
                selected = YES;
            }
        }
        obj.isCurrentSelect = selected;
    }];
}

- (void)indicateItemClick:(UIButton *)sender {
    if (self.selindexGroupArray.count == 0) {
        return;
    }
    
    UPMarketIndexGroupModel * selectGroupModel = self.selindexGroupArray[sender.tag];
    if (selectGroupModel.isCurrentSelect && sender.selected) {
        return;
    }

    [self.selindexGroupArray enumerateObjectsUsingBlock:^(UPMarketIndexGroupModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        obj.isCurrentSelect = NO;
    }];
    selectGroupModel.isCurrentSelect = YES;

    if (selectGroupModel.mainIndexIdArray.count) {
        [self.indexHost switchIndex:YES indexIDs:selectGroupModel.mainIndexIdArray syncGloble:YES];//
    } 
    if (selectGroupModel.viceIndexIdArray.count) {
        [self.indexHost switchIndex:NO indexIDs:selectGroupModel.viceIndexIdArray syncGloble:YES];//
    } 
    if (!selectGroupModel.specialIndexId) {
        if (self.isKline) {
            [UPMarket2StockSettingStateManager updateKLineMinorChartNum:selectGroupModel.viceIndexIdArray.count];
        }else{
            [UPMarket2StockSettingStateManager updateMinuteMinorChartNum:selectGroupModel.viceIndexIdArray.count];
        }
    }
//    [self.indexHost resetIndexs];
    
    
    for (NSInteger index = 0; index < self.indexGroupBtnArray.count; index++) {
        UIButton *btn = self.indexGroupBtnArray[index];
        UPMarketIndexGroupModel * groupModel = self.selindexGroupArray[index];
        btn.selected = groupModel.isCurrentSelect;
    }
    
    [[NSNotificationCenter defaultCenter] postNotificationName:@"UPMarket2StockSettingStateChangedNotificationName" object:nil userInfo:nil];

    // 通知上层控制器用户已手动选择模板
    if (self.userDidSelectTemplateBlock) {
        self.userDidSelectTemplateBlock();
    }
}

- (void)chooseTemplate:(UITapGestureRecognizer *)gr {
    UPMarketChooseTemplatePopView *view = [UPMarketChooseTemplatePopView sharedInstance];
    if (!view.superview) {

        [view showFromView:[FMUPDataTool currentVC].view referenceView:gr.view withMenuItems:self.showTemplateCategoryNames];
        WeakSelf(weakSelf)
        view.buttonTappedBlock = ^(NSString * buttonTitle, NSInteger index) {
            if (![weakSelf.chooseTemeplateCategoryName isEqualToString:buttonTitle]) {
                weakSelf.chooseTemeplateCategoryName = buttonTitle;
                weakSelf.templateLabel.text = buttonTitle;
                // 通知上层控制器用户已手动选择模板
                if (weakSelf.userDidSelectTemplateBlock) {
                    weakSelf.userDidSelectTemplateBlock();
                }
            }
        };
    } else {
        [view dismiss];
    }
}

- (void)setChooseTemeplateCategoryName:(NSString *)chooseTemeplateCategoryName {
    [self.showTemplateCategoryNames removeAllObjects];
    
    [self getTemplatesArr];
    [self getStrategyIndexArr];
    
    if (self.templateIndexGroupArray.count) {
        [self.showTemplateCategoryNames addObject:@"模板"];
    }
    if (self.strategyIndexGroupArray.count) {
        [self.showTemplateCategoryNames addObject:@"策略"];
    }
    
    if (self.stockModel.stockCategory == UPMarketStockCategory_INDEX) { // 指数只显示模板
        chooseTemeplateCategoryName = @"模板";
    }
    
    NSInteger hasChooseTemeplateCategoryName = -1; // -1表示没找到对应模板，也没有替换显示的模板。 0表示有替换的。 1表示直接找到
    NSArray<FMIndexMallTemplateGroupModel *> *arr = [FMIndicatorImportUtil importedTemplateIndicators];
    for (FMIndexMallTemplateGroupModel *obj in arr) {
        if ([obj.templateTypeName isEqualToString:chooseTemeplateCategoryName]) {
            if ([chooseTemeplateCategoryName isEqualToString:@"模板"]) {
                if (self.templateIndexGroupArray.count) {
                    self.selindexGroupArray = [self.templateIndexGroupArray copy];
                    hasChooseTemeplateCategoryName = 1;
                } else if (self.strategyIndexGroupArray.count) {
                    chooseTemeplateCategoryName = @"策略";
                    self.selindexGroupArray = [self.strategyIndexGroupArray copy];
                    hasChooseTemeplateCategoryName = 0;
                }
            } else if ([chooseTemeplateCategoryName isEqualToString:@"策略"]) {
                if (self.strategyIndexGroupArray.count) {
                    self.selindexGroupArray = [self.strategyIndexGroupArray copy];
                    hasChooseTemeplateCategoryName = 1;
                } else if (self.templateIndexGroupArray.count) {
                    chooseTemeplateCategoryName = @"模板";
                    self.selindexGroupArray = [self.templateIndexGroupArray copy];
                    hasChooseTemeplateCategoryName = 0;
                }
            }
            
            break;
        }
    }
    if (hasChooseTemeplateCategoryName == -1) {
        chooseTemeplateCategoryName = @"";
        self.selindexGroupArray = nil;
    }
    
    _chooseTemeplateCategoryName = chooseTemeplateCategoryName;
    self.templateLabel.text = chooseTemeplateCategoryName;
    [self refreshSelectGroupModel];
    [self autoSetTemplateButtonStatus];
    
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    [defaults setObject:chooseTemeplateCategoryName forKey:@"chooseTemeplateCategoryNameCache"];
    [defaults synchronize];
}

#pragma mark - 模板名称设置（指标联动功能）
- (void)setTemplateName:(NSString *)templateName {
    _templateName = templateName;

    // 如果有指定的具体模板名称，尝试选中对应的模板
    if (templateName.length > 0) {
        [self selectTemplateWithName:templateName];
    }
}

/// 根据具体模板名称选中对应的指标模板
/// @param templateName 具体模板名称（如"龙头启升"、"龙头再涨"等）
- (void)selectTemplateWithName:(NSString *)templateName {
    // 检查是否有对应的模板
    NSArray<FMIndexMallTemplateGroupModel *> *arr = [FMIndicatorImportUtil importedTemplateIndicators];
    for (FMIndexMallTemplateGroupModel *groupModel in arr) {
        for (FMIndexMallTemplateListModel *templateModel in groupModel.indextemplateList) {
            if ([templateModel.name isEqualToString:templateName]) {
                // 找到对应的模板，先设置模板分类，再选中具体模板
                self.chooseTemeplateCategoryName = groupModel.templateTypeName;

                // 在指标按钮中找到对应的模板并选中
                [self selectTemplateInButtons:templateModel];
                return;
            }
        }
    }

    // 如果没有找到对应的模板，延迟重试（可能模板数据还未加载完成）
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self selectTemplateWithName:templateName];
    });
}

/// 在指标按钮中选中指定的模板
/// @param templateModel 要选中的模板
- (void)selectTemplateInButtons:(FMIndexMallTemplateListModel *)templateModel {
    // 遍历指标按钮，找到对应的模板并选中
    for (NSInteger i = 0; i < self.selindexGroupArray.count; i++) {
        UPMarketIndexGroupModel *groupModel = self.selindexGroupArray[i];
        if (groupModel.identify == -templateModel.formulaTemplateId) {
            // 找到对应的按钮，模拟点击选中
            if (i < self.indexGroupBtnArray.count) {
                UIButton *button = self.indexGroupBtnArray[i];
                button.tag = i;
                [self indicateItemClick:button];
            }
            break;
        }
    }
}

- (void)superScrolling {
    [[UPMarketChooseTemplatePopView sharedInstance] dismiss];
}

- (NSMutableArray *)showTemplateCategoryNames {
    if (!_showTemplateCategoryNames) {
        _showTemplateCategoryNames = [NSMutableArray array];
    }
    
    return _showTemplateCategoryNames;
}

#pragma mark - 老内容
- (void)layoutSubviews {
    [super layoutSubviews];
    
    CGFloat height = self.bounds.size.height;
    NSInteger minorCount = self.isKline ? UPMarket2StockSettingStateManager.numberOfKLineMinorChart :  UPMarket2StockSettingStateManager.numberOfMinuteMinorChart;
    BOOL isLandscape = self.isLandscape;
    
    CGFloat majorHeight,minorHeight,indicatorHeight = 0.0;

    if (isLandscape) {
        if (minorCount == 1) {
            minorHeight = 76;
        } else if (minorCount == 2) {
            minorHeight = 60;
        } else if (minorCount == 3) {
            minorHeight = 50;
        } else {
            minorHeight = 45;
        }
        
        majorHeight = height - minorHeight * minorCount;
        
    } else {
        minorHeight = 100;
        majorHeight = 264;
        
        indicatorHeight = 40;
    }
    
    for (int i = 0; i < self.stockViews.count; i++) {
        
        UPMarket2StockView *composeView = self.stockViews[i];
        
        // 主图
        if (i == 0) {
            [composeView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.left.right.equalTo(self.chartContainerView);
                make.height.equalTo(@(majorHeight));
            }];
            
        } else {
            // 幅图
            [composeView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.left.right.equalTo(self.chartContainerView);
                make.height.equalTo(@(minorHeight));
            }];
        }
    }
    
    UPMarket2StockView *composeView = self.stockViews.firstObject;
    if (composeView) {
        [self.chartToolView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(composeView);
            make.bottom.equalTo(composeView).offset(-5);
            make.height.equalTo(@(UPWidth(40)));
        }];
    }
}

- (void)setStockModel:(UPMarketUIBaseModel *)stockModel {
    [super setStockModel:stockModel];
    
    self.indexHost.stockModel = stockModel;
    
    for (UPMarket2StockView *view in self.stockViews) {
        view.stockModel = stockModel;
    }
    
//    if (self.chooseTemeplateIndex == 0 || self.stockModel.stockCategory == UPMarketStockCategory_INDEX) {
//        [self getTemplatesArr];
//    } else {
//        [self getStrategyIndexArr];
//    }
    
    if (!self.templateLabel.text.length) {
        NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
        NSString *chooseTemeplateCategoryName = [defaults objectForKey:@"chooseTemeplateCategoryNameCache"];
        if (!chooseTemeplateCategoryName.length) {
            chooseTemeplateCategoryName = @"模板";
        }
        self.chooseTemeplateCategoryName = chooseTemeplateCategoryName;
    } else {
        // 股票切换时，同步模板显示状态与实际指标组合状态
        [self refreshSelectGroupModel];
        [self autoSetTemplateButtonStatus];
    }
    
    [self requsetCccbx:stockModel];
}

- (void)requsetCccbx:(UPMarketUIBaseModel *)stockModel {
    
    if (self.stockViews.count > 0) {
        UPMarket2StockView *stockView = self.stockViews.firstObject;
        stockView.stockLayer.positionPrice = CGFLOAT_MAX;
    }
}

- (void)updateIndicator:(UPMarket2StockView *)stockview {
    
    stockview.stockLayer.stockModel = self.stockModel;

    stockview.stockLayer.lock = [UPMarket2IndexUtil isIndexLocked:stockview.indexID];
    
    stockview.stockLayer.customDataArray = self.customDataArray;

}
- (void)setCustomDataArray:(NSArray *)customDataArray
{
    [super setCustomDataArray:customDataArray];
    
    for (UPMarket2StockView *view in self.stockViews) {
        view.stockLayer.customDataArray = self.customDataArray;
    }
}


- (void)viewDidAppear {
    self.isAppeared = YES;

    for (UPMarket2StockView *view in self.stockViews) {
        [view stockViewStart];
        [self updateIndicator:view];
    }

    [self.indexHost resetIndexs];
}

- (void)viewDidDisappear {
    self.isAppeared = NO;

    for (UPMarket2StockView *view in self.stockViews) {
        [view stockViewStop];
    }
    
    for (UPMarket2StockView *view in self.stockViews) {
        [view endRegion];
    }
    
    [self exitCrossLineMode];
}

- (id)getCurrentMajorData:(UPMarketUIBaseStockLayer *)stockLayer {
    return nil;
}

- (void)endRegion {
    for (UPMarket2StockView *view in self.stockViews) {
        [view endRegion];
    }
}

- (BOOL)canShowLongClickMaskView {
    return YES;
}

- (BOOL)canShowAfterTradeKCBLabel {
    return NO;
}

- (BOOL)supportUpdateWhenLongPress {
    return NO;
}

- (void)exitCrossLineMode {
    
    if (self.stockViews.count) {
        for (UPMarket2StockView *stockView in self.stockViews) {
            [stockView exitCrossState];
        }
    }
    
    [self.handler removeMessage:0];
}

// MARK: UPMarket2SDIStockIndexHostDelegate
- (BOOL)isDayKlineTab {
    return NO;
}

- (BOOL)isRTMinuteTab {
    return NO;
}

- (void)setMajorIndex:(NSInteger)indexID {
    BOOL isChanged = [self.indexIDDic[kMajorIndexKey] integerValue] != indexID;
    
    self.indexIDDic[kMajorIndexKey] = @(indexID);
    
    if (isChanged) {
        [self setMajorView:indexID];
    }
}

- (void)setMinorIndex:(NSInteger)indexID position:(NSInteger)position {
    
    NSString *key = [NSString stringWithFormat:@"%@_%@",kMinorIndexKey,@(position)];
    
    BOOL isChanged = [self.indexIDDic[key] integerValue] != indexID;
    if (isChanged) {
        [self setMinorView:indexID position:position];
    }
    self.indexIDDic[key] = @(indexID);

    if (isChanged) {
        [self setMinorView:indexID position:position];
    }
}

// MARK: UPMarket2ChartToolViewDelegate
- (void)didSelectedWithType:(UPMarket2ChartGestureButtonType)type {
    
}

// MARK: UPMarket2StockViewDelegate
- (void)stockViewDidSelectedIndexModel:(id)data
{
    NSLog(@"当前线程 %@ %s:%d",[NSThread currentThread] ,__FUNCTION__, __LINE__);
    if (self.didSelectedIndexModel) {
        self.didSelectedIndexModel(data);
    }
}

- (void)stockViewDidClickOverlayCloseWithStockView:(UPMarket2StockView *)stockView
{
    NSLog(@"当前线程 %@ %s:%d",[NSThread currentThread] ,__FUNCTION__, __LINE__);
    [self.stockViews.firstObject overlayWithIndexID:0];
    self.overlayIndexId = 0;
}

- (void)stockViewClickHistoryMinuteWithStockLayer:(UPMarketUIBaseStockLayer *)stockLayer iskline:(BOOL)iskline {
    if (iskline && self.xLabelClickBlock) {
        [self.handler removeMessage:0];
        
        id currentData = [self getCurrentMajorData:stockLayer];
        
        self.xLabelClickBlock(YES,currentData);
        
        dispatch_async(dispatch_get_main_queue(), ^{
            self.isHistoryMinute = YES;
        });
        
    }
}

-(void)stockViewClickLockWithStockLayer:(UPMarketUIBaseStockLayer *)stockLayer {
//    if ([UPMarket2IndexUtil isSqzbLayer:stockLayer]) {
//        if (![UPUserManager isUserLogin]) {
//            [UPRouterUtil goUserLogin];
//        } else {
//            // 跳转到h5
//            UPRouterNavigate(UPURLSqsj);
//        }
//        UPRouterNavigate(UPURLSqsj);
//        return;
//    }
//    [UPRouter navigate:UPURLLevel2Ads];
}

- (void)stockViewClickLockUserInteractionRectWithStockLayer:(UPMarketUIBaseStockLayer *)stockLayer {
//    if ([UPMarket2IndexUtil isSqzbLayer:stockLayer]) {
//        [self.indexHost switchIndex:YES position:0 indexID:UPMarketUIMajorIndexMALine];
//        [self exitCrossLineMode];
//    }
}

- (BOOL)stockViewCanShowMaskBtn:(UPMarket2StockView *)stockView {
    return NO;
}

- (void)stockViewCrossStateChangeWithStockLayer:(UPMarketUIBaseStockLayer *)stockLayer crossState:(UPMarketCrossLineState)state {
    if (![stockLayer isMajorLayer]) {
        return;
    }
    
    if (self.crossStateChangeBlock) {
        self.crossStateChangeBlock(state);
    }
    
    [self.handler removeMessage:0];

    if (state == UPMarketCrossLineStateBegin) {
        [UPFeedbackUtil vibrate];
    } else if (state == UPMarketCrossLineStateUp) { // 手指抬起,开始计时
        if (!self.isHistoryMinute) {
            [self.handler sendMessageDelayed:0 delayMillis:kCrossLineTimeoutMS];
        }
    } else {
        // 退出长按十字丝模式
        if (!self.isHistoryMinute) {
            if (self.longPressBlock) {
                self.longPressBlock(nil);
            }
        }
    }
}

- (BOOL)stockViewIslandscapeWithStocklayer:(UPMarketUIBaseStockLayer *)stockLayer {
    return UPMarket2LandscapeUtil.isLandscape;
}

- (void)stockViewUpdateRegionData:(NSArray<UPMarketUIKlineModel *> *)data {
    if (self.regionChangeBlock) {
        self.regionChangeBlock(data);
    }
}

- (void)stockViewDidClickQXDataModel:(id)data qxData:(UPHqQXData *)qxData {
    if (self.clickQXModelBlock) {
        self.clickQXModelBlock(data, qxData);
    }
}

- (void)stockViewDataUpdateWithStockLayer:(UPMarketUIBaseStockLayer *)stockLayer {
    // 非长按状态下,不需要通过该回调更新盘口数据
    if (![stockLayer isMajorLayer] || !stockLayer.isShowCross) {
        return;
    }
    
    id currentData = [self getCurrentMajorData:stockLayer];
        
    if (self.longPressBlock) {
        self.longPressBlock(currentData);
    }
    
    if (self.isHistoryMinute && self.xLabelClickBlock) {
        [NSObject cancelPreviousPerformRequestsWithTarget:self];
        
        if (stockLayer.crossState == UPMarketCrossLineStateUp) {
            self.xLabelClickBlock(YES, currentData);
        } else {
            [self performSelector:@selector(delayDispatchHistoryMinute:) withObject:currentData afterDelay:1];
        }
    }
}

// MARK: UPMarketUIBaseStockViewDelegate
- (void)onClickStockView:(UPMarketUIBaseStockView *)stockViews {
    
    NSInteger index = [self.stockViews indexOfObject:(UPMarket2StockView *)stockViews];
    
    if (self.isLandscape) {
        if (index == 0) {
            [UPThirdStatistics trackEvent:@"APP_HQ_GGFS_HP_ZT"];
        } else {
            [UPThirdStatistics trackEvent:@"APP_HQ_GGFS_HP_FT"];
        }
    }
    
    BOOL isMajorIndex = index == 0;
    
    if(![UPMarket2StockSettingStateManager isSwitchIndexTap]) {
        return;
    }
    
    [self.indexHost switchIndex:isMajorIndex position:isMajorIndex ? index : index - 1];
}

// MARK: UPTAFHandlerDelegate
-(void)handleMessage:(int)what object:(id)anObject {
    [self exitCrossLineMode];
}

// MARK: Private
- (void)setCustomConfig:(UPMarket2SDICustomConfig *)customConfig {
    _customConfig = customConfig;
    for (UPMarket2StockView *view in self.stockViews) {
        view.customConfig = customConfig;
    }
}

- (void)setIsLandscape:(BOOL)isLandscape {
   _isLandscape = isLandscape;
    [self updateSettingChange];
}

- (void)initMajorAndMinorView:(BOOL)isKline {
    
    UPMarket2StockView *majorView = [UPMarket2IndexUtil createStockView:0 indexID:[self.indexIDDic[kMajorIndexKey] integerValue] isKline:isKline];
    majorView.shouldHideQX = YES;
    NSInteger minorCount = isKline ? UPMarket2StockSettingStateManager.numberOfKLineMinorChart : UPMarket2StockSettingStateManager.numberOfMinuteMinorChart;
    
    NSMutableArray *minorViews = [NSMutableArray arrayWithCapacity:minorCount];
    for (int i = 0; i < minorCount; i++) {
        NSString *key = [NSString stringWithFormat:@"%@_%@",kMinorIndexKey,@(i)];
        
        UPMarket2StockView *minorView = [UPMarket2IndexUtil createStockView:i indexID:[self.indexIDDic[key] integerValue] isKline:isKline];
        minorView.shouldHideQX = YES;
        [minorViews addObject:minorView];
    }
    
    [self configStockView:majorView minorViews:minorViews];
}

- (void)configStockView:(UPMarket2StockView *)majorView minorViews:(NSArray *)minorViews {
    void(^clickBlock)(UPMarket2ChartViewEventTag, BOOL, NSInteger) = nil;
    for (UPMarket2StockView *view in self.stockViews) {
        clickBlock = view.clickBlock;
        [view stockViewStop];
        [view removeGestureRecognizer:self.tapGesture];
        [view removeGestureRecognizer:self.longPressGesture];
        [view removeFromSuperview];
        !self.indexDeallocBlock?:self.indexDeallocBlock(view.indexID);
    }
    self.stockViews = [NSMutableArray array];
    
    [self.stockViews addObject:majorView];
    majorView.stockViewDelegate = self;
    majorView.delegate = self;
    
    if (clickBlock) {
        majorView.clickBlock = clickBlock;
    }
    
    [majorView enableGestures:UPMarketUIBaseStockViewGestureTap | UPMarketUIBaseStockViewGestureLongPress withRecognizer:@{
        @(UPMarketUIBaseStockViewGestureTap) : self.tapGesture,
        @(UPMarketUIBaseStockViewGestureLongPress) : self.longPressGesture,
    }];
    
    [self.chartContainerView addArrangedSubview:majorView];
    
    for (UPMarket2StockView *minorView in minorViews) {
        [self.stockViews addObject:minorView];
        minorView.stockViewDelegate = self;
        minorView.delegate = self;
        
        if (clickBlock) {
            minorView.clickBlock = clickBlock;
        }
        
        [minorView enableGestures:UPMarketUIBaseStockViewGestureTap | UPMarketUIBaseStockViewGestureLongPress withRecognizer:@{
            @(UPMarketUIBaseStockViewGestureTap) : self.tapGesture,
            @(UPMarketUIBaseStockViewGestureLongPress) : self.longPressGesture,
        }];
        
        [self.chartContainerView addArrangedSubview:minorView];
    }
    
    BOOL showIndicatorView = NO;
    if ([FMUPDataTool isLogin]) { // 登录
        if (self.stockModel.stockCategory == UPMarketStockCategory_SZ_A || self.stockModel.stockCategory == UPMarketStockCategory_SZ_B || self.stockModel.stockCategory ==   UPMarketStockCategory_SH_A || self.stockModel.stockCategory ==  UPMarketStockCategory_SH_B || self.stockModel.stockCategory == UPMarketStockCategory_INDEX) { // 沪深或指数
            if (!self.isLandscape && self.isKline) { // 竖屏，k线
                NSInteger klineType = [[self valueForKeyPath:@"klineType"] integerValue];
                if (klineType == UPMarketUIKlineDataTypeDaily) { // 日K
                    if (self.selindexGroupArray.count) {
                        showIndicatorView = YES;
                    }
                }
            }
        }
    }
    if (showIndicatorView) {
        self.indicatorView.hidden = NO;
        [self.chartContainerView addArrangedSubview:self.indicatorView];
        [self.indicatorView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self.chartContainerView);
            make.height.equalTo(@40);
        }];
    } else {
        self.indicatorView.hidden = YES;
    }
    
    [self reloadIndexUpdateBlock];
    
    for (UPMarket2StockView *view in self.stockViews) {
        !view.updateIndexIdBlock?:view.updateIndexIdBlock(0,view.indexID,0);
    }
}

- (void)reloadIndexUpdateBlock
{
    //子类分别实现
}

- (void)setMajorView:(NSInteger)indexID {
    [self requsetCccbx:self.stockModel];
}

- (void)setMinorView:(NSInteger)indexID position:(NSInteger)position {
    
}

- (void)maskStock:(UPHqStockHq *)stockHq {
    self.maskSelectStockHq = stockHq;
    
    if(IsValidateString(self.stockModel.stockCode)){
        [UPMarket2Preference.sharedManager.maskSelectStockHqDic setValue:self.maskSelectStockHq forKey:self.stockModel.stockCode];
    }
    
    [self addSubview:self.cancelMaskView];
    [self.cancelMaskView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self).offset(40);
        make.left.equalTo(self).offset(20);
        make.height.equalTo(@20);
    }];
    
    self.cancelMaskView.titleLabel.text = stockHq.name;
}

- (void)cancelMaskViewClick {
    self.maskSelectStockHq = nil;
    
    if(IsValidateString(self.stockModel.stockCode)){
        [UPMarket2Preference.sharedManager.maskSelectStockHqDic setValue:self.maskSelectStockHq forKey:self.stockModel.stockCode];
    }
    
    [self.cancelMaskView removeFromSuperview];
    [self.stockViews.firstObject maskStock:nil];
}

- (void)updateSettingChange {
    [self initMajorAndMinorView:self.isKline];
    
//    if (!self.customConfig.isGetDragonStyle) {
//        [self.indexHost resetIndexs];
//    }
//
//    [self.indexHost resetIndexs];
    
    [self requsetCccbx:self.stockModel];
    
    [self setNeedsLayout];
}

- (void)updateWhileStockViewsChange {
    [self initMajorAndMinorView:self.isKline];
    [self setNeedsLayout];
}

- (void)clearData {
    [self.stockViews makeObjectsPerformSelector:@selector(clearData)];
}

- (void)moveCrossLine:(UPMarketUIMoveCrossLineType)type {
    for (UPMarket2StockView *stockView in self.stockViews) {
        [stockView moveCrossLine:type];
    }
}

- (void)delayDispatchHistoryMinute:(id)currentData {
    self.xLabelClickBlock(YES, currentData);
}

// MARK: Getter & Setter

- (UITapGestureRecognizer *)tapGesture {
    if (!_tapGesture) {
        _tapGesture = [[UITapGestureRecognizer alloc]init];
        [self addGestureRecognizer:_tapGesture];
    }
    return _tapGesture;
}

- (UILongPressGestureRecognizer *)longPressGesture {
    if (!_longPressGesture) {
        _longPressGesture = [[UILongPressGestureRecognizer alloc] init];
        [self addGestureRecognizer:_longPressGesture];
    }
    return _longPressGesture;
}

- (void)setStockHq:(UPHqStockHq *)stockHq {
    super.stockHq = stockHq;
    
    for (UPMarket2StockView *view in self.stockViews) {
        view.stockHq = stockHq;
    }
}

- (void)setClickBlock:(void (^)(UPMarket2ChartViewEventTag, id))clickBlock {
    WeakSelf(weakSelf)
    
    void(^stockViewClickBlock)(UPMarket2ChartViewEventTag, BOOL, NSInteger) = ^(UPMarket2ChartViewEventTag tag, BOOL isMajorIndex, NSInteger position){
        
        if (tag == UPMarket2StockViewEventTagIndexName) {
            NSArray *indexArray = [weakSelf.indexHost getIndexArray:isMajorIndex position:position];
            NSInteger selectIndex = [weakSelf.indexHost getSelectIndex:isMajorIndex position:position];

            UPMarket2SDISelectIndexModel *indexModel = [UPMarket2SDISelectIndexModel new];
            indexModel.isKline = weakSelf.isKline;
            indexModel.isMajor = isMajorIndex;
            indexModel.indexArray = indexArray;
            indexModel.selectIndex = selectIndex;
            if (weakSelf.isKline) {
                if (isMajorIndex) {
                    indexModel.type = 2;
                } else {
                    indexModel.type = 3;
                }
            } else {
                if (isMajorIndex) {
                    indexModel.type = 4;
                } else {
                    indexModel.type = 5;
                }
            }
            
            indexModel.selectedBlock = ^(NSInteger newIndex) {
                [weakSelf.indexHost switchIndex:isMajorIndex position:position indexID:newIndex];
            };
            
            if (isMajorIndex && weakSelf.isKline) {
                indexModel.overlaySelectIndex = weakSelf.stockViews.firstObject.overlayLayer.indexID;
                indexModel.overlayIndexBlock = ^(NSInteger newIndex) {
                    [weakSelf.stockViews.firstObject  overlayWithIndexID:newIndex];
                };
            }
            
            clickBlock(tag, indexModel);
        } else {
            clickBlock(tag, nil);
        }
        
    };
    
    for (UPMarket2StockView *view in self.stockViews) {
        view.clickBlock = stockViewClickBlock;
    }
}

- (UPMarket2SDIStockCancelStockView *)cancelMaskView {
    if (!_cancelMaskView) {
        UPMarket2SDIStockCancelStockView *view = [UPMarket2SDIStockCancelStockView new];
        [view addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(cancelMaskViewClick)]];
        
        _cancelMaskView = view;
    }
    
    return _cancelMaskView;
}

- (void)setIsHistoryMinute:(BOOL)isHistoryMinute {
    _isHistoryMinute = isHistoryMinute;
    
    for (UPMarket2StockView *view in self.stockViews) {
        view.isHistoryMinute = isHistoryMinute;
    }
}

- (UPMarket2ChartToolView *)chartToolView {
    if (!_chartToolView) {
        _chartToolView = [UPMarket2ChartToolView chartToolView:self.isKline];
        _chartToolView.delegate = self;
    }
    
    return _chartToolView;
}


- (UIScrollView *)indicatorButtonContentView {
    if (!_indicatorButtonContentView) {
        _indicatorButtonContentView = [[UIScrollView alloc] init];
        _indicatorButtonContentView.showsHorizontalScrollIndicator = NO;
    }
    return _indicatorButtonContentView;
}


- (UIStackView *)chartContainerView {
    if (!_chartContainerView) {
        _chartContainerView = [UIStackView new];
        _chartContainerView.axis = UILayoutConstraintAxisVertical;
        _chartContainerView.distribution = UIStackViewDistributionFill;
        _chartContainerView.alignment = UIStackViewAlignmentCenter;
        _chartContainerView.spacing = 0;
    }
    return _chartContainerView;
}

- (void)updateIndexDataModel:(UPMarketIndexDataModel *)indexDataModel isOverlay:(BOOL)isOverlay
{
    if (isOverlay) {
        self.overlayIndexDataModel = indexDataModel;
    }else{
        self.indexDataModel = indexDataModel;
    }
    
    if (isOverlay && self.stockViews.firstObject.overlayLayer.indexID == indexDataModel.indexId) {
        NSDictionary *marketIndexMap = [NSDictionary dictionaryWithDictionary:indexDataModel.marketIndexInfoMap];
        [self.stockViews.firstObject updateOverlayIndexMap:marketIndexMap];
    }else{
        for (UPMarket2StockView *stockView in self.stockViews) {
            //stockView 包含主图和副图
            if (stockView.indexID == indexDataModel.indexId) {
                NSDictionary *marketIndexMap = [NSDictionary dictionaryWithDictionary:indexDataModel.marketIndexInfoMap];
                NSDictionary *marketMinIndexMap = [NSDictionary dictionaryWithDictionary:indexDataModel.marketIndexMinInfoMap];
                NSDictionary *ddeInfoMap = [NSDictionary dictionaryWithDictionary:indexDataModel.ddeInfoMap];
                if (marketIndexMap.count) {
                    [stockView updateIndexMap:marketIndexMap];
                }
                if (marketMinIndexMap.count) {
                    [stockView updateIndexMap:marketMinIndexMap];
                }
                if (ddeInfoMap.count) {
                    stockView.ddeDatadic = ddeInfoMap;
                }
            }
        }
    }
}


@end
