//
//  UPMarket2SDIBaseChartView.h
//  UPMarket2
//
//  Created by sammy<PERSON> on 2020/2/9.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2SDIBaseView.h"
#import "UPMarket2SDIStockIndexHost.h"
#import "UPMarket2StockView.h"
#import "UPMarket2ChartToolView.h"
#import <UPMarketIndex/UPMarketIndexDataModel.h>

NS_ASSUME_NONNULL_BEGIN

/*
 个股指标图基类, 包含指标图跟ContentController的一些交互, 比如点击、长按、叠加、跳转指标设置等
*/
@interface UPMarket2SDIBaseChartView : UPMarket2SDIBaseView <UPMarket2SDIStockIndexHostDelegate, UPMarket2StockViewDelegate,UPMarket2ChartToolViewDelegate>

@property (nonatomic, copy) void (^indexDeallocBlock)(NSInteger indexId);


@property (nonatomic, copy) void(^clickBlock)(UPMarket2ChartViewEventTag tag , _Nullable id object);

@property (nonatomic, copy) void(^userDidSelectTemplateBlock)(void); // 用户手动选择模板的回调

@property (nonatomic, copy) void(^longPressBlock)(_Nullable id data);

@property (nonatomic, copy) void(^landscapeBlock)(void);

@property (nonatomic, copy) void(^xLabelClickBlock)(BOOL isKline, id data);

@property (nonatomic, copy) void(^crossStateChangeBlock)(UPMarketCrossLineState crossLineState);

@property (nonatomic, copy) void(^regionChangeBlock)(NSArray<UPMarketUIKlineModel *> *data);

@property (nonatomic, copy) void(^clickQXModelBlock)(UPMarketUIKlineModel *klineModel, UPHqQXData *qxData);

@property (nonatomic, copy) void(^completionBlock)(void);

/// 当当前stock视图需要加载更多的时候的回调
@property (nonatomic, copy) void (^loadMoreBlock)(NSInteger startDate, UPMarketIndexId indexId);

/// 实时获取当前分时数量(擒龙环境和普通要隔离 不能共用)
@property (nonatomic, copy) NSInteger (^getMinorCountBlock)(BOOL isKline);

@property (nonatomic, copy) void (^didSelectedIndexModel)(id model);

@property (nonatomic, assign) BOOL isKline;

@property (nonatomic, assign) BOOL isLandscape;

@property (nonatomic, assign) BOOL isAppeared;

/// 自定义指标
@property (nonatomic, strong) UPMarket2SDICustomConfig *customConfig;

@property (nonatomic, assign, readonly) UPMarketCrossLineState crossLineState;

@property (nonatomic, assign) BOOL canShowLongClickMaskView,canShowAfterTradeKCBLabel,supportUpdateWhenLongPress;

@property (nonatomic, strong) UPMarket2SDIStockIndexHost *indexHost;

@property (nonatomic, strong) UPHqStockHq * _Nullable maskSelectStockHq;

// 主副图视图, index 0为主图
@property (nonatomic, strong) NSMutableArray<UPMarket2StockView *> *stockViews;

@property (nonatomic, assign) BOOL isHistoryMinute;

// K线缩放比例
@property (assign, nonatomic) CGFloat displayScale;

@property (nonatomic, strong) UPMarket2ChartToolView *chartToolView;

@property (nonatomic, assign) NSInteger overlayIndexId;

@property (nonatomic, strong) NSString *templateName; // 指定模板名称

// 数据
- (void)updateIndexDataModel:(UPMarketIndexDataModel *)indexDataModel isOverlay:(BOOL)isOverlay;

- (id)getCurrentMajorData:(UPMarketUIBaseStockLayer *)stockLayer;

- (void)configStockView:(UPMarket2StockView *)majorView minorViews:(NSArray *)minorViews;

- (void)setMajorView:(NSInteger)indexID;

- (void)setMinorView:(NSInteger)indexID position:(NSInteger)position;

- (void)reloadIndexUpdateBlock;

- (void)exitCrossLineMode;

- (void)maskStock:(UPHqStockHq *)stockHq;

- (void)cancelMaskViewClick;

- (void)updateSettingChange;

- (void)updateWhileStockViewsChange;

- (void)clearData;

- (void)moveCrossLine:(UPMarketUIMoveCrossLineType)type;

- (void)endRegion;

- (void)updateIndicator:(UPMarket2StockView *)stockview;

- (void)superScrolling;

@end

NS_ASSUME_NONNULL_END
