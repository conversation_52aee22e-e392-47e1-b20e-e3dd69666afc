//
//  UPMarket2SDIController.m
//  UPMarket2
//
//  Created by sammy<PERSON> on 2020/2/7.
//  Copyright © 2020 UpChina. All rights reserved.
//

//#import <UPUserSDK/UPUserSDK.h>
#import "UPMarket2LandscapeUtil.h"
#import "UPMarket2SDIController.h"
//#import "UPMarket2SDIOptionalListView.h"
//#import <UPUserSDK/UPOptionalManager.h>
#import "FMUPDataTool.h"


static CGFloat const UPLandscapeStockListViewWidth = 145.0;
static NSString *const UPLandscapeStockListHiddenStatusKey = @"kUPLandscapeStockListHiddenStatusKey";

@interface UPMarket2SDIController () <
    UIPageViewControllerDelegate,
    UIPageViewControllerDataSource,
    UPMarket2SDIBaseContentControllerDelegate,
    UPMarket2SDILandscapeStockListViewDelegate>

@property(nonatomic, strong) UPMarket2SDIBaseContentController * cachedContentController; // 用于复用
@property(nonatomic, strong) UPMarket2SDIBaseContentController * preContentController; // 前一个VC
@property(nonatomic, strong) UPMarket2SDIBaseContentController * afterContentController; // 后一个VC

@property(nonatomic, strong) NSMutableDictionary<NSNumber *, NSData *> * cachedContentControllerState; // 用于保存状态
@property(nonatomic, strong) NSMutableDictionary<NSNumber *, UPHqStockHq *> * cachedStockHq; // 用于保存最新的stockHq
@property(nonatomic, strong) NSMutableDictionary<NSNumber *, UPMarketUIBaseModel *> *cacheStockModel; //用于保存从码表查询的stockModel

@property(nonatomic, strong) UPMarketMonitor * marketMonitor;
@property (assign, nonatomic) BOOL stockListShow;
@property (strong, nonatomic) MASConstraint *widthConstraint;

@end

@implementation UPMarket2SDIController

// MARK: Override

- (instancetype)initWithNibName:(NSString *)nibNameOrNil bundle:(NSBundle *)nibBundleOrNil {
    self = [super initWithNibName:nibNameOrNil bundle:nibBundleOrNil];
    if(self) {
        self.hidesBottomBarWhenPushed = YES;

        _cachedContentControllerState = [NSMutableDictionary dictionaryWithCapacity:4];
        _cachedStockHq = [NSMutableDictionary dictionaryWithCapacity:4];
        _cacheStockModel = [NSMutableDictionary dictionaryWithCapacity:4];
        _stockListShow = ![self getSavedStockListHiddenStatus];

        ///TODO: 监听用户登录状态
        // 用户状态监听
//        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(l2PermissionDidUpdate)
//                                                     name:(UPNotifyUserDidLogin) object:nil];
//        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(l2PermissionDidUpdate)
//                                                     name:(UPNotifyUserDidLogout) object:nil];
//        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(l2PermissionDidUpdate)
//                                                     name:(UPNotifyUserInfoUpdated) object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(l2PermissionDidUpdate)
                                                     name:(UPMarketNotifLevel2KickedOff) object:nil];
        
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(l2PermissionDidUpdate) name:UPMarketNotifLevel2LoginSuccess object:nil];

        
        // 上证云L2行情离线
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(l2PermissionDidUpdate)
                                                     name:UPMarketNotifSHYL2Online
                                                   object:nil];
        // 上证云L2行情恢复
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(l2PermissionDidUpdate)
                                                     name:UPMarketNotifSHYL2Offline
                                                   object:nil];
    }
    return self;
}

-(void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

-(void)viewDidLoad {
    [super viewDidLoad];
    [self addChildViewController:self.pageController];
    [self.view addSubview:self.contentView];
    [self.view addSubview:self.stockListView];
    [self.contentView addSubview:self.pageController.view];
    
    CGFloat width;
    
    if (![UPMarket2LandscapeUtil isLandscape] || ![self selectedContentController].isLandscape || (self.models.count <= 1 && !self.isFromOpitonalList)) {
        width = 0.0;
    } else {
        width = self.stockListShow * UPLandscapeStockListViewWidth;
    }
        
    [self.stockListView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.up_safeAreaLayoutGuideTop);
        make.bottom.equalTo(self.up_safeAreaLayoutGuideBottom);
        make.left.equalTo(self.up_safeAreaLayoutGuideLeft);
        self.widthConstraint = make.width.mas_equalTo(width);
    }];
    
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view);
        make.bottom.equalTo(self.up_safeAreaLayoutGuideBottom);
        make.right.equalTo(self.up_safeAreaLayoutGuideRight);
        make.left.equalTo(self.stockListView.mas_right);
    }];
    
    [self.pageController.view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView);
    }];
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    
    //补丁:通过调用生命周期接口来停止所有页面的请求
    if (self.preContentController) {
        [self.preContentController viewDidDisappear:animated];
    }
    if (self.afterContentController) {
        [self.afterContentController viewDidDisappear:animated];
    }
}

- (void)dismiss {
    [super dismiss];
}
// MARK: Public

-(void)setSelectedIndex:(NSInteger)index animated:(BOOL)animated {
    UPMarket2SDIBaseContentController * vc = [self contentControllerWithIndex:index];
    
    if (self.trend.length) {
        vc.trend = self.trend;
    }

    if (self.tab) {
        vc.tab = self.tab;
    }
    
    vc.mainIndex = self.mainIndex;
    vc.viceIndex = self.viceIndex;
    vc.templateName = self.templateName;
    
    if(vc) {
        [self updateContentStockList:vc];
        
        [self.stockListView updateSelectedIndex:index];
        
        UIPageViewControllerNavigationDirection direction = UIPageViewControllerNavigationDirectionForward;

        if(self.selectedIndex > index) {
            direction = UIPageViewControllerNavigationDirectionReverse;
        }

        // TODO: setViewControllers会清除UIPageViewController缓存的前后页面, 所以设置之后, 左右滑动的时候页面会重新加载
        [self.pageController setViewControllers:@[vc] direction:direction animated:animated completion:nil];
    }
}

-(void)goPrev:(BOOL)animated {
    [self setSelectedIndex:self.selectedIndex - 1 animated:animated];
}

-(void)goNext:(BOOL)animated {
    [self setSelectedIndex:self.selectedIndex + 1 animated:animated];
}

- (void)updateStockListView:(BOOL)hidden {
    
    UPMarket2SDIBaseContentController *currentVC = [self selectedContentController];
    
    self.stockListShow = !hidden;
    
    [UIView animateWithDuration:0.3 animations:^{
        [self.widthConstraint setOffset:self.stockListShow * UPLandscapeStockListViewWidth];
        [self.widthConstraint install];
        
        if(currentVC) {
            [currentVC notifyStockListHidden:!hidden];
        }
        
        [self.view layoutIfNeeded];
    } completion:^(BOOL finished) {
        self.stockListView.hidden = hidden;
        if (self.stockListView.hidden) {
            [self.stockListView stop];
        } else {
            [self.stockListView start];
        }
    }];
    
    if (!hidden && currentVC) {
        // 打开列表时关闭区间统计
        [currentVC hideRegion];
    }
}

-(void)prepareContentController:(UPMarket2SDIBaseContentController *)contentController {}
-(void)contentControllerWillAppear:(UPMarket2SDIBaseContentController *)contentController {}
-(void)contentControllerDidAppear:(UPMarket2SDIBaseContentController *)contentController {}
-(void)contentControllerWillDisappear:(UPMarket2SDIBaseContentController *)contentController {}
-(void)contentControllerDidDisappear:(UPMarket2SDIBaseContentController *)contentController {}

-(void)stockHqDidUpdate:(UPHqStockHq *)stockHq {}

// MARK: Getter & Setter

- (void)setSelectedGroupName:(NSString *)selectedGroupName {
    _selectedGroupName = selectedGroupName;
    if (IsValidateString(selectedGroupName)) {
        [self.stockListView updateTitle:selectedGroupName showAccessory:YES];
    } else {
        [self.stockListView updateTitle:@"收起列表" showAccessory:NO];
    }
}

- (void)setModels:(NSArray<UPMarket2SDIControllerModel *> *)models {
    
    BOOL needsUpdateCurrentIndex = NO;
    if (IsValidateArray(_models)) {
        // 此时是在切换自选列表
        needsUpdateCurrentIndex = YES;
    }
    _models = models;
    
    if (needsUpdateCurrentIndex) {
        [self clearCache];
        [self setSelectedIndex:0 animated:NO];
    }
    
    self.stockListView.models = models;
}

-(NSInteger)selectedIndex {
    UPMarket2SDIBaseContentController * vc = [self selectedContentController];
    
    if(vc) {
        return vc.index;
    }

    return -1;
}

-(UPMarket2SDIPageController *)pageController {
    if(!_pageController) {
        _pageController = [[UPMarket2SDIPageController alloc] initWithTransitionStyle:UIPageViewControllerTransitionStyleScroll navigationOrientation:UIPageViewControllerNavigationOrientationHorizontal
                                                                options:nil];
        _pageController.doubleSided = YES;
        _pageController.delegate = self;
        _pageController.dataSource = self;
    }
    
    return _pageController;
}

- (UPMarketMonitor *)marketMonitor {
    if(!_marketMonitor) {
        _marketMonitor = [[UPMarketMonitor alloc] init];
    }

    return _marketMonitor;
}

- (UPMarket2SDILandscapeStockListView *)stockListView {
    if (!_stockListView) {
        _stockListView = [UPMarket2SDILandscapeStockListView new];
        _stockListView.delegate = self;
    }
    return _stockListView;
}

- (UIView *)contentView {
    if (!_contentView) {
        _contentView = [UIView new];
        _contentView.backgroundColor = [UIColor greenColor];
    }
    return _contentView;
}

// MARK: UIPageViewControllerDelegate

- (void)pageViewController:(UIPageViewController *)pageViewController willTransitionToViewControllers:(NSArray<UIViewController *> *)pendingViewControllers {
    [self updateContentStockList:(UPMarket2SDIBaseContentController *)pendingViewControllers.firstObject];
}

- (void)pageViewController:(UIPageViewController *)pageViewController didFinishAnimating:(BOOL)finished previousViewControllers:(NSArray<UIViewController *> *)previousViewControllers transitionCompleted:(BOOL)completed {
}

- (UIInterfaceOrientationMask)pageViewControllerSupportedInterfaceOrientations:(UIPageViewController *)pageViewController {
    return UIInterfaceOrientationMaskPortrait | UIInterfaceOrientationMaskLandscapeRight;
}

- (UIInterfaceOrientation)pageViewControllerPreferredInterfaceOrientationForPresentation:(UIPageViewController *)pageViewController {
    return UIInterfaceOrientationPortrait;
}

// MARK: UIPageViewControllerDataSource

- (nullable UIViewController *)pageViewController:(UIPageViewController *)pageViewController viewControllerBeforeViewController:(UIViewController *)viewController {

    if([viewController isKindOfClass:UPMarket2SDIBaseContentController.class]) {
        UPMarket2SDIBaseContentController * contentVC = (UPMarket2SDIBaseContentController *)viewController;
        NSInteger beforeIndex = contentVC.index - 1;
        self.preContentController =  [self contentControllerWithIndex:beforeIndex];
        return self.preContentController;
    }
    
    return nil;
}

- (nullable UIViewController *)pageViewController:(UIPageViewController *)pageViewController viewControllerAfterViewController:(UIViewController *)viewController {

    if([viewController isKindOfClass:UPMarket2SDIBaseContentController.class]) {
        UPMarket2SDIBaseContentController * contentVC = (UPMarket2SDIBaseContentController *)viewController;
        NSInteger afterIndex = contentVC.index + 1;
        self.afterContentController = [self contentControllerWithIndex:afterIndex];
        return self.afterContentController;
    }
    
    return nil;
}

// MARK: UPMarket2SDIBaseContentControllerDelegate

-(void)sdiContentControllerDidAttach:(UPMarket2SDIBaseContentController *)sdiContentController {
    NSInteger index = sdiContentController.index;
    
    if(index >= 0 && index < self.models.count) {
        NSData * stateData = [self.cachedContentControllerState objectForKey:@(index)];

        // 还原数据
        if(stateData) {
            NSKeyedUnarchiver * unarchiver;

            if (@available(iOS 11.0, *)) {
                unarchiver = [[NSKeyedUnarchiver alloc] initForReadingFromData:stateData error:nil];
            } else {
                unarchiver = [[NSKeyedUnarchiver alloc] initForReadingWithData:stateData];
            }

            [sdiContentController restoreContentViewState:unarchiver];

            [unarchiver finishDecoding];
        }

        // 必须在restoringContentState之后调用
        if(sdiContentController.viewLoaded) {
            UPHqStockHq *stockHq = nil;
            
            if (self.stockHqArray && index < self.stockHqArray.count && [self.stockHqArray[index] isKindOfClass:UPHqStockHq.class]) {
                stockHq = self.stockHqArray[index];
            }
            
            [sdiContentController contentViewDidReuse:stockHq];
        }
        [self updateContentStockList:sdiContentController];
    }
}

-(void)sdiContentControllerDidDetach:(UPMarket2SDIBaseContentController *)sdiContentController {
    NSInteger index = sdiContentController.index;

    if(index >= 0 && index < self.models.count) {
        // 保存数据
        if(sdiContentController.isContentViewDidAppearOnce) {
            NSData * stateData = nil;
            NSKeyedArchiver * archiver;

            if (@available(iOS 10.0, *)) {
                if (@available(iOS 11.0, *)) {
                    archiver = [[NSKeyedArchiver alloc] initRequiringSecureCoding:NO];
                } else {
                    archiver = [[NSKeyedArchiver alloc] init];
                }
            } else {
                NSMutableData * data = [NSMutableData dataWithCapacity:64];
                archiver = [[NSKeyedArchiver alloc] initForWritingWithMutableData:data];
                stateData = data;
            }

            archiver.outputFormat = NSPropertyListBinaryFormat_v1_0;

            [sdiContentController saveContentViewState:archiver];

            [archiver finishEncoding];

            if (@available(iOS 10.0, *)) {
                stateData = archiver.encodedData;
            }

            if(stateData) {
                [self.cachedContentControllerState setObject:stateData forKey:@(index)];
            }
        }

        self.cachedContentController = sdiContentController;
    }
}

-(void)sdiContentControllerWillAppear:(UPMarket2SDIBaseContentController *)sdiContentController {
    NSInteger index = sdiContentController.index;

    UPHqStockHq * stockHq = [self.cachedStockHq objectForKey:@(index)];

    if(stockHq) {
        sdiContentController.stockHq = stockHq;
    }
    
    [self contentControllerWillAppear:sdiContentController];
}

-(void)sdiContentControllerDidAppear:(UPMarket2SDIBaseContentController *)sdiContentController {
    [self refreshDataWithContentController:sdiContentController];
    [self contentControllerDidAppear:sdiContentController];
    [self.stockListView updateSelectedIndex:self.selectedIndex];
}

-(void)sdiContentControllerWillDisappear:(UPMarket2SDIBaseContentController *)sdiContentController {
    [self contentControllerWillDisappear:sdiContentController];
}

-(void)sdiContentControllerDidDisappear:(UPMarket2SDIBaseContentController *)sdiContentController {
    [self.marketMonitor stopMonitorWithTag:sdiContentController.index];

    [self contentControllerDidDisappear:sdiContentController];
}

-(void)sdiContentControllerNeedGoPrev:(UPMarket2SDIBaseContentController *)sdiContentController {
    [self goPrev:YES];
}

-(void)sdiContentControllerNeedGoNext:(UPMarket2SDIBaseContentController *)sdiContentController {
    [self goNext:YES];
}

- (void)sdiContentController:(UPMarket2SDIBaseContentController *)sdiContentController updateStockListView:(BOOL)hidden {
    [self updateStockListView:hidden];
    [self saveStockListHiddenStatus:hidden];
}

-(void)sdiContentControllerNeedEnterLandscape:(UPMarket2SDIBaseContentController *)sdiContentController {
    [UPMarket2LandscapeUtil enterLandscapeWithTarget:self];
}

-(void)sdiContentControllerNeedExit:(UPMarket2SDIBaseContentController *)sdiContentController {
    [UPMarket2LandscapeUtil exitLandscapeWithTarget:self];
}

- (void)sdiContentControllerNeedRefreshData:(UPMarket2SDIBaseContentController *)sdiContentController {
    [self refreshDataWithContentController:sdiContentController];
}

-(void)sdiContentControllerEditOptionalGroup:(UPMarket2SDIBaseContentController *)sdiContentController {
}

-(void)sdiContentControllerUserDidSelectTemplate:(UPMarket2SDIBaseContentController *)sdiContentController {
    // 用户手动选择模板后，清空外部传入的templateName，避免后续股票切换时被覆盖
    self.templateName = nil;
}

// MARK: - UPMarket2SDILandscapeStockListViewDelegate

- (void)listViewTitleClicked:(UPMarket2SDILandscapeStockListView *)listView {
    if (self.isFromOpitonalList) {
        // 弹出自选分组列表popView
//        [self showOptionalGroupPopupView];
    }
}

- (void)listViewListButtonClicked:(UPMarket2SDILandscapeStockListView *)listView {
    [self updateStockListView:YES];
    [self saveStockListHiddenStatus:YES];
}

- (void)listView:(UPMarket2SDILandscapeStockListView *)listView didClickAtIndex:(NSInteger)index {
    [self setSelectedIndex:index animated:NO];
}

// MARK: Private

- (void)updateContentStockList:(UPMarket2SDIBaseContentController *)vc {
    if (!vc) return;
    [vc setShouldShowStockList:(self.models.count > 1 || self.isFromOpitonalList)];
    if (!vc.isLandscape) {
        [vc notifyStockListHidden:YES];
    } else{
        [vc notifyStockListHidden:self.stockListShow];
    }
}

- (BOOL)getSavedStockListHiddenStatus {
    id obj = [[NSUserDefaults standardUserDefaults] valueForKey:UPLandscapeStockListHiddenStatusKey];
    if (IsValidateNumber(obj)) {
        return [obj boolValue];
    }
    // 第一次安装默认隐藏
    return YES;
}

- (void)saveStockListHiddenStatus:(BOOL)hidden {
    [[NSUserDefaults standardUserDefaults] setValue:@(hidden) forKey:UPLandscapeStockListHiddenStatusKey];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

- (void)clearCache {
    self.cachedContentControllerState = [NSMutableDictionary dictionaryWithCapacity:4];
    self.cachedStockHq = [NSMutableDictionary dictionaryWithCapacity:4];
    self.cacheStockModel = [NSMutableDictionary dictionaryWithCapacity:4];
}

/**
 - (NSArray<UPMarket2SDIControllerModel *> *)transferOptionalList:(NSArray<UPOptionalModel *> *)optionalList {
     NSMutableArray<UPMarket2SDIControllerModel *> *array = [NSMutableArray arrayWithCapacity:10];
     for (NSInteger i = 0; i < optionalList.count; i++) {
         UPOptionalModel *optionalModel = optionalList[i];
         UPMarket2SDIControllerModel *vcModel = [UPMarket2SDIControllerModel new];
         vcModel.stockSetCode = optionalModel.setCode;
         vcModel.stockCode = optionalModel.code;
         [array addObject:vcModel];
     }
     return [array copy];
 }

 - (void)showOptionalGroupPopupView {
     UPPopupView *popView = [[UPPopupView alloc] init];

     UPMarket2SDIOptionalListView *content = [[UPMarket2SDIOptionalListView alloc] init];
     
     __weak typeof(self) weakSelf = self;
     __weak typeof(popView) weakPop = popView;
     content.clickBlock = ^(UPOptionalGroupModel * _Nonnull groupModel) {
         NSArray *optionalList = [UPOptionalStockManager getCurrentOptionalDataByGroupId:groupModel.groupId];
         if (IsValidateArray(optionalList)) {
             weakSelf.models = [weakSelf transferOptionalList:optionalList];
             weakSelf.selectedGroupName = groupModel.groupName;
         } else {
             [UPToastView show:@"所选分组暂无自选"];
         }
         
         [weakPop hide];
     };

     content.groupArray = [[UPOptionalGroupManager sharedInstance] getCurrentOptionalAllGroupData];
     popView.contentView = content;
     
     CGFloat height = content.groupArray.count > 3 ? content.rowHeight * 3 : content.rowHeight * content.groupArray.count;
     popView.contentSize = CGSizeMake(95, height);
     
     [popView showInView:self.view atPosition:CGPointMake(37, 42)];
 }
 */

-(UPMarket2SDIBaseContentController *)contentControllerWithIndex:(NSInteger)index {
    UPMarket2SDIBaseContentController * vc = nil;

    if(index >= 0 && index < self.models.count) {
        if(self.cachedContentController) {
            vc = self.cachedContentController;
            self.cachedContentController = nil; // 复用之后清空, 避免重复复用
        } else {
            // 创建子类
            Class clazz = NSClassFromString(@"UPMarket2SDIContentController");
            vc = [[clazz alloc] init];
            vc.delegate = self;
        }
        vc.customConfig = self.customConfig;

        [self prepareContentController:vc];
        
        vc.index = index;
        [self updateStockModelWithContentController:vc];
        
        if (self.stockHqArray && index < self.stockHqArray.count) {
            vc.stockHq = self.stockHqArray[index];
        }
    }

    return vc;
}

- (void)updateStockModelWithContentController:(UPMarket2SDIBaseContentController *)sdiContentController {
    NSInteger index = sdiContentController.index;
    UPMarketUIBaseModel *stockModel = [self.cacheStockModel objectForKey:@(index)];
    if (!stockModel) {
        if (index >= 0 && index < self.models.count) {
            stockModel = [UPMarketUIBaseModel modelWithSetCode:self.models[index].stockSetCode code:self.models[index].stockCode];
            if (self.models[index].category == FMMarketStockCategory_BLOCK) {
                [stockModel setValue:@(FMMarketStockCategory_BLOCK) forKey:@"stockCategory"];
            }
            
            [self.cacheStockModel setObject:stockModel forKey:@(index)];
        }
    }
    sdiContentController.stockModel = stockModel;
}

-(void)refreshDataWithContentController:(UPMarket2SDIBaseContentController *)sdiContentController {
    
    if (UPTAFNetworkNotReachable) {
        [UPToastView show:@"网络连接失败"];
    }
    
    UPMarketUIBaseModel * model = sdiContentController.stockModel;

    UPMarketStockHqReq * req;
    if (model.stockCategory == FMMarketStockCategory_BLOCK) {
        req = [[UPMarketStockHqReq alloc] initWithSetCode:1 code:@"000001"];
    } else {
       req = [[UPMarketStockHqReq alloc] initWithSetCode:model.stockSetCode code:model.stockCode];
    }
    req.wantNum = 1;

    sdiContentController.isStockL2 = [self hasL2Permission];
    
    [self.marketMonitor startMonitorStockHq:req tag:sdiContentController.index completionHandler:^(UPMarketStockHqRsp *rsp, NSError *error) {
        UPHqStockHq *hq = rsp.dataArray.firstObject;
        if(error) {
            [sdiContentController endRefreshData];
            NSLog(@"[SDIController] MonitorStockHq failed: %@", error);
        } else if(rsp.dataArray.count > 0) {
            if (model.stockCategory == FMMarketStockCategory_BLOCK) {
                NSLog(@"筛选---自定义板块数据");
                [FMUPDataTool requestBlockDetailHqWithStockCode:model.stockCode Success:^(NSDictionary *dic) {
                    FMMarketBlockDetailHqModel *model = [FMMarketBlockDetailHqModel modelWithDictionary:dic[@"data"]];
                    UPHqStockHq * stockHq = [UPHqStockHq new];
                    stockHq.code = model.stockCode;
                    stockHq.name = model.stockName;
                    stockHq.nowPrice = model.lastPx;
                    stockHq.changeValue = model.pxChange;
                    stockHq.changeRatio = model.pxChangeRate / 100.0;
                    stockHq.precise = 2;
                    stockHq.highPrice = model.highPx;
                    stockHq.lowPrice = model.lowPx;
                    stockHq.openPrice = model.openPx;
                    stockHq.yClosePrice = model.preclosePx;
                    stockHq.turnoverRate = model.turnoverRatio / 100.0;
                    stockHq.dealAmount = model.businessBalance;
                    stockHq.dealVol = model.businessAmount;
                    stockHq.swingRatio = model.amplitude / 100.0;
                    stockHq.circulationMarketValue = model.circulationValue;
                    stockHq.totalMarketValue = model.marketValue;
                    stockHq.riseCount = model.riseCount;
                    stockHq.equalCount = model.flatCount;
                    stockHq.fallCount = model.fallCount;
                    stockHq.category = FMMarketStockCategory_BLOCK;
                    stockHq.tradeStatus = [FMUPDataTool tradeStatusWithOurString:model.tradeStatus];
                    stockHq.tradeDate = model.marketDate;
                    stockHq.tradeTime = model.ex_tradeTime;
                    stockHq.volRatio = (model.businessAmount / model.ba5avg);
                    
                    [self stockHqDidUpdate:stockHq];
                    sdiContentController.stockHq = stockHq;
                    [self.cachedStockHq setObject:stockHq forKey:@(sdiContentController.index)];
                } failure:^{
                }];
            } else {
//                NSLog(@"筛选--不是自定义板块");
                UPHqStockHq * stockHq = rsp.dataArray[0];

                [self stockHqDidUpdate:stockHq];
                sdiContentController.stockHq = stockHq;
                [self.cachedStockHq setObject:stockHq forKey:@(sdiContentController.index)];
            }
        }
    }];
    
    [sdiContentController refreshCurrentChartView];
}

-(UPMarket2SDIBaseContentController *)selectedContentController {
    if(self.pageController.viewControllers.count > 0) {
        return self.pageController.viewControllers.firstObject;
    }

    return nil;
}

// MARK: L2

-(void)l2PermissionDidUpdate {
    dispatch_async(dispatch_get_main_queue(), ^{
        UPMarket2SDIBaseContentController * sdiContentController = [self selectedContentController];

        if(sdiContentController) {
            sdiContentController.isStockL2 = [self hasL2Permission];
        }
    });
}

-(BOOL)hasL2Permission {
    return UPMarket2Preference.isL2Online;
}

@end
