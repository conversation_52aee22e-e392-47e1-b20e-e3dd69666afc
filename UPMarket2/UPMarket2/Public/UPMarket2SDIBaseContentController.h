//
//  UPMarket2SDIBaseContentController.h
//  UPMarket2
//
//  Created by sammy<PERSON> on 2020/2/14.
//  Copyright © 2020 UpChina. All rights reserved.
//  UPMarket2SDIBaseContentController 是一个基础控制器类，提供了与父控制器交互的通用逻辑和视图管理方法。子类可以重写这些方法，实现具体的功能需求。该类主要用于显示和管理个股内容，并处理个股的横竖屏切换、数据更新和视图复用等功能。

#import "UPMarket2BaseViewController.h"
#import "UPMarket2SDIControllerModel.h"
#import "UPMarket2SDICustomConfig.h"

NS_ASSUME_NONNULL_BEGIN

@class UPMarket2SDIBaseContentController;

/*
 个股内容Controller跟父Controller的交互
 */
@protocol UPMarket2SDIBaseContentControllerDelegate <NSObject>
@optional

-(void)sdiContentControllerDidAttach:(UPMarket2SDIBaseContentController *)sdiContentController;

-(void)sdiContentControllerDidDetach:(UPMarket2SDIBaseContentController *)sdiContentController;

-(void)sdiContentControllerWillAppear:(UPMarket2SDIBaseContentController *)sdiContentController;

-(void)sdiContentControllerDidAppear:(UPMarket2SDIBaseContentController *)sdiContentController;

-(void)sdiContentControllerWillDisappear:(UPMarket2SDIBaseContentController *)sdiContentController;

-(void)sdiContentControllerDidDisappear:(UPMarket2SDIBaseContentController *)sdiContentController;

-(void)sdiContentControllerNeedGoPrev:(UPMarket2SDIBaseContentController *)sdiContentController;

-(void)sdiContentControllerNeedGoNext:(UPMarket2SDIBaseContentController *)sdiContentController;

- (void)sdiContentController:(UPMarket2SDIBaseContentController *)sdiContentController updateStockListView:(BOOL)hidden;

-(void)sdiContentControllerNeedEnterLandscape:(UPMarket2SDIBaseContentController *)sdiContentController;

-(void)sdiContentControllerNeedExit:(UPMarket2SDIBaseContentController *)sdiContentController;

-(void)sdiContentControllerNeedRefreshData:(UPMarket2SDIBaseContentController *)sdiContentController;

-(void)sdiContentControllerEditOptionalGroup:(UPMarket2SDIBaseContentController *)sdiContentController;

-(void)sdiContentControllerUserDidSelectTemplate:(UPMarket2SDIBaseContentController *)sdiContentController;

@end

/*
 个股内容Controller基类, 包含跟父Controller交互的通用逻辑
 一次性初始化的变量使用property定义, 会动态更新的变量使用method回调
*/
@interface UPMarket2SDIBaseContentController : UPMarket2BaseViewController

@property(nonatomic, weak) id<UPMarket2SDIBaseContentControllerDelegate> delegate;

@property(nonatomic, assign) NSUInteger index; // 在父Controller中的位置, 外部设置, 内部只读, 一次性设置, 用于初始化UI
@property(nonatomic, assign) BOOL isLandscape; // 是否横屏, 外部设置, 内部只读, 一次性设置, 用于初始化UI

@property(nonatomic, strong, readonly) UPViewController * outerContainerViewController; // 外层父ViewController

@property(nonatomic, assign) BOOL isContentViewDidAppearOnce; // 是否DidAppear过, 用于判断是否保存状态

@property(nonatomic, strong) UPMarketUIBaseModel * stockModel; // 外部一次性设置, 用于初始化UI

@property(nonatomic, strong) UPHqStockHq * stockHq; // 最新的stockHq数据, 动态更新, 调用频繁, 禁止用于初始化UI, 禁止重写setter
@property(nonatomic, assign) BOOL isStockL2; // 是否L2, 动态更新, 禁止重写setter

@property (nonatomic, strong) NSString *tab, *trend;  //tab:个股当前的tab  trend:minute表示分时 ; day 表示 K 线
@property (nonatomic, strong) NSString *mainIndex, *viceIndex; // 指定主附图索引
@property (nonatomic, strong) NSString *templateName; // 指定模板名称

@property (nonatomic, copy, readonly) NSArray *shareContentViews;

@property(nonatomic, copy) NSArray<UPMarket2SDIControllerModel *> * models;

@property (assign, nonatomic) BOOL shouldShowStockList;



/// 自定义指标
@property (nonatomic, strong) UPMarket2SDICustomConfig *customConfig;

-(void)contentViewStockHqDidUpdate:(UPHqStockHq *)stockHq; // stockHq数据, 动态更新, 调用频繁, 禁止用于初始化UI
-(void)contentViewStockL2DidUpdate:(BOOL)isStockL2; // 是否为L2, 动态更新

-(void)contentViewDidReuse:(UPHqStockHq *)stockHq; // 被复用, 需要在这个里面做一些除了添加View以外的初始化工作, 比如重新设置或者清除之前UI的数据等

-(void)saveContentViewState:(NSCoder *)coder; // 保存状态, 比如滚动位置, 选中状态等
-(void)restoreContentViewState:(NSCoder *)coder; // 还原状态, 比如滚动位置, 选中状态等

-(void)setupViews; // 添加View, 设置属性等, 禁止设置View布局
-(void)setupViewsForLandscape; // 添加View, 设置属性等, 禁止设置View布局

-(void)layoutViews; // 设置View布局, 禁止用于其他用途
-(void)layoutViewsForLandscape; // 设置View布局, 禁止用于其他用途

-(void)setupViewsContent; // 设置View内容, 数据等

-(void)goPrevContentController; // 上一页
-(void)goNextContentController; // 下一页
-(void)notifyStockListHidden:(BOOL)hidden; // 通知股票列表
-(void)hideRegion; // 关闭区间统计
-(void)enterLandscapeContentController; // 打开横屏
-(void)exitContentController; // 退出当前Controller
-(void)requestRefreshData; // 通知外层刷新数据
-(void)endRefreshData; // 结束刷新
-(void)editOptionalGroup; // 编辑自选分组

-(void)refreshCurrentChartView; //刷新当前绘制试图
@end

NS_ASSUME_NONNULL_END
