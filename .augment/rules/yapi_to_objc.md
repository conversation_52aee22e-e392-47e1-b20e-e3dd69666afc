---
type: "manual"
---

# YAPI to Objective-C 转换规范

## 项目基础信息

- **项目名称**: QCYZT
- **版权信息**: Cursor
- **作者**: Cursor

## 全局设置

### 模型生成设置

- **单文件模式**: 生成单个文件包含所有模型
- **YYModel支持**: 使用 YYModel 进行数据转换
- **模型基类**: NSObject
- **生成注释**: 启用
- **数据字段处理**: 只转换data字段下的数据为model，不生成最外层响应包装

### 接口方法生成设置

- **网络请求工具类**: HttpRequestTool对应的分类
- **生成注释**: 启用
- **生成API常量**: 启用

## 类型映射规则

### 基于Mock信息的映射

| Mock类型 | Objective-C类型 |
|----------|----------------|
| @float | CGFloat |
| @integer | NSInteger |
| @string | NSString * |
| @timestamp | long long |

### 基于YAPI类型的映射

| YAPI类型 | Objective-C类型 |
|----------|----------------|
| string | NSString * |
| number | CGFloat |
| integer | NSInteger |
| boolean | BOOL |
| object | NSDictionary * |
| array | NSArray * |

### 特殊字段名映射

- **id字段处理**: 
  - 类型: NSString *
  - 重命名: identifier (避免Objective-C关键字冲突)
- **is前缀字段**: 
  - 类型: BOOL
  - 模式: `^is[A-Z].*`

## 属性特性映射

| 类型 | 属性特性 |
|------|----------|
| NSString * | copy |
| NSArray * | strong |
| NSDictionary * | strong |
| NSNumber * | strong |
| NSObject * | strong |
| 其他类型 | assign |

## 命名规则

### 模型命名

- **模型名称**: 基于接口名称和路径智能生成
- **嵌套模型**: 使用 `{fieldName}Model` 格式

### 接口方法命名

- **方法名**: 基于接口名称和路径智能生成
- **API常量**: 使用 `kAPI_{intelligentName}` 格式

## 特殊处理规则

### 请求方法选择

#### 基于Content-Type

| Content-Type | 请求方法 |
|--------------|----------|
| application/json | postDataJsonInfoWithUrl |
| application/x-www-form-urlencoded | postDataInfoWithUrl |

#### 基于HTTP方法

| HTTP方法 | 请求方法 |
|----------|----------|
| GET | getDataInfoWithUrl |
| POST | postDataInfoWithUrl |
| PUT | putDataInfoWithUrl |
| DELETE | deleteDataInfoWithUrl |

### 数组泛型处理

- **使用泛型**: 启用
- **声明格式**: `NSArray<{itemClass} *> *`
- **容器映射方法**: modelContainerPropertyGenericClass

## 代码模板

### 模型头文件模板

```objective-c
//
//  {modelName}.h
//  {projectName}
//
//  Created by {author} on {date}
//  Copyright © {year} {copyright}. All rights reserved.
//

#import <Foundation/Foundation.h>

{modelInterfaces}
```

### 模型实现文件模板

```objective-c
//
//  {modelName}.m
//  {projectName}
//
//  Created by {author} on {date}
//  Copyright © {year} {copyright}. All rights reserved.
//

#import "{modelName}.h"

{modelImplementations}
```

### 模型接口声明模板

```objective-c
@interface {modelName} : {baseClass}

{properties}
@end
```

### 模型实现模板

```objective-c
@implementation {modelName}

{customMethods}

@end
```

### 接口方法模板（无参数）

```objective-c
/**
 * {apiName}
 * @param startBlock 请求开始回调
 * @param failBlock 请求失败回调  
 * @param success 请求成功回调
 */
+ (void){methodName}WithStart:(void (^)())startBlock
                   failure:(void (^)())failBlock
                   success:(requestSuccessBlock)success {
    NSString *url = {KDakaBaseUrl(apiConstant)};
    [{requestTool} {requestMethod}:url params:nil startBlock:startBlock failBlock:failBlock success:success];
}
```

### 接口方法模板（带参数）

```objective-c
/**
 * {apiName}
 * {paramComments}
 * @param startBlock 请求开始回调
 * @param failBlock 请求失败回调  
 * @param success 请求成功回调
 */
+ (void){methodName}With{paramNames}Start:(void (^)())startBlock
                                  failure:(void (^)())failBlock
                                  success:(requestSuccessBlock)success {
    NSString *url = {KDakaBaseUrl(apiConstant)};
    NSDictionary *params = @{
        {paramDictionary}
    };
    [{requestTool} {requestMethod}:url params:params startBlock:startBlock failBlock:failBlock success:success];
}
```

## 使用说明

1. **模型生成**: 根据YAPI接口文档自动生成对应的Objective-C模型类
2. **接口方法生成**: 自动生成网络请求方法，包含完整的参数处理和回调
3. **类型安全**: 通过类型映射确保数据类型的正确性
4. **命名规范**: 遵循Objective-C命名约定和项目规范
5. **代码复用**: 使用统一的模板确保代码风格一致性

## 注意事项

- 确保生成的代码符合项目的编码规范
- 特别注意Objective-C关键字的处理
- 数组类型需要正确设置泛型信息
- 网络请求方法的选择要根据实际API特性确定
