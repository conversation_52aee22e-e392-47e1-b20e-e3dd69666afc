# 语言使用规则

## 中文回复规则

### 基本原则
- 当用户使用中文提问时，优先使用中文回复
- 技术术语可以保留英文，但需要提供中文解释
- 代码注释应使用中文，便于团队理解
- 文档和规格说明应使用中文编写

### 回复格式
- 使用简体中文
- 保持专业但友好的语调
- 技术概念首次出现时提供中英文对照
- 代码示例中的变量名可使用英文，但注释用中文

### 特殊情况
- 错误信息和日志保持英文（便于调试）
- 第三方库的API调用保持原有命名
- Git提交信息建议使用英文（国际化考虑）

### 示例格式
```objective-c
// 用户登录验证
- (BOOL)validateUserLogin:(NSString *)username password:(NSString *)password {
    // 检查用户名是否为空
    if (!username || username.length == 0) {
        return NO;
    }
    
    // 验证密码强度
    return [self checkPasswordStrength:password];
}
```

## 技术文档规则

### 需求文档
- 使用中文描述功能需求
- 技术实现细节可中英文混合
- 用户界面相关内容必须使用中文

### 代码审查
- 代码审查意见使用中文
- 重要的架构决策文档使用中文
- 团队内部技术分享使用中文