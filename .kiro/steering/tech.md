# Technology Stack

## Platform & Language
- **Platform**: iOS (minimum deployment target: iOS 13.0)
- **Primary Language**: Objective-C with some Objective-C++ (.mm files)
- **Architecture**: MVC pattern with category-based extensions

## Build System
- **Dependency Management**: CocoaPods
- **Build Tool**: Xcode project with workspace configuration
- **Project Structure**: Modular architecture with local pods

## Key Frameworks & Libraries

### Core Dependencies
- **UI Framework**: UIKit with custom UI components
- **Networking**: Custom HTTP request tools built on NSURLSession
- **Database**: FMDB (SQLite wrapper)
- **Image Loading**: SDWebImage, YYImage
- **Auto Layout**: Masonry (Objective-C wrapper for Auto Layout)
- **JSON Parsing**: YYModel for model mapping
- **Refresh Control**: MJRefresh
- **Progress Indicators**: MBProgressHUD, SVProgressHUD

### Financial & Market Data
- **UP Series SDKs**: UPBase, UPBaseUI, UPMarketSDK, UPMarketUISDK, UPUserSDK
- **Market Data**: Custom TAF-based communication protocol
- **Real-time Data**: WebSocket connections for live market feeds

### Third-Party Integrations
- **Social Sharing**: WeChat SDK, QQ SDK, Weibo SDK
- **Payment**: Alipay SDK, WeChat Pay
- **Analytics**: Bugly for crash reporting
- **Face Recognition**: TencentCloudHuiyanSDKFace
- **Calendar**: FSCalendar
- **MQTT**: MQTTClient for real-time messaging

### Development Tools
- **Debug**: LookinServer (Debug builds only)
- **Storage**: MMKV for key-value storage
- **Keyboard Management**: IQKeyboardManager

## Common Commands

### Setup
```bash
# Install dependencies
pod install

# Update pods
pod update
```

### Build
```bash
# Open workspace (required for CocoaPods projects)
open QCYZT.xcworkspace

# Build from command line
xcodebuild -workspace QCYZT.xcworkspace -scheme QCYZT -configuration Debug
```

### Development
```bash
# Clean build folder
xcodebuild clean -workspace QCYZT.xcworkspace -scheme QCYZT

# Archive for distribution
xcodebuild archive -workspace QCYZT.xcworkspace -scheme QCYZT -archivePath build/QCYZT.xcarchive
```

## Configuration Notes
- Uses precompiled header (.pch) for common imports
- Custom macro definitions for UI, colors, and utilities
- Modular pod structure with local dependencies
- Build settings configured for warnings as errors in most targets
- Supports both Debug and Release configurations with different preprocessor definitions