# Project Structure

## Root Directory Organization

### Main Application (`QCYZT/`)
The core iOS application with modular organization:

#### App Entry (`AppEntry/`)
- `FMAppDelegate.mm` - Main application delegate
- `FMMainTabController.m` - Root tab bar controller
- `main.m` - Application entry point
- `LaunchScreen/` - Launch screen assets
- `TabBar/` - Custom tab bar implementation

#### Configuration (`Config/`)
- `QCYZT-Prefix.pch` - Precompiled header with common imports
- `ColorFontMacro.h` - Color and font definitions
- `UIMacro.h` - UI-related macros and utilities
- `ConstVariable.h/m` - Global constants
- `Info.plist` - App configuration and permissions

#### Feature Modules (`Classes/Sections/`)
Organized by functional areas:
- `UserCenter(我的)/` - User profile and account management
- `Home(首页)/` - Main dashboard and market overview
- `Stock(行情)/` - Stock market data and analysis
- `Community(社区)/` - Social features and discussions
- `Course(课程)/` - Educational content and live streaming
- `Message(消息)/` - Messaging and notifications

#### Utilities & Extensions (`Category/`)
- UI component extensions (UIView+, UIButton+, etc.)
- Foundation extensions (NSString+, NSDate+, etc.)
- Custom category implementations

#### Common Tools (`CommonTool/`)
- Network layer (`Http/`)
- Database management (`db/`)
- Payment integration (`PayCommonTool/`)
- Custom UI components (`CustomView/`)
- Analytics and statistics (`StatisticsHelper/`)

#### Resources (`Images.xcassets/`, `Resource/`)
- App icons and image assets organized by feature
- Localization files and other resources

### Local Pods
Modular dependencies managed as local CocoaPods:

#### Core Modules
- `UPCommon/` - Shared utilities and base classes
- `UPMarket2/` - Market data display components
- `UPMarketIndex/` - Market index calculations
- `UPNewsSDK/` - News and information services
- `UPSearch/` - Search functionality
- `CNTafReqCenter/` - TAF protocol communication
- `CNCommonUI/` - Shared UI components
- `CNMainBussiness/` - Core business logic

#### Third-Party Frameworks (`DependencyFrameworks/`)
- UP series SDKs (UPBase, UPMarketSDK, etc.)
- Proprietary frameworks for market data

### External Dependencies (`Pods/`)
Standard CocoaPods managed dependencies including UI libraries, networking tools, and third-party SDKs.

## Naming Conventions

### Files & Classes
- **Prefix**: `FM` for main app classes (e.g., `FMViewController`)
- **Categories**: `ClassName+CategoryName` format
- **Models**: Suffix with `Model` (e.g., `UserModel`)
- **Views**: Suffix with `View` or `Cell` for table/collection cells
- **Controllers**: Suffix with `ViewController`

### Directories
- Chinese names in parentheses for user-facing features
- English technical names for infrastructure
- Organized by feature/module rather than file type

## Architecture Patterns

### MVC Structure
- **Models**: Data objects and business logic
- **Views**: UI components and custom views  
- **Controllers**: View controllers managing user interaction

### Category-Based Extensions
Heavy use of Objective-C categories to extend system classes with project-specific functionality.

### Modular Design
- Local pods for major feature areas
- Shared utilities in common modules
- Clear separation between UI and business logic

## Key Integration Points
- Precompiled header imports all common dependencies
- Macro definitions centralize UI constants and utilities
- Shared base classes provide common functionality
- Protocol-based communication between modules